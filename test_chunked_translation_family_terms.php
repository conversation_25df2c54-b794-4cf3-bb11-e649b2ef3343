<?php
/**
 * Test chunked translation specifically for family term handling
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

echo "=== Testing Chunked Translation Family Terms ===\n\n";

try {
    $translationService = new TranslationService();
    $db = Database::getInstance();
    
    // Get family terms from name dictionary
    $familyNames = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['兄さん', '兄', '父', '母', 'お父さん', 'お母さん', 'お兄ちゃん', 'お兄さん']
    );
    
    // Create test content that simulates a chunk with family terms
    $testChunks = [
        // Chunk 1 - Early content (should be correct based on analysis)
        "「もう、兄さんは……ちょっと元侯爵様に挨拶に行くだけだからって言っていたのに、まさかそのまま任務に行ってしまうなんて」と、ティルティの不満点は、ソルドに置いていかれたというその一点だ。",
        
        // Chunk 2 - Middle content (problematic area based on analysis)  
        "彼女の母は貴族だったと聞いている。しかし、その父からの手紙が届いた。「家族になろう」という誘いだった。",
        
        // Chunk 3 - Later content (mixed results based on analysis)
        "「兄さんのことが大好きです。誰にも渡したくない……でも」と彼女は思った。"
    ];
    
    foreach ($testChunks as $i => $chunkContent) {
        echo "=== Testing Chunk " . ($i + 1) . " ===\n";
        echo "Content: {$chunkContent}\n";
        
        // Simulate chunk context
        $chunkContext = [
            'type' => 'chapter',
            'novel_id' => 7,
            'names' => $familyNames,
            'chunk_number' => $i + 1,
            'total_chunks' => count($testChunks),
            'is_first_chunk' => $i === 0,
            'is_last_chunk' => $i === count($testChunks) - 1
        ];
        
        $result = $translationService->translateText(
            $chunkContent,
            'en',
            'ja',
            $chunkContext
        );
        
        if ($result['success']) {
            $translation = $result['translated_text'];
            echo "Translation: {$translation}\n";
            echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
            echo "Provider used: " . ($result['provider_used'] ?? 'unknown') . "\n";
            echo "Fallback used: " . ($result['fallback_used'] ? 'Yes' : 'No') . "\n";
            
            // Check for family term issues
            $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
            $foundForbidden = [];
            
            foreach ($forbiddenTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundForbidden[] = $term;
                }
            }
            
            // Check for romanized terms
            $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san'];
            $foundExpected = [];
            
            foreach ($expectedTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundExpected[] = $term;
                }
            }
            
            // Assessment
            if (empty($foundForbidden) && !empty($foundExpected)) {
                echo "🎉 PERFECT: No forbidden terms, found romanized: " . implode(', ', $foundExpected) . "\n";
            } elseif (empty($foundForbidden)) {
                echo "✅ GOOD: No forbidden terms found\n";
            } else {
                echo "❌ PROBLEM: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
                
                // If we found problems, let's check which translation path was used
                if (isset($result['fallback_used']) && $result['fallback_used']) {
                    echo "🔍 This chunk used FALLBACK translation - may need separate fix\n";
                } else {
                    echo "🔍 This chunk used MAIN translation - fix should have worked\n";
                }
            }
            
        } else {
            echo "❌ Translation failed: " . $result['error'] . "\n";
        }
        
        echo "\n";
    }
    
    // Test with actual Chapter 56 content to see chunking behavior
    echo "=== Testing with Actual Chapter 56 Content ===\n";
    
    $chapter = $db->fetchOne(
        'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
        []
    );
    
    // Get the problematic section (around position 4000-6000 based on analysis)
    $problematicSection = substr($chapter['original_content'], 3000, 2000);
    
    echo "Testing problematic section (chars 3000-5000):\n";
    echo "Sample: " . substr($problematicSection, 0, 200) . "...\n";
    
    $chunkContext = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $familyNames,
        'chunk_number' => 3,
        'total_chunks' => 4,
        'is_first_chunk' => false,
        'is_last_chunk' => false
    ];
    
    $result = $translationService->translateText(
        $problematicSection,
        'en',
        'ja',
        $chunkContext
    );
    
    if ($result['success']) {
        $translation = $result['translated_text'];
        echo "Translation successful!\n";
        echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
        echo "Provider used: " . ($result['provider_used'] ?? 'unknown') . "\n";
        echo "Fallback used: " . ($result['fallback_used'] ? 'Yes' : 'No') . "\n";
        
        // Check for family term issues
        $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $foundForbidden = [];
        
        foreach ($forbiddenTerms as $term) {
            if (stripos($translation, $term) !== false) {
                $foundForbidden[] = $term;
            }
        }
        
        if (empty($foundForbidden)) {
            echo "✅ EXCELLENT: No forbidden family terms in problematic section\n";
        } else {
            echo "❌ CONFIRMED PROBLEM: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
            echo "🔍 This confirms the issue exists in chunked translation\n";
        }
        
        // Show sample
        echo "Sample translation: " . substr($translation, 0, 300) . "...\n";
        
    } else {
        echo "❌ Problematic section translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
