<?php
/**
 * Fix Family Term Translations in Name Dictionary
 * Updates family terms to use proper English + honorific format instead of romanization
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Fixing Family Term Translations in Name Dictionary ===\n\n";

$db = Database::getInstance();

// Define correct translations for family terms according to user preference
$familyTermCorrections = [
    // Japanese family terms with honorifics
    'お父さん' => 'father-san',
    '父さん' => 'father-san',
    'お母さん' => 'mother-san', 
    '母さん' => 'mother-san',
    'お兄さん' => 'big brother-san',
    '兄さん' => 'big brother-san',
    'お姉さん' => 'big sister-san',
    '姉さん' => 'big sister-san',
    'お兄ちゃん' => 'big brother-chan',
    '兄ちゃん' => 'big brother-chan',
    'お姉ちゃん' => 'big sister-chan',
    '姉ちゃん' => 'big sister-chan',
    'おじさん' => 'uncle-san',
    'おばさん' => 'aunt-san',
    'おじいさん' => 'grandfather-san',
    'おばあさん' => 'grandmother-san',
    'おじいちゃん' => 'grandfather-chan',
    'おばあちゃん' => 'grandmother-chan',
    'お父様' => 'father-sama',
    'お母様' => 'mother-sama',
    'お兄様' => 'big brother-sama',
    'お姉様' => 'big sister-sama'
];

echo "Checking family terms in name dictionary for novel 7...\n\n";

$updatedCount = 0;
$totalChecked = 0;

foreach ($familyTermCorrections as $originalName => $correctTranslation) {
    // Check if this term exists in the name dictionary
    $existing = $db->fetchOne(
        'SELECT id, original_name, romanization, translation, name_type, frequency 
         FROM name_dictionary 
         WHERE novel_id = 7 AND original_name = ?',
        [$originalName]
    );
    
    if ($existing) {
        $totalChecked++;
        $currentTranslation = $existing['translation'];
        
        echo "Found: {$originalName}\n";
        echo "  Current translation: '{$currentTranslation}'\n";
        echo "  Correct translation: '{$correctTranslation}'\n";
        
        if ($currentTranslation !== $correctTranslation) {
            // Update the translation
            $updated = $db->update(
                'name_dictionary',
                [
                    'translation' => $correctTranslation,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$existing['id']]
            );
            
            if ($updated > 0) {
                echo "  ✓ Updated translation from '{$currentTranslation}' to '{$correctTranslation}'\n";
                $updatedCount++;
            } else {
                echo "  ✗ Failed to update translation\n";
            }
        } else {
            echo "  ✓ Translation already correct\n";
        }
        echo "\n";
    }
}

echo "=== Summary ===\n";
echo "Total family terms checked: {$totalChecked}\n";
echo "Translations updated: {$updatedCount}\n";

if ($updatedCount > 0) {
    echo "\n✓ Family term translations have been corrected!\n";
    echo "Gemini will now use proper English + honorific format like:\n";
    echo "- 父さん → 'father-san' (instead of 'Tou-san')\n";
    echo "- 母さん → 'mother-san' (instead of 'kaa-san')\n";
    echo "- お兄ちゃん → 'big brother-chan' (instead of 'onii-chan')\n";
    echo "\nThis follows your preference for honorifics properly attached to character names.\n";
} else {
    echo "\n✓ All family term translations were already correct.\n";
}

echo "\n=== Fix Complete ===\n";
