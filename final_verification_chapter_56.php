<?php
/**
 * Final verification of Chapter 56 family term fixes
 */

require_once 'config/config.php';
require_once 'config/database.php';

$db = Database::getInstance();

echo "=== Final Verification: Novel ID 7, Chapter 56 ===\n\n";

// Get the fixed chapter
$chapter = $db->fetchOne(
    'SELECT id, chapter_number, original_title, translated_title, 
            translated_content, translation_status, updated_at
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

echo "Chapter Information:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Original Title: {$chapter['original_title']}\n";
echo "- Translated Title: {$chapter['translated_title']}\n";
echo "- Translation Status: {$chapter['translation_status']}\n";
echo "- Last Updated: {$chapter['updated_at']}\n";
echo "- Content Length: " . strlen($chapter['translated_content']) . " characters\n\n";

$translatedContent = $chapter['translated_content'];

// Check for English family terms that should not exist
echo "=== Checking for Prohibited English Family Terms ===\n";
$prohibitedTerms = ['father', 'mother', 'mom', 'dad', 'brother', 'sister'];
$foundProhibited = [];

foreach ($prohibitedTerms as $term) {
    $count = 0;
    $offset = 0;
    $positions = [];
    
    while (($pos = stripos($translatedContent, $term, $offset)) !== false) {
        $beforeChar = $pos > 0 ? $translatedContent[$pos - 1] : ' ';
        $afterChar = $pos + strlen($term) < strlen($translatedContent) ? $translatedContent[$pos + strlen($term)] : ' ';
        
        // Check if it's a standalone word (not part of another word like "moment")
        if (!ctype_alnum($beforeChar) && !ctype_alnum($afterChar)) {
            $count++;
            $start = max(0, $pos - 50);
            $end = min(strlen($translatedContent), $pos + strlen($term) + 50);
            $context = substr($translatedContent, $start, $end - $start);
            $positions[] = $context;
        }
        $offset = $pos + 1;
    }
    
    if ($count > 0) {
        $foundProhibited[] = [
            'term' => $term,
            'count' => $count,
            'contexts' => $positions
        ];
    }
}

if (empty($foundProhibited)) {
    echo "✅ PASS: No prohibited English family terms found\n";
} else {
    echo "❌ FAIL: Found prohibited terms:\n";
    foreach ($foundProhibited as $issue) {
        echo "- '{$issue['term']}': {$issue['count']} occurrence(s)\n";
        foreach ($issue['contexts'] as $i => $context) {
            echo "  Context " . ($i + 1) . ": ...{$context}...\n";
        }
    }
}

// Check for expected romanized family terms
echo "\n=== Checking for Expected Romanized Family Terms ===\n";
$expectedTerms = [
    'Otou-san' => 'father',
    'Okaa-san' => 'mother', 
    'Onii-san' => 'older brother',
    'Onee-san' => 'older sister',
    'Onii-chan' => 'big brother (affectionate)',
    'Onee-chan' => 'big sister (affectionate)',
    'Tou-san' => 'father (casual)',
    'Kaa-san' => 'mother (casual)',
    'Nii-san' => 'brother'
];

$foundExpected = [];
foreach ($expectedTerms as $romanized => $meaning) {
    $count = substr_count($translatedContent, $romanized);
    if ($count > 0) {
        $foundExpected[] = [
            'term' => $romanized,
            'meaning' => $meaning,
            'count' => $count
        ];
    }
}

if (!empty($foundExpected)) {
    echo "✅ GOOD: Found expected romanized terms:\n";
    foreach ($foundExpected as $term) {
        echo "- '{$term['term']}' ({$term['meaning']}): {$term['count']} occurrence(s)\n";
    }
} else {
    echo "⚠️ No romanized family terms found - this might indicate an issue\n";
}

// Check for isolated honorifics (should not exist)
echo "\n=== Checking for Isolated Honorifics ===\n";
$isolatedHonorifics = [];
$honorificPatterns = [
    '/-chan\b/' => '-chan',
    '/-san\b/' => '-san', 
    '/-sama\b/' => '-sama',
    '/-kun\b/' => '-kun'
];

foreach ($honorificPatterns as $pattern => $honorific) {
    if (preg_match_all($pattern, $translatedContent, $matches)) {
        $isolatedHonorifics[] = [
            'honorific' => $honorific,
            'count' => count($matches[0])
        ];
    }
}

if (empty($isolatedHonorifics)) {
    echo "✅ PASS: No isolated honorifics found\n";
} else {
    echo "❌ FAIL: Found isolated honorifics:\n";
    foreach ($isolatedHonorifics as $honorific) {
        echo "- '{$honorific['honorific']}': {$honorific['count']} occurrence(s)\n";
    }
}

// Overall assessment
echo "\n=== Overall Assessment ===\n";
$passedChecks = 0;
$totalChecks = 3;

if (empty($foundProhibited)) $passedChecks++;
if (!empty($foundExpected)) $passedChecks++;
if (empty($isolatedHonorifics)) $passedChecks++;

if ($passedChecks === $totalChecks) {
    echo "🎉 SUCCESS: Chapter 56 translation now follows all established family term rules!\n";
    echo "✅ No English family terms (father, mother, brother, sister)\n";
    echo "✅ Uses proper romanized family terms (Otou-san, Okaa-san, Onii-san, etc.)\n";
    echo "✅ No isolated honorifics\n";
    echo "✅ Gemini AI configuration has been fixed to prevent future issues\n";
} else {
    echo "⚠️ PARTIAL SUCCESS: {$passedChecks}/{$totalChecks} checks passed\n";
    echo "Some issues may still need attention.\n";
}

// Show sample of corrected content
echo "\n=== Sample of Corrected Content ===\n";
echo substr($translatedContent, 0, 800) . "...\n";

echo "\n=== Verification Complete ===\n";
