<?php
/**
 * Test Chapter 56 retranslation with stable Gemini versions
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing Chapter 56 with Stable Gemini Versions ===\n\n";

$db = Database::getInstance();

// Get original content of chapter 56
$chapter = $db->fetchOne(
    'SELECT id, chapter_number, original_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

// Get a small sample that contains family terms
$originalContent = $chapter['original_content'];

// Find a section with family terms
$familyTermSample = '';
$lines = explode("\n", $originalContent);
foreach ($lines as $line) {
    if (preg_match('/[父母兄姉]|お[父母兄姉]/', $line)) {
        $familyTermSample = $line;
        break;
    }
}

if (empty($familyTermSample)) {
    // Use first 500 characters if no family terms found in individual lines
    $familyTermSample = substr($originalContent, 0, 500);
}

echo "Testing with sample containing family terms:\n";
echo "Sample: {$familyTermSample}\n\n";

try {
    $providerManager = new AIProviderManager();
    
    // Test with Gemini 1.5 and 2.0 (stable versions)
    $stableVersions = ['gemini_15', 'gemini_20'];
    
    // Get name dictionary for novel 7
    $names = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         ORDER BY frequency DESC',
        []
    );
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $names
    ];
    
    echo "Using name dictionary with " . count($names) . " entries\n\n";
    
    foreach ($stableVersions as $version) {
        echo "=== Testing {$version} ===\n";
        
        try {
            $providerManager->setActiveProvider($version);
            $translationService = $providerManager->getTranslationService($version);
            
            echo "Version: " . $translationService->getVersion() . "\n";
            
            // Perform translation
            $result = $translationService->translateText(
                $familyTermSample,
                'en',
                'ja',
                $context
            );
            
            if ($result['success']) {
                $translation = $result['translated_text'];
                echo "Translation: {$translation}\n";
                echo "Execution time: " . $result['execution_time'] . "s\n";
                
                // Check for problematic English family terms
                $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
                $foundProblems = [];
                
                foreach ($problematicTerms as $term) {
                    if (stripos($translation, $term) !== false) {
                        $foundProblems[] = $term;
                    }
                }
                
                // Check for expected romanized terms
                $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san', 'Onee-san'];
                $foundExpected = [];
                
                foreach ($expectedTerms as $term) {
                    if (stripos($translation, $term) !== false) {
                        $foundExpected[] = $term;
                    }
                }
                
                // Assessment
                if (empty($foundProblems)) {
                    echo "✅ GOOD: No English family terms found\n";
                } else {
                    echo "❌ PROBLEM: Found English family terms: " . implode(', ', $foundProblems) . "\n";
                }
                
                if (!empty($foundExpected)) {
                    echo "✅ GOOD: Found romanized terms: " . implode(', ', $foundExpected) . "\n";
                } else {
                    echo "⚠️ WARNING: No romanized family terms found\n";
                }
                
            } else {
                echo "❌ Translation failed: " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error with {$version}: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    // Now test with a longer sample to see real behavior
    echo "=== Testing with Longer Sample ===\n";
    $longerSample = substr($originalContent, 0, 2000);
    
    echo "Using Gemini 1.5 for longer sample test...\n";
    $providerManager->setActiveProvider('gemini_15');
    $translationService = $providerManager->getTranslationService('gemini_15');
    
    $result = $translationService->translateText(
        $longerSample,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        $translation = $result['translated_text'];
        echo "Translation successful (length: " . strlen($translation) . " chars)\n";
        
        // Check for family term issues in longer text
        $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $foundProblems = [];
        
        foreach ($problematicTerms as $term) {
            $count = substr_count(strtolower($translation), strtolower($term));
            if ($count > 0) {
                $foundProblems[] = "{$term}: {$count}";
            }
        }
        
        if (empty($foundProblems)) {
            echo "✅ EXCELLENT: No English family terms in longer sample\n";
        } else {
            echo "❌ CONFIRMED PROBLEM: Found English family terms in longer sample:\n";
            foreach ($foundProblems as $problem) {
                echo "- {$problem}\n";
            }
        }
        
        // Show first part of translation
        echo "\nFirst 500 chars of translation:\n";
        echo substr($translation, 0, 500) . "...\n";
        
    } else {
        echo "❌ Longer sample translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ General Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
