<?php
/**
 * Test actual retranslation of Chapter 56 to see if family terms are still problematic
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing Actual Chapter 56 Retranslation ===\n\n";

$db = Database::getInstance();

// Get original content of chapter 56
$chapter = $db->fetchOne(
    'SELECT id, chapter_number, original_content, translated_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

echo "Chapter 56 found:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Original content length: " . strlen($chapter['original_content']) . " characters\n";
echo "- Current translated content length: " . strlen($chapter['translated_content']) . " characters\n\n";

// Get a small sample of original content to test translation
$originalContent = $chapter['original_content'];
$sampleLength = 1000; // Test with first 1000 characters
$sampleContent = substr($originalContent, 0, $sampleLength);

echo "Testing translation with sample content (first {$sampleLength} characters):\n";
echo "Sample: " . substr($sampleContent, 0, 200) . "...\n\n";

try {
    $providerManager = new AIProviderManager();
    
    // Test with Gemini 2.5 (the problematic one)
    $providerManager->setActiveProvider('gemini_25');
    $translationService = $providerManager->getTranslationService('gemini_25');
    
    echo "Using Gemini 2.5 for retranslation test...\n";
    
    // Get name dictionary for novel 7
    $names = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         ORDER BY frequency DESC',
        []
    );
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $names
    ];
    
    echo "Using name dictionary with " . count($names) . " entries\n\n";
    
    // Perform translation
    echo "Translating sample content...\n";
    $result = $translationService->translateText(
        $sampleContent,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        $translation = $result['translated_text'];
        echo "Translation successful!\n";
        echo "Execution time: " . $result['execution_time'] . "s\n\n";
        
        echo "=== Checking for Family Term Issues ===\n";
        
        // Check for problematic English family terms
        $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $foundProblems = [];
        
        foreach ($problematicTerms as $term) {
            $count = 0;
            $offset = 0;
            $contexts = [];
            
            while (($pos = stripos($translation, $term, $offset)) !== false) {
                $beforeChar = $pos > 0 ? $translation[$pos - 1] : ' ';
                $afterChar = $pos + strlen($term) < strlen($translation) ? $translation[$pos + strlen($term)] : ' ';
                
                // Check if it's a standalone word
                if (!ctype_alnum($beforeChar) && !ctype_alnum($afterChar)) {
                    $count++;
                    $start = max(0, $pos - 50);
                    $end = min(strlen($translation), $pos + strlen($term) + 50);
                    $context = substr($translation, $start, $end - $start);
                    $contexts[] = $context;
                }
                $offset = $pos + 1;
            }
            
            if ($count > 0) {
                $foundProblems[] = [
                    'term' => $term,
                    'count' => $count,
                    'contexts' => $contexts
                ];
            }
        }
        
        if (empty($foundProblems)) {
            echo "✅ GOOD: No problematic English family terms found\n";
        } else {
            echo "❌ PROBLEM CONFIRMED: Found English family terms:\n";
            foreach ($foundProblems as $problem) {
                echo "- '{$problem['term']}': {$problem['count']} occurrence(s)\n";
                foreach ($problem['contexts'] as $i => $context) {
                    echo "  Context " . ($i + 1) . ": ...{$context}...\n";
                }
            }
        }
        
        // Check for expected romanized terms
        echo "\n=== Checking for Expected Romanized Terms ===\n";
        $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san', 'Onee-san'];
        $foundExpected = [];
        
        foreach ($expectedTerms as $term) {
            $count = substr_count($translation, $term);
            if ($count > 0) {
                $foundExpected[] = "{$term}: {$count}";
            }
        }
        
        if (!empty($foundExpected)) {
            echo "✅ GOOD: Found romanized terms: " . implode(', ', $foundExpected) . "\n";
        } else {
            echo "⚠️ WARNING: No romanized family terms found\n";
        }
        
        // Show sample of translation
        echo "\n=== Sample Translation Output ===\n";
        echo substr($translation, 0, 800) . "...\n";
        
    } else {
        echo "❌ Translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
