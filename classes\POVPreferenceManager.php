<?php

/**
 * POV Preference Manager
 * 
 * Handles storage, retrieval, and management of Point of View preferences
 * for novels and chapters. Supports both novel-level defaults and 
 * chapter-specific overrides.
 */
class POVPreferenceManager {
    
    private $db;
    
    /**
     * POV preference constants (matching PerspectiveService)
     */
    const POV_PRESERVE_ORIGINAL = 'preserve_original';
    const POV_FIRST_PERSON = 'first_person';
    const POV_SECOND_PERSON = 'second_person';
    const POV_THIRD_PERSON_LIMITED = 'third_person_limited';
    const POV_THIRD_PERSON_OMNISCIENT = 'third_person_omniscient';
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Set POV preference for a novel (default for all chapters)
     */
    public function setNovelPOVPreference(int $novelId, string $povPreference): bool {
        try {
            // Validate POV preference
            if (!$this->isValidPOVPreference($povPreference)) {
                throw new InvalidArgumentException("Invalid POV preference: {$povPreference}");
            }
            
            // Remove existing default preference for this novel
            $this->db->query(
                "DELETE FROM pov_preferences WHERE novel_id = ? AND is_default = TRUE",
                [$novelId]
            );
            
            // Insert new default preference
            $this->db->insert('pov_preferences', [
                'novel_id' => $novelId,
                'chapter_id' => null,
                'pov_preference' => $povPreference,
                'is_default' => true
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to set novel POV preference: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Set POV preference for a specific chapter
     */
    public function setChapterPOVPreference(int $novelId, int $chapterId, string $povPreference): bool {
        try {
            // Validate POV preference
            if (!$this->isValidPOVPreference($povPreference)) {
                throw new InvalidArgumentException("Invalid POV preference: {$povPreference}");
            }
            
            // Check if chapter exists and belongs to the novel
            $chapter = $this->db->fetchOne(
                "SELECT id FROM chapters WHERE id = ? AND novel_id = ?",
                [$chapterId, $novelId]
            );
            
            if (!$chapter) {
                throw new InvalidArgumentException("Chapter {$chapterId} not found for novel {$novelId}");
            }
            
            // Remove existing preference for this chapter
            $this->db->query(
                "DELETE FROM pov_preferences WHERE chapter_id = ?",
                [$chapterId]
            );
            
            // Insert new chapter-specific preference
            $this->db->insert('pov_preferences', [
                'novel_id' => $novelId,
                'chapter_id' => $chapterId,
                'pov_preference' => $povPreference,
                'is_default' => false
            ]);
            
            return true;
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to set chapter POV preference: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get POV preference for a specific chapter (with fallback to novel default)
     */
    public function getChapterPOVPreference(int $novelId, int $chapterId): ?string {
        try {
            // First, check for chapter-specific preference
            $chapterPOV = $this->db->fetchOne(
                "SELECT pov_preference FROM pov_preferences 
                 WHERE chapter_id = ? AND novel_id = ?",
                [$chapterId, $novelId]
            );
            
            if ($chapterPOV) {
                return $chapterPOV['pov_preference'];
            }
            
            // Fall back to novel default
            return $this->getNovelPOVPreference($novelId);
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to get chapter POV preference: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get POV preference for a novel (default)
     */
    public function getNovelPOVPreference(int $novelId): ?string {
        try {
            $novelPOV = $this->db->fetchOne(
                "SELECT pov_preference FROM pov_preferences 
                 WHERE novel_id = ? AND is_default = TRUE",
                [$novelId]
            );
            
            return $novelPOV ? $novelPOV['pov_preference'] : null;
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to get novel POV preference: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get all POV preferences for a novel (including chapter-specific ones)
     */
    public function getNovelPOVPreferences(int $novelId): array {
        try {
            $preferences = $this->db->fetchAll(
                "SELECT p.*, c.chapter_number 
                 FROM pov_preferences p
                 LEFT JOIN chapters c ON p.chapter_id = c.id
                 WHERE p.novel_id = ?
                 ORDER BY p.is_default DESC, c.chapter_number ASC",
                [$novelId]
            );
            
            $result = [
                'novel_default' => null,
                'chapter_specific' => []
            ];
            
            foreach ($preferences as $pref) {
                if ($pref['is_default']) {
                    $result['novel_default'] = $pref['pov_preference'];
                } else {
                    $result['chapter_specific'][] = [
                        'chapter_id' => $pref['chapter_id'],
                        'chapter_number' => $pref['chapter_number'],
                        'pov_preference' => $pref['pov_preference'],
                        'applied_at' => $pref['applied_at']
                    ];
                }
            }
            
            return $result;
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to get novel POV preferences: " . $e->getMessage());
            return ['novel_default' => null, 'chapter_specific' => []];
        }
    }
    
    /**
     * Remove POV preference for a chapter
     */
    public function removeChapterPOVPreference(int $chapterId): bool {
        try {
            $this->db->query(
                "DELETE FROM pov_preferences WHERE chapter_id = ?",
                [$chapterId]
            );
            return true;
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to remove chapter POV preference: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Remove POV preference for a novel (default)
     */
    public function removeNovelPOVPreference(int $novelId): bool {
        try {
            $this->db->query(
                "DELETE FROM pov_preferences WHERE novel_id = ? AND is_default = TRUE",
                [$novelId]
            );
            return true;
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to remove novel POV preference: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get POV preference statistics for a novel
     */
    public function getPOVStatistics(int $novelId): array {
        try {
            $stats = $this->db->fetchAll(
                "SELECT 
                    pov_preference,
                    COUNT(*) as usage_count,
                    COUNT(CASE WHEN is_default = TRUE THEN 1 END) as is_default_count
                 FROM pov_preferences 
                 WHERE novel_id = ?
                 GROUP BY pov_preference",
                [$novelId]
            );
            
            $totalChapters = $this->db->fetchOne(
                "SELECT COUNT(*) as count FROM chapters WHERE novel_id = ?",
                [$novelId]
            )['count'];
            
            return [
                'total_chapters' => $totalChapters,
                'pov_usage' => $stats,
                'has_preferences' => !empty($stats)
            ];
        } catch (Exception $e) {
            error_log("POVPreferenceManager: Failed to get POV statistics: " . $e->getMessage());
            return ['total_chapters' => 0, 'pov_usage' => [], 'has_preferences' => false];
        }
    }
    
    /**
     * Validate POV preference value
     */
    private function isValidPOVPreference(string $povPreference): bool {
        $validPreferences = [
            self::POV_PRESERVE_ORIGINAL,
            self::POV_FIRST_PERSON,
            self::POV_SECOND_PERSON,
            self::POV_THIRD_PERSON_LIMITED,
            self::POV_THIRD_PERSON_OMNISCIENT
        ];
        
        return in_array($povPreference, $validPreferences);
    }
    
    /**
     * Get available POV options with descriptions
     */
    public function getAvailablePOVOptions(): array {
        return [
            self::POV_PRESERVE_ORIGINAL => [
                'name' => 'Preserve Original',
                'description' => 'Maintain the original perspective as detected in the source text'
            ],
            self::POV_FIRST_PERSON => [
                'name' => 'First Person',
                'description' => 'Use "I", "me", "my" - Personal, intimate narrative from the protagonist\'s viewpoint'
            ],
            self::POV_SECOND_PERSON => [
                'name' => 'Second Person',
                'description' => 'Use "you", "your" - Direct address to the reader (rare in novels)'
            ],
            self::POV_THIRD_PERSON_LIMITED => [
                'name' => 'Third Person Limited',
                'description' => 'Use "he", "she", "they" - Focus on one character\'s thoughts and feelings'
            ],
            self::POV_THIRD_PERSON_OMNISCIENT => [
                'name' => 'Third Person Omniscient',
                'description' => 'Use "he", "she", "they" - Access to all characters\' thoughts and feelings'
            ]
        ];
    }
}
