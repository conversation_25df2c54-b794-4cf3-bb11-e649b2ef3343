<?php
/**
 * Get meaningful family term samples from Chapter 56
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Getting Meaningful Family Term Samples ===\n\n";

$db = Database::getInstance();

// Get original content of chapter 56
$chapter = $db->fetchOne(
    'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
    []
);

$originalContent = $chapter['original_content'];

// Look for longer sentences/paragraphs with family terms
$familyTermPatterns = ['父', '母', '兄さん', '兄'];

echo "=== Finding Meaningful Sentences ===\n";

// Split by paragraph and find those with family terms
$paragraphs = explode("\n", $originalContent);
$meaningfulSamples = [];

foreach ($paragraphs as $paragraph) {
    $paragraph = trim($paragraph);
    if (strlen($paragraph) < 20) continue; // Skip very short paragraphs
    
    foreach ($familyTermPatterns as $term) {
        if (strpos($paragraph, $term) !== false) {
            $meaningfulSamples[] = $paragraph;
            break;
        }
    }
}

// Show the samples
foreach ($meaningfulSamples as $i => $sample) {
    echo "Sample " . ($i + 1) . ":\n";
    echo "{$sample}\n\n";
    
    if ($i >= 9) { // Show first 10 samples
        echo "... and " . (count($meaningfulSamples) - 10) . " more samples\n";
        break;
    }
}

// Pick a good sample for testing
$testSamples = [
    "「もう、兄さんは……ちょっと元侯爵様に挨拶に行くだけだからって言っていたのに、まさかそのまま任務に行ってしまうなんて」",
    "「はあ……兄さん、会いたいです……」",
    "お母様、よくお父様のことをロクでもない遊び人だって言っていましたけど、実はあなたも同じなのでは……？",
    "「お母様、一体何を想像しているんですか！？ 私は全然音なんて立てていませんよ！？」"
];

echo "\n=== Selected Test Samples ===\n";
foreach ($testSamples as $i => $sample) {
    echo "Test " . ($i + 1) . ": {$sample}\n";
}

echo "\n=== Testing Translation with These Samples ===\n";

try {
    require_once 'classes/AIProviderManager.php';
    
    $providerManager = new AIProviderManager();
    $providerManager->setActiveProvider('gemini_15'); // Use stable version
    $translationService = $providerManager->getTranslationService('gemini_15');
    
    // Get name dictionary
    $names = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         ORDER BY frequency DESC',
        []
    );
    
    $context = [
        'type' => 'test',
        'novel_id' => 7,
        'names' => $names
    ];
    
    foreach ($testSamples as $i => $sample) {
        echo "\n--- Test " . ($i + 1) . " ---\n";
        echo "Original: {$sample}\n";
        
        $result = $translationService->translateText(
            $sample,
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            $translation = $result['translated_text'];
            echo "Translation: {$translation}\n";
            
            // Check for family term issues
            $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
            $foundProblems = [];
            
            foreach ($problematicTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundProblems[] = $term;
                }
            }
            
            if (empty($foundProblems)) {
                echo "✅ GOOD: No English family terms\n";
            } else {
                echo "❌ PROBLEM: Found English family terms: " . implode(', ', $foundProblems) . "\n";
            }
            
            // Check for romanized terms
            $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san'];
            $foundExpected = [];
            
            foreach ($expectedTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundExpected[] = $term;
                }
            }
            
            if (!empty($foundExpected)) {
                echo "✅ GOOD: Found romanized: " . implode(', ', $foundExpected) . "\n";
            }
            
        } else {
            echo "❌ Translation failed: " . $result['error'] . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
