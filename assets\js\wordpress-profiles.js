/**
 * WordPress Profiles Management
 * Handles profile creation, editing, and selection for multi-domain posting
 */

class WordPressProfiles {
    constructor() {
        this.profiles = [];
        this.currentProfile = null;
        this.init();
    }

    async init() {
        await this.loadProfiles();
        this.bindEvents();
    }

    async loadProfiles() {
        try {
            const response = await fetch('api/wordpress-profiles.php?action=list');
            const result = await response.json();
            
            if (result.success) {
                this.profiles = result.profiles;
                this.updateProfileSelectors();
            } else {
                console.error('Failed to load profiles:', result.error);
            }
        } catch (error) {
            console.error('Error loading profiles:', error);
        }
    }

    bindEvents() {
        // Profile management modal events
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-action="create-profile"]')) {
                this.showCreateProfileModal();
            } else if (e.target.matches('[data-action="edit-profile"]')) {
                // Handle kebab-case to camelCase conversion
                const profileId = parseInt(e.target.getAttribute('data-profile-id'));
                console.log('Edit profile clicked, ID:', profileId);
                this.showEditProfileModal(profileId);
            } else if (e.target.matches('[data-action="delete-profile"]')) {
                // Handle kebab-case to camelCase conversion
                const profileId = parseInt(e.target.getAttribute('data-profile-id'));
                console.log('Delete profile clicked, ID:', profileId);
                this.deleteProfile(profileId);
            } else if (e.target.matches('[data-action="test-profile"]')) {
                // Handle kebab-case to camelCase conversion
                const profileId = parseInt(e.target.getAttribute('data-profile-id'));
                console.log('Test profile clicked, ID:', profileId);
                this.testProfileConnection(profileId);
            }
        });

        // Profile selection events
        document.addEventListener('change', (e) => {
            if (e.target.matches('.profile-selector')) {
                this.currentProfile = e.target.value ? parseInt(e.target.value) : null;
            }
        });
    }

    updateProfileSelectors() {
        const selectors = document.querySelectorAll('.profile-selector');
        
        selectors.forEach(selector => {
            // Clear existing options except the first one
            while (selector.children.length > 1) {
                selector.removeChild(selector.lastChild);
            }
            
            // Add profile options
            this.profiles.forEach(profile => {
                if (profile.is_active) {
                    const option = document.createElement('option');
                    option.value = profile.id;
                    option.textContent = profile.profile_name;
                    selector.appendChild(option);
                }
            });
        });
    }

    showCreateProfileModal() {
        const modal = this.createProfileModal();
        document.body.appendChild(modal);
        
        // Show modal
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Add event listener for load categories button
        const loadCategoriesBtn = document.getElementById('load_categories_new');
        if (loadCategoriesBtn) {
            loadCategoriesBtn.addEventListener('click', () => {
                this.loadCategoriesForProfile(null, 'default_category_new');
            });
        }

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    showEditProfileModal(profileId) {
        const profile = this.profiles.find(p => p.id === profileId);
        if (!profile) {
            console.error('Profile not found for ID:', profileId);
            if (typeof utils !== 'undefined') {
                utils.showToast('Profile not found', 'error');
            } else {
                alert('Profile not found');
            }
            return;
        }

        console.log('Editing profile:', profile);
        const modal = this.createProfileModal(profile);
        document.body.appendChild(modal);

        // Show modal
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // Add event listener for load categories button
        const loadCategoriesBtn = document.getElementById(`load_categories_${profile.id}`);
        if (loadCategoriesBtn) {
            loadCategoriesBtn.addEventListener('click', () => {
                this.loadCategoriesForProfile(profile.id, `default_category_${profile.id}`);
            });
        }

        // Pre-select current category if it exists
        if (profile.default_category) {
            setTimeout(() => {
                const selectElement = document.getElementById(`default_category_${profile.id}`);
                if (selectElement) {
                    // Try to set the value, but if it's not in the options, add it as a custom option
                    selectElement.value = profile.default_category;
                    if (selectElement.value !== profile.default_category) {
                        const option = document.createElement('option');
                        option.value = profile.default_category;
                        option.textContent = `Current: ${profile.default_category}`;
                        option.selected = true;
                        selectElement.appendChild(option);
                    }
                }
            }, 100);
        }

        // Clean up when modal is hidden
        modal.addEventListener('hidden.bs.modal', () => {
            document.body.removeChild(modal);
        });
    }

    createProfileModal(profile = null) {
        const isEdit = profile !== null;
        const modalId = 'wordpressProfileModal';
        
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = modalId;
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fab fa-wordpress me-2"></i>
                            ${isEdit ? 'Edit' : 'Create'} WordPress Profile
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="profileForm">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Profile Name *</label>
                                        <input type="text" class="form-control" name="profile_name" 
                                               value="${profile?.profile_name || ''}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Site URL *</label>
                                        <input type="url" class="form-control" name="site_url" 
                                               value="${profile?.site_url || ''}" 
                                               placeholder="https://yoursite.com" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Username *</label>
                                        <input type="text" class="form-control" name="username" 
                                               value="${profile?.username || ''}" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Application Password *</label>
                                        <input type="password" class="form-control" name="app_password" 
                                               value="${profile?.app_password || ''}" required>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Novel Post Type</label>
                                        <select class="form-select" name="novel_post_type">
                                            <option value="page" ${profile?.novel_post_type === 'page' ? 'selected' : ''}>Page</option>
                                            <option value="post" ${profile?.novel_post_type === 'post' ? 'selected' : ''}>Post</option>
                                            <option value="custom" ${profile?.novel_post_type === 'custom' ? 'selected' : ''}>Custom</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Chapter Post Type</label>
                                        <select class="form-select" name="chapter_post_type">
                                            <option value="post" ${profile?.chapter_post_type === 'post' ? 'selected' : ''}>Post</option>
                                            <option value="page" ${profile?.chapter_post_type === 'page' ? 'selected' : ''}>Page</option>
                                            <option value="custom" ${profile?.chapter_post_type === 'custom' ? 'selected' : ''}>Custom</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Novel Custom Post Type</label>
                                        <input type="text" class="form-control" name="novel_custom_post_type" 
                                               value="${profile?.novel_custom_post_type || ''}" 
                                               placeholder="e.g., novel, book">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label">Chapter Custom Post Type</label>
                                        <input type="text" class="form-control" name="chapter_custom_post_type" 
                                               value="${profile?.chapter_custom_post_type || ''}" 
                                               placeholder="e.g., chapter, episode">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Default Category</label>
                                <div class="input-group">
                                    <select class="form-select" name="default_category" id="default_category_${profile?.id || 'new'}">
                                        <option value="">Select a category...</option>
                                    </select>
                                    <button class="btn btn-outline-secondary" type="button" id="load_categories_${profile?.id || 'new'}" title="Load categories from WordPress">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    <small class="text-muted">Load categories after entering site URL and credentials above</small>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="use_custom_post_types"
                                               ${profile && (profile.use_custom_post_types == 1 || profile.use_custom_post_types == '1' || profile.use_custom_post_types === true) ? 'checked' : ''}>
                                        <label class="form-check-label">Use Custom Post Types</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="auto_publish"
                                               ${!profile || profile.auto_publish == 1 || profile.auto_publish == '1' || profile.auto_publish === true ? 'checked' : ''}>
                                        <label class="form-check-label">Auto Publish</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="include_original_title"
                                               ${!profile || profile.include_original_title == 1 || profile.include_original_title == '1' || profile.include_original_title === true ? 'checked' : ''}>
                                        <label class="form-check-label">Include Original Titles</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-check mt-3">
                                <input class="form-check-input" type="checkbox" name="is_active"
                                       ${!profile || profile.is_active == 1 || profile.is_active == '1' || profile.is_active === true ? 'checked' : ''}>
                                <label class="form-check-label">Active Profile</label>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="wordpressProfiles.saveProfile(${profile?.id || 'null'})">
                            ${isEdit ? 'Update' : 'Create'} Profile
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        return modal;
    }

    async saveProfile(profileId = null) {
        console.log('Saving profile, ID:', profileId);

        const form = document.getElementById('profileForm');
        if (!form) {
            console.error('Profile form not found');
            return;
        }

        const formData = new FormData(form);

        const profileData = {
            profile_name: formData.get('profile_name'),
            site_url: formData.get('site_url'),
            username: formData.get('username'),
            app_password: formData.get('app_password'),
            novel_post_type: formData.get('novel_post_type'),
            chapter_post_type: formData.get('chapter_post_type'),
            novel_custom_post_type: formData.get('novel_custom_post_type'),
            chapter_custom_post_type: formData.get('chapter_custom_post_type'),
            default_category: formData.get('default_category'),
            use_custom_post_types: formData.has('use_custom_post_types'),
            auto_publish: formData.has('auto_publish'),
            include_original_title: formData.has('include_original_title'),
            is_active: formData.has('is_active')
        };

        console.log('Profile data to save:', profileData);

        try {
            let url, method, body;

            if (profileId) {
                // Update existing profile
                url = 'api/wordpress-profiles.php';
                method = 'PUT';
                body = JSON.stringify({
                    action: 'update',
                    profile_id: profileId,
                    profile_data: profileData
                });
                console.log('Updating profile with data:', body);
            } else {
                // Create new profile
                url = 'api/wordpress-profiles.php';
                method = 'POST';
                body = JSON.stringify({
                    action: 'create',
                    profile_data: profileData
                });
                console.log('Creating new profile with data:', body);
            }

            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: body
            });

            console.log('Save response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Save result:', result);

            if (result.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('wordpressProfileModal'));
                modal.hide();
                
                // Reload profiles
                await this.loadProfiles();
                
                // Show success message
                if (typeof utils !== 'undefined') {
                    utils.showToast(result.message || 'Profile saved successfully!', 'success');
                } else {
                    alert(result.message || 'Profile saved successfully!');
                }
                
                // Refresh page if we're on settings page
                if (window.location.pathname.includes('settings.php')) {
                    window.location.reload();
                }
            } else {
                if (typeof utils !== 'undefined') {
                    utils.showToast(result.error || 'Failed to save profile', 'error');
                } else {
                    alert(result.error || 'Failed to save profile');
                }
            }
        } catch (error) {
            console.error('Error saving profile:', error);
            if (typeof utils !== 'undefined') {
                utils.showToast('Network error occurred', 'error');
            } else {
                alert('Network error occurred');
            }
        }
    }

    async deleteProfile(profileId) {
        console.log('Attempting to delete profile ID:', profileId);

        const profile = this.profiles.find(p => p.id === profileId);
        if (!profile) {
            console.error('Profile not found for deletion, ID:', profileId);
            if (typeof utils !== 'undefined') {
                utils.showToast('Profile not found', 'error');
            } else {
                alert('Profile not found');
            }
            return;
        }

        if (!confirm(`Are you sure you want to delete the profile "${profile.profile_name}"?`)) {
            return;
        }

        try {
            console.log('Sending delete request for profile:', profileId);

            const response = await fetch('api/wordpress-profiles.php', {
                method: 'DELETE',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'delete',
                    profile_id: profileId
                })
            });

            console.log('Delete response status:', response.status);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const result = await response.json();
            console.log('Delete result:', result);

            if (result.success) {
                await this.loadProfiles();

                if (typeof utils !== 'undefined') {
                    utils.showToast(result.message || 'Profile deleted successfully!', 'success');
                } else {
                    alert(result.message || 'Profile deleted successfully!');
                }

                // Refresh page if we're on settings page
                if (window.location.pathname.includes('settings.php')) {
                    window.location.reload();
                }
            } else {
                console.error('Delete failed:', result.error);
                if (typeof utils !== 'undefined') {
                    utils.showToast(result.error || 'Failed to delete profile', 'error');
                } else {
                    alert(result.error || 'Failed to delete profile');
                }
            }
        } catch (error) {
            console.error('Error deleting profile:', error);
            if (typeof utils !== 'undefined') {
                utils.showToast('Network error occurred: ' + error.message, 'error');
            } else {
                alert('Network error occurred: ' + error.message);
            }
        }
    }

    async testProfileConnection(profileId) {
        try {
            const response = await fetch(`api/wordpress-profiles.php?action=test_connection&id=${profileId}`);
            const result = await response.json();

            if (result.success) {
                if (typeof utils !== 'undefined') {
                    utils.showToast('Connection test successful!', 'success');
                } else {
                    alert('Connection test successful!');
                }
            } else {
                if (typeof utils !== 'undefined') {
                    utils.showToast(result.error || 'Connection test failed', 'error');
                } else {
                    alert(result.error || 'Connection test failed');
                }
            }
        } catch (error) {
            console.error('Error testing connection:', error);
            if (typeof utils !== 'undefined') {
                utils.showToast('Network error occurred', 'error');
            } else {
                alert('Network error occurred');
            }
        }
    }

    getActiveProfiles() {
        return this.profiles.filter(p => p.is_active);
    }

    getProfile(profileId) {
        return this.profiles.find(p => p.id === profileId);
    }

    async loadCategoriesForProfile(profileId, selectElementId) {
        const selectElement = document.getElementById(selectElementId);
        const loadButton = document.getElementById(`load_categories_${profileId || 'new'}`);

        if (!selectElement) {
            console.error('Category select element not found:', selectElementId);
            return;
        }

        try {
            // Disable button and show loading state
            if (loadButton) {
                loadButton.disabled = true;
                loadButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            }

            // Clear existing options except the first one
            selectElement.innerHTML = '<option value="">Select a category...</option>';

            if (!profileId) {
                if (typeof utils !== 'undefined') {
                    utils.showToast('Please save the profile first to load categories', 'warning');
                } else {
                    alert('Please save the profile first to load categories');
                }
                return;
            }

            const response = await fetch(`api/wordpress-categories.php?profile_id=${profileId}&_t=${Date.now()}`);
            const result = await response.json();

            if (result.success) {
                // Add categories to select
                result.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name + (category.count > 0 ? ` (${category.count})` : '');
                    selectElement.appendChild(option);
                });

                if (typeof utils !== 'undefined') {
                    utils.showToast(`Loaded ${result.categories.length} categories`, 'success');
                } else {
                    alert(`Loaded ${result.categories.length} categories`);
                }
            } else {
                if (typeof utils !== 'undefined') {
                    utils.showToast(result.error || 'Failed to load categories', 'error');
                } else {
                    alert(result.error || 'Failed to load categories');
                }
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            if (typeof utils !== 'undefined') {
                utils.showToast('Network error occurred while loading categories', 'error');
            } else {
                alert('Network error occurred while loading categories');
            }
        } finally {
            // Restore button state
            if (loadButton) {
                loadButton.disabled = false;
                loadButton.innerHTML = '<i class="fas fa-sync-alt"></i>';
            }
        }
    }
}

// Initialize global instance
let wordpressProfiles;
document.addEventListener('DOMContentLoaded', () => {
    // Add a small delay to ensure all scripts are loaded
    setTimeout(() => {
        try {
            // Check if required dependencies are available
            if (typeof utils === 'undefined') {
                console.error('WordPress Profiles: Utils object not available');
                alert('Error: Required JavaScript utilities not loaded. Please refresh the page.');
                return;
            }

            console.log('WordPress Profiles: Initializing...');
            wordpressProfiles = new WordPressProfiles();
        } catch (error) {
            console.error('Failed to initialize WordPress Profiles:', error);
            alert('Error: Failed to initialize WordPress profiles. Please refresh the page.');
        }
    }, 100);
});
