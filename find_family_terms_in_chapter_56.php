<?php
/**
 * Find actual family terms in Chapter 56 original content
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Finding Family Terms in Chapter 56 Original Content ===\n\n";

$db = Database::getInstance();

// Get original content of chapter 56
$chapter = $db->fetchOne(
    'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

$originalContent = $chapter['original_content'];
echo "Original content length: " . strlen($originalContent) . " characters\n\n";

// Find all family terms in the content
$familyTermPatterns = [
    '父さん' => 'Tou-san',
    'お父さん' => 'Otou-san',
    '父' => 'Chichi/Otou-san',
    '母さん' => 'Kaa-san', 
    'お母さん' => 'Okaa-san',
    '母' => 'Haha/Okaa-san',
    '兄さん' => 'Nii-san',
    'お兄さん' => 'Onii-san',
    'お兄ちゃん' => 'Onii-chan',
    '兄' => 'Ani/Onii-san',
    '姉さん' => 'Nee-san',
    'お姉さん' => 'Onee-san', 
    'お姉ちゃん' => 'Onee-chan',
    '姉' => 'Ane/Onee-san'
];

$foundTerms = [];
$contextSamples = [];

foreach ($familyTermPatterns as $japanese => $romanized) {
    $count = substr_count($originalContent, $japanese);
    if ($count > 0) {
        $foundTerms[] = [
            'japanese' => $japanese,
            'romanized' => $romanized,
            'count' => $count
        ];
        
        // Find context for this term
        $pos = strpos($originalContent, $japanese);
        if ($pos !== false) {
            $start = max(0, $pos - 100);
            $end = min(strlen($originalContent), $pos + strlen($japanese) + 100);
            $context = substr($originalContent, $start, $end - $start);
            $contextSamples[] = [
                'term' => $japanese,
                'context' => $context
            ];
        }
    }
}

echo "=== Family Terms Found ===\n";
if (empty($foundTerms)) {
    echo "No family terms found in Chapter 56 original content\n";
} else {
    foreach ($foundTerms as $term) {
        echo "- {$term['japanese']} ({$term['romanized']}): {$term['count']} occurrence(s)\n";
    }
}

echo "\n=== Context Samples ===\n";
foreach ($contextSamples as $i => $sample) {
    echo "Sample " . ($i + 1) . " - {$sample['term']}:\n";
    echo "Context: ...{$sample['context']}...\n\n";
}

// Extract sentences with family terms for testing
echo "=== Extracting Sentences with Family Terms for Testing ===\n";
$sentences = preg_split('/[。！？]/', $originalContent);
$familyTermSentences = [];

foreach ($sentences as $sentence) {
    $sentence = trim($sentence);
    if (empty($sentence)) continue;
    
    foreach ($familyTermPatterns as $japanese => $romanized) {
        if (strpos($sentence, $japanese) !== false) {
            $familyTermSentences[] = $sentence . '。';
            break; // Only add once per sentence
        }
    }
}

if (!empty($familyTermSentences)) {
    echo "Found " . count($familyTermSentences) . " sentences with family terms:\n\n";
    foreach ($familyTermSentences as $i => $sentence) {
        echo "Sentence " . ($i + 1) . ":\n";
        echo "{$sentence}\n\n";
        
        if ($i >= 4) { // Show only first 5 sentences
            echo "... and " . (count($familyTermSentences) - 5) . " more sentences\n";
            break;
        }
    }
    
    // Save the first sentence for testing
    if (!empty($familyTermSentences)) {
        echo "=== Test Sample for Translation ===\n";
        $testSample = $familyTermSentences[0];
        echo "Test sample: {$testSample}\n";
        
        // Save to file for easy testing
        file_put_contents('family_term_test_sample.txt', $testSample);
        echo "✅ Saved to family_term_test_sample.txt\n";
    }
} else {
    echo "No sentences with family terms found\n";
}

echo "\n=== Analysis Complete ===\n";
