<?php
/**
 * Check Novel ID 1, Chapter 171 POV implementation
 * Verify if Third Person Omniscient POV is working correctly
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/POVPreferenceManager.php';

echo "=== Checking Novel ID 1, Chapter 171 POV Implementation ===\n\n";

$db = Database::getInstance();
$povManager = new POVPreferenceManager();

// Get chapter data
$chapter = $db->fetchOne(
    'SELECT id, chapter_number, original_title, translated_title, 
            original_content, translated_content, translation_status, updated_at
     FROM chapters 
     WHERE novel_id = 1 AND chapter_number = 171',
    []
);

if (!$chapter) {
    echo "❌ Chapter 171 not found for Novel ID 1\n";
    exit;
}

echo "Chapter found:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Chapter Number: {$chapter['chapter_number']}\n";
echo "- Original Title: {$chapter['original_title']}\n";
echo "- Translated Title: {$chapter['translated_title']}\n";
echo "- Translation Status: {$chapter['translation_status']}\n";
echo "- Last Updated: {$chapter['updated_at']}\n";
echo "- Has Original Content: " . (!empty($chapter['original_content']) ? 'Yes' : 'No') . "\n";
echo "- Has Translated Content: " . (!empty($chapter['translated_content']) ? 'Yes' : 'No') . "\n\n";

// Check POV preference for this chapter
echo "=== POV Preference Check ===\n";
$chapterPOV = $povManager->getChapterPOVPreference(1, $chapter['id']);
$novelPOV = $povManager->getNovelPOVPreference(1);

echo "- Novel Default POV: " . ($novelPOV ?: 'Not set') . "\n";
echo "- Chapter Specific POV: " . ($chapterPOV ?: 'Using novel default') . "\n";
echo "- Effective POV: " . ($chapterPOV ?: $novelPOV ?: 'preserve_original') . "\n\n";

if (empty($chapter['translated_content'])) {
    echo "❌ No translated content found for this chapter\n";
    echo "Chapter needs to be translated to test POV implementation.\n";
    exit;
}

// Analyze the translated content for POV consistency
echo "=== POV Analysis of Translated Content ===\n";

$translatedContent = $chapter['translated_content'];
$contentLength = strlen($translatedContent);
echo "- Translated content length: {$contentLength} characters\n\n";

// Check for first-person pronouns (should be minimal in third-person omniscient)
$firstPersonPronouns = ['I ', ' I ', 'I\'', 'me ', ' me', 'my ', ' my', 'mine ', ' mine', 'myself', 'we ', ' we', 'us ', ' us', 'our ', ' our', 'ours ', ' ours', 'ourselves'];
$firstPersonCount = 0;
$firstPersonFound = [];

foreach ($firstPersonPronouns as $pronoun) {
    $count = substr_count($translatedContent, $pronoun);
    if ($count > 0) {
        $firstPersonCount += $count;
        $firstPersonFound[] = trim($pronoun) . ": {$count}";
    }
}

// Check for third-person pronouns (should be abundant in third-person omniscient)
$thirdPersonPronouns = ['he ', ' he', 'she ', ' she', 'they ', ' they', 'him ', ' him', 'her ', ' her', 'them ', ' them', 'his ', ' his', 'hers ', ' hers', 'their ', ' their', 'theirs ', ' theirs'];
$thirdPersonCount = 0;
$thirdPersonFound = [];

foreach ($thirdPersonPronouns as $pronoun) {
    $count = substr_count($translatedContent, $pronoun);
    if ($count > 0) {
        $thirdPersonCount += $count;
        $thirdPersonFound[] = trim($pronoun) . ": {$count}";
    }
}

echo "First-person pronouns found: {$firstPersonCount}\n";
if (!empty($firstPersonFound)) {
    echo "- " . implode(', ', array_slice($firstPersonFound, 0, 10)) . "\n";
    if (count($firstPersonFound) > 10) {
        echo "- ... and " . (count($firstPersonFound) - 10) . " more\n";
    }
}

echo "\nThird-person pronouns found: {$thirdPersonCount}\n";
if (!empty($thirdPersonFound)) {
    echo "- " . implode(', ', array_slice($thirdPersonFound, 0, 10)) . "\n";
    if (count($thirdPersonFound) > 10) {
        echo "- ... and " . (count($thirdPersonFound) - 10) . " more\n";
    }
}

// Calculate POV ratio
$totalPronouns = $firstPersonCount + $thirdPersonCount;
if ($totalPronouns > 0) {
    $thirdPersonRatio = ($thirdPersonCount / $totalPronouns) * 100;
    echo "\nPOV Analysis:\n";
    echo "- Third-person ratio: " . round($thirdPersonRatio, 1) . "%\n";
    
    if ($thirdPersonRatio >= 80) {
        echo "✅ GOOD: Content appears to be in third-person perspective\n";
    } elseif ($thirdPersonRatio >= 50) {
        echo "⚠️ MIXED: Content has mixed perspective (may be dialogue heavy)\n";
    } else {
        echo "❌ ISSUE: Content appears to be primarily first-person\n";
    }
} else {
    echo "\n⚠️ No personal pronouns found in content\n";
}

// Show sample of content for manual inspection
echo "\n=== Content Sample (First 500 characters) ===\n";
echo substr($translatedContent, 0, 500) . "...\n\n";

// Check if chapter uses chunks
$chunks = $db->fetchAll(
    'SELECT id, chunk_number, translated_content, translation_status, updated_at
     FROM chapter_chunks 
     WHERE chapter_id = ? 
     ORDER BY chunk_number',
    [$chapter['id']]
);

if (!empty($chunks)) {
    echo "=== Chapter Chunks Analysis ===\n";
    echo "- Chapter uses chunked translation: " . count($chunks) . " chunks\n";
    
    foreach ($chunks as $chunk) {
        echo "- Chunk {$chunk['chunk_number']}: {$chunk['translation_status']} (updated: {$chunk['updated_at']})\n";
    }
    
    echo "\nNote: For chunked chapters, POV should be consistent across all chunks.\n";
} else {
    echo "=== Translation Method ===\n";
    echo "- Chapter uses single-pass translation (no chunks)\n";
}

echo "\n=== Recommendation ===\n";
if ($chapterPOV === 'third_person_omniscient' || $novelPOV === 'third_person_omniscient') {
    if ($thirdPersonRatio >= 80) {
        echo "✅ POV implementation appears to be working correctly.\n";
        echo "   The chapter is using third-person perspective as expected.\n";
    } else {
        echo "⚠️ POV implementation may need attention.\n";
        echo "   Expected third-person omniscient but found mixed/first-person content.\n";
        echo "   Consider re-translating this chapter with the fixed POV system.\n";
    }
} else {
    echo "ℹ️ Chapter is not set to use third-person omniscient POV.\n";
    echo "   Current setting: " . ($chapterPOV ?: $novelPOV ?: 'preserve_original') . "\n";
    echo "   To test the fix, set POV to 'third_person_omniscient' and re-translate.\n";
}

echo "\nDone.\n";
