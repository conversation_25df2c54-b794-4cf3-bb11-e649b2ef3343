<?php
/**
 * Check what honorific fragments DeepSeek detected
 */

$translation = "「Tou-san, thank you for your hard work,」 I said. <PERSON><PERSON><PERSON><PERSON><PERSON> smiled. <PERSON><PERSON>-chan came too.";

echo "=== Checking for Honorific Fragments ===\n\n";
echo "Translation: {$translation}\n\n";

// Check for broken fragments
if (preg_match_all('/\b-(?:san|chan|kun|sama)\b/', $translation, $matches, PREG_OFFSET_CAPTURE)) {
    echo "Found isolated honorific fragments:\n";
    foreach ($matches[0] as $match) {
        $fragment = $match[0];
        $position = $match[1];
        $context = substr($translation, max(0, $position - 10), 20);
        echo "  - '{$fragment}' at position {$position}: ...{$context}...\n";
    }
} else {
    echo "No isolated honorific fragments found.\n";
}

// Let's also check what the regex is actually matching
echo "\nDetailed regex analysis:\n";
echo "Pattern: /\\b-(?:san|chan|kun|sama)\\b/\n";
echo "Matches: ";
if (preg_match('/\b-(?:san|chan|kun|sama)\b/', $translation)) {
    echo "YES\n";
} else {
    echo "NO\n";
}

// Check each part separately
$parts = ['Tou-san', 'Kaa-san', 'Onii-chan'];
foreach ($parts as $part) {
    echo "Testing '{$part}': ";
    if (preg_match('/\b-(?:san|chan|kun|sama)\b/', $part)) {
        echo "MATCHES (false positive)\n";
    } else {
        echo "NO MATCH (correct)\n";
    }
}

echo "\n=== Analysis Complete ===\n";
