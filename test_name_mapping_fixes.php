<?php
/**
 * Test Name Mapping Fixes
 * Comprehensive test suite to verify that the name dictionary mapping issues have been resolved
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/NameSubstitutionService.php';

echo "=== Name Mapping Fixes Test Suite ===\n\n";

try {
    $nameService = new NameSubstitutionService();
    
    // Test 1: Conflicting Names (Substring Issue)
    echo "Test 1: Conflicting Names (Substring Issue)\n";
    echo "Testing names where one is a substring of another\n";
    
    $conflictingNames = [
        [
            'original_name' => '田中',
            'translation' => 'Tanaka',
            'name_type' => 'character',
            'frequency' => 10
        ],
        [
            'original_name' => '田中太郎',
            'translation' => 'Tanaka Taro',
            'name_type' => 'character',
            'frequency' => 5
        ]
    ];
    
    $testText = "<PERSON> Taro walked with <PERSON> to the store.";
    $originalText = "田中太郎は田中と一緒に店に歩いた。";
    
    $result = $nameService->applyNameSubstitutions($testText, $conflictingNames, $originalText);
    
    echo "Original: {$testText}\n";
    echo "Result: {$result['processed_text']}\n";
    echo "Substitutions: " . count($result['substitutions']) . "\n";
    
    // Check that longer name (Tanaka Taro) is preserved and shorter name (Tanaka) is handled correctly
    $hasCorrectTanakaMapping = strpos($result['processed_text'], 'Tanaka Taro') !== false;
    echo $hasCorrectTanakaMapping ? "✅ Longer name preserved correctly\n" : "❌ Longer name not preserved\n";
    echo "\n";
    
    // Test 2: Similar Names (Character Confusion)
    echo "Test 2: Similar Names (Character Confusion)\n";
    echo "Testing names that are similar but should not be confused\n";
    
    $similarNames = [
        [
            'original_name' => 'アリス',
            'translation' => 'Alice',
            'name_type' => 'character',
            'frequency' => 15
        ],
        [
            'original_name' => 'アリサ',
            'translation' => 'Alisa',
            'name_type' => 'character',
            'frequency' => 8
        ]
    ];
    
    $testText2 = "Alicia spoke to Alice about the plan.";
    $originalText2 = "アリサはアリスに計画について話した。";
    
    $result2 = $nameService->applyNameSubstitutions($testText2, $similarNames, $originalText2);
    
    echo "Original: {$testText2}\n";
    echo "Result: {$result2['processed_text']}\n";
    echo "Substitutions: " . count($result2['substitutions']) . "\n";
    
    // Check that Alice is not incorrectly changed to Alisa
    $alicePreserved = strpos($result2['processed_text'], 'Alice') !== false;
    $noIncorrectAlisa = strpos($result2['processed_text'], 'Alicia') === false || 
                       substr_count($result2['processed_text'], 'Alisa') <= substr_count($testText2, 'Alicia');
    
    echo $alicePreserved ? "✅ Alice preserved correctly\n" : "❌ Alice incorrectly changed\n";
    echo $noIncorrectAlisa ? "✅ No incorrect Alisa substitution\n" : "❌ Incorrect Alisa substitution detected\n";
    echo "\n";
    
    // Test 3: Generic Terms vs Character Names
    echo "Test 3: Generic Terms vs Character Names\n";
    echo "Testing that generic terms are not confused with character names\n";
    
    $mixedNames = [
        [
            'original_name' => '王',
            'translation' => 'King',
            'name_type' => 'character',
            'frequency' => 20
        ],
        [
            'original_name' => '王様',
            'translation' => 'King-sama',
            'name_type' => 'character',
            'frequency' => 5
        ]
    ];
    
    $testText3 = "The king of the land spoke to King about the kingdom.";
    $originalText3 = "その土地の王は王について王国について話した。";
    
    $result3 = $nameService->applyNameSubstitutions($testText3, $mixedNames, $originalText3);
    
    echo "Original: {$testText3}\n";
    echo "Result: {$result3['processed_text']}\n";
    echo "Substitutions: " . count($result3['substitutions']) . "\n";
    
    // Check that generic "the king" is not changed to character name
    $genericKingPreserved = strpos($result3['processed_text'], 'the king') !== false;
    echo $genericKingPreserved ? "✅ Generic 'the king' preserved\n" : "❌ Generic 'the king' incorrectly changed\n";
    echo "\n";
    
    // Test 4: Case Sensitivity and Capitalization
    echo "Test 4: Case Sensitivity and Capitalization\n";
    echo "Testing proper handling of case variations\n";
    
    $caseNames = [
        [
            'original_name' => 'サラ',
            'translation' => 'Sara',
            'name_type' => 'character',
            'frequency' => 12
        ]
    ];
    
    $testText4 = "sara met with Sara and SARA discussed the plan.";
    $originalText4 = "サラはサラと会い、サラは計画について話し合った。";
    
    $result4 = $nameService->applyNameSubstitutions($testText4, $caseNames, $originalText4);
    
    echo "Original: {$testText4}\n";
    echo "Result: {$result4['processed_text']}\n";
    echo "Substitutions: " . count($result4['substitutions']) . "\n";
    
    // Check that proper capitalization is maintained
    $properCapitalization = preg_match_all('/\bSara\b/', $result4['processed_text']) >= 1;
    echo $properCapitalization ? "✅ Proper capitalization maintained\n" : "❌ Capitalization issues detected\n";
    echo "\n";
    
    // Test 5: Priority and Frequency Handling
    echo "Test 5: Priority and Frequency Handling\n";
    echo "Testing that higher frequency names take priority\n";
    
    $priorityNames = [
        [
            'original_name' => 'リン',
            'translation' => 'Rin',
            'name_type' => 'character',
            'frequency' => 25
        ],
        [
            'original_name' => 'リンダ',
            'translation' => 'Linda',
            'name_type' => 'character',
            'frequency' => 3
        ]
    ];
    
    $testText5 = "Lynn spoke to Linda about the meeting.";
    $originalText5 = "リンはリンダに会議について話した。";
    
    $result5 = $nameService->applyNameSubstitutions($testText5, $priorityNames, $originalText5);
    
    echo "Original: {$testText5}\n";
    echo "Result: {$result5['processed_text']}\n";
    echo "Substitutions: " . count($result5['substitutions']) . "\n";
    
    // Check that high-frequency name is handled correctly
    $rinHandledCorrectly = strpos($result5['processed_text'], 'Rin') !== false;
    $lindaPreserved = strpos($result5['processed_text'], 'Linda') !== false;
    
    echo $rinHandledCorrectly ? "✅ High-frequency name handled correctly\n" : "❌ High-frequency name not handled correctly\n";
    echo $lindaPreserved ? "✅ Linda preserved correctly\n" : "❌ Linda not preserved correctly\n";
    echo "\n";
    
    // Test 6: Fuzzy Matching Improvements
    echo "Test 6: Fuzzy Matching Improvements\n";
    echo "Testing improved fuzzy matching with better thresholds\n";
    
    $fuzzyNames = [
        [
            'original_name' => 'エミリー',
            'translation' => 'Emily',
            'name_type' => 'character',
            'frequency' => 18
        ]
    ];
    
    $fuzzyMatches = $nameService->findFuzzyMatches("Emilie talked to Emma about Emily's plan.", $fuzzyNames);
    
    echo "Fuzzy matches found: " . count($fuzzyMatches) . "\n";
    foreach ($fuzzyMatches as $match) {
        echo "  - Found: '{$match['found_text']}' → '{$match['target_name']}' (similarity: " . number_format($match['similarity'], 3) . ", confidence: " . number_format($match['confidence'], 3) . ")\n";
    }
    
    // Check that only appropriate matches are found
    $appropriateMatches = true;
    foreach ($fuzzyMatches as $match) {
        if ($match['confidence'] < 0.6 || $match['found_text'] === 'Emma') {
            $appropriateMatches = false;
            break;
        }
    }
    
    echo $appropriateMatches ? "✅ Only appropriate fuzzy matches found\n" : "❌ Inappropriate fuzzy matches detected\n";
    echo "\n";
    
    echo "=== Test Suite Complete ===\n";
    echo "All tests completed. Check the debug.log file for detailed processing information.\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
