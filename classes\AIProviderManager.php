<?php
/**
 * AI Provider Management Service
 * Manages AI provider selection, routing, and configuration
 * Novel Translation Application
 */

class AIProviderManager {
    private $db;
    private $currentUserId;
    private $providers;
    private $activeProvider;
    private $fallbackProvider;

    public function __construct(int $userId = 1) {
        $this->db = Database::getInstance();
        $this->currentUserId = $userId;
        $this->providers = [];
        $this->loadProviderConfiguration();
    }

    /**
     * Load provider configuration from database and constants
     */
    private function loadProviderConfiguration(): void {
        // Get user's active provider preference
        $preference = $this->db->fetchOne(
            "SELECT * FROM ai_provider_preferences WHERE user_id = ? AND is_active = TRUE ORDER BY updated_at DESC LIMIT 1",
            [$this->currentUserId]
        );

        if ($preference) {
            $this->activeProvider = $preference['provider_type'];
        } else {
            $this->activeProvider = DEFAULT_AI_PROVIDER;
        }

        // Set fallback provider for mixed mode
        if ($this->activeProvider === 'mixed') {
            $this->fallbackProvider = MIXED_MODE_FALLBACK;
        }

        file_put_contents('debug.log', "AIProviderManager: Active provider: {$this->activeProvider}\n", FILE_APPEND);
    }

    /**
     * Get the appropriate translation service instance
     */
    public function getTranslationService(string $providerType = null): object {
        $provider = $providerType ?: $this->activeProvider;

        // Handle mixed mode
        if ($provider === 'mixed') {
            $provider = MIXED_MODE_PRIMARY;
        }

        switch ($provider) {
            case 'deepseek':
                if (!isset($this->providers['deepseek'])) {
                    $this->providers['deepseek'] = new DeepSeekTranslationService();
                }
                return $this->providers['deepseek'];

            case 'gemini_15':
                if (!isset($this->providers['gemini_15'])) {
                    $this->providers['gemini_15'] = new Gemini15TranslationService();
                }
                return $this->providers['gemini_15'];

            case 'gemini_20':
                if (!isset($this->providers['gemini_20'])) {
                    $this->providers['gemini_20'] = new Gemini20TranslationService();
                }
                return $this->providers['gemini_20'];

            case 'gemini_25':
                if (!isset($this->providers['gemini_25'])) {
                    $this->providers['gemini_25'] = new Gemini25TranslationService();
                }
                return $this->providers['gemini_25'];

            default:
                throw new Exception("Unsupported AI provider: {$provider}");
        }
    }

    /**
     * Get fallback translation service for mixed mode
     */
    public function getFallbackTranslationService(): object {
        if (!$this->fallbackProvider) {
            throw new Exception("No fallback provider configured");
        }

        return $this->getTranslationService($this->fallbackProvider);
    }

    /**
     * Translate text with automatic provider selection and fallback
     */
    public function translateText(string $text, string $targetLanguage = 'en', string $sourceLanguage = 'auto', array $context = []): array {
        $startTime = microtime(true);

        try {
            // Get primary translation service
            $primaryService = $this->getTranslationService();
            $primaryProvider = $this->activeProvider === 'mixed' ? MIXED_MODE_PRIMARY : $this->activeProvider;

            file_put_contents('debug.log', "AIProviderManager: Using primary provider: {$primaryProvider}\n", FILE_APPEND);

            // Attempt translation with primary provider
            $result = $primaryService->translateText($text, $targetLanguage, $sourceLanguage, $context);

            if ($result['success']) {
                // Log successful translation
                $this->logProviderUsage($primaryProvider, true, $result['execution_time'] ?? 0, mb_strlen($text));
                
                // Add provider information to result
                $result['provider_used'] = $primaryProvider;
                $result['fallback_used'] = false;
                
                return $result;
            }

            // If primary failed and we're in mixed mode, try fallback
            if ($this->activeProvider === 'mixed' && $this->fallbackProvider) {
                file_put_contents('debug.log', "AIProviderManager: Primary provider failed, trying fallback: {$this->fallbackProvider}\n", FILE_APPEND);

                $fallbackService = $this->getFallbackTranslationService();
                $fallbackResult = $fallbackService->translateText($text, $targetLanguage, $sourceLanguage, $context);

                // Log primary failure and fallback attempt
                $this->logProviderUsage($primaryProvider, false, $result['execution_time'] ?? 0, mb_strlen($text));

                if ($fallbackResult['success']) {
                    $this->logProviderUsage($this->fallbackProvider, true, $fallbackResult['execution_time'] ?? 0, mb_strlen($text));
                    
                    // Add provider information to result
                    $fallbackResult['provider_used'] = $this->fallbackProvider;
                    $fallbackResult['fallback_used'] = true;
                    $fallbackResult['primary_provider'] = $primaryProvider;
                    $fallbackResult['primary_error'] = $result['error'] ?? 'Unknown error';
                    
                    return $fallbackResult;
                } else {
                    $this->logProviderUsage($this->fallbackProvider, false, $fallbackResult['execution_time'] ?? 0, mb_strlen($text));
                }

                // Both providers failed
                return [
                    'success' => false,
                    'error' => "Both providers failed. Primary ({$primaryProvider}): " . ($result['error'] ?? 'Unknown error') . 
                              ". Fallback ({$this->fallbackProvider}): " . ($fallbackResult['error'] ?? 'Unknown error'),
                    'execution_time' => round(microtime(true) - $startTime, 2),
                    'provider_used' => $primaryProvider,
                    'fallback_used' => true,
                    'primary_error' => $result['error'] ?? 'Unknown error',
                    'fallback_error' => $fallbackResult['error'] ?? 'Unknown error'
                ];
            }

            // Single provider mode failed
            $this->logProviderUsage($primaryProvider, false, $result['execution_time'] ?? 0, mb_strlen($text));
            
            $result['provider_used'] = $primaryProvider;
            $result['fallback_used'] = false;
            
            return $result;

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;
            
            return [
                'success' => false,
                'error' => 'Provider manager error: ' . $e->getMessage(),
                'execution_time' => round($executionTime, 2),
                'provider_used' => $this->activeProvider,
                'fallback_used' => false
            ];
        }
    }

    /**
     * Get current active provider
     */
    public function getActiveProvider(): string {
        return $this->activeProvider;
    }

    /**
     * Set active provider for current user
     */
    public function setActiveProvider(string $providerType): bool {
        try {
            // Validate provider type
            if (!array_key_exists($providerType, SUPPORTED_AI_PROVIDERS)) {
                throw new Exception("Invalid provider type: {$providerType}");
            }

            // Deactivate current preferences
            $this->db->query(
                "UPDATE ai_provider_preferences SET is_active = FALSE WHERE user_id = ?",
                [$this->currentUserId]
            );

            // Check if preference already exists
            $existing = $this->db->fetchOne(
                "SELECT id FROM ai_provider_preferences WHERE user_id = ? AND provider_type = ?",
                [$this->currentUserId, $providerType]
            );

            if ($existing) {
                // Reactivate existing preference
                $this->db->update(
                    'ai_provider_preferences',
                    ['is_active' => true, 'updated_at' => date('Y-m-d H:i:s')],
                    'id = ?',
                    [$existing['id']]
                );
            } else {
                // Create new preference
                $this->db->insert('ai_provider_preferences', [
                    'user_id' => $this->currentUserId,
                    'provider_type' => $providerType,
                    'is_active' => true,
                    'configuration' => json_encode($this->getDefaultConfiguration($providerType))
                ]);
            }

            // Update current active provider
            $this->activeProvider = $providerType;
            
            file_put_contents('debug.log', "AIProviderManager: Active provider changed to: {$providerType}\n", FILE_APPEND);
            
            return true;

        } catch (Exception $e) {
            error_log("AIProviderManager: Failed to set active provider: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get default configuration for a provider
     */
    private function getDefaultConfiguration(string $providerType): array {
        $configs = [
            'deepseek' => [
                'model' => 'deepseek-chat',
                'temperature' => 0.3,
                'max_tokens' => 4096,
                'timeout' => 300
            ],
            'gemini_15' => [
                'model' => 'gemini-1.5-flash',
                'temperature' => 0.3,
                'topK' => 40,
                'topP' => 0.95,
                'maxOutputTokens' => 2048
            ],
            'gemini_20' => [
                'model' => 'gemini-2.0-flash',
                'temperature' => 0.25,
                'topK' => 50,
                'topP' => 0.9,
                'maxOutputTokens' => 3072
            ],
            'gemini_25' => [
                'model' => 'gemini-2.5-flash-preview-05-20',
                'temperature' => 0.2,
                'topK' => 60,
                'topP' => 0.85,
                'maxOutputTokens' => 4096
            ],
            'mixed' => [
                'primary' => MIXED_MODE_PRIMARY,
                'fallback' => MIXED_MODE_FALLBACK,
                'auto_fallback' => true
            ]
        ];

        return $configs[$providerType] ?? [];
    }

    /**
     * Log provider usage statistics
     */
    private function logProviderUsage(string $providerType, bool $success, float $executionTime, int $characterCount): void {
        try {
            $today = date('Y-m-d');
            
            // Check if record exists for today
            $existing = $this->db->fetchOne(
                "SELECT * FROM ai_provider_usage_stats WHERE provider_type = ? AND usage_date = ?",
                [$providerType, $today]
            );

            if ($existing) {
                // Update existing record
                $updates = [
                    'translation_count' => $existing['translation_count'] + 1,
                    'character_count' => $existing['character_count'] + $characterCount,
                    'total_execution_time' => $existing['total_execution_time'] + $executionTime
                ];

                if ($success) {
                    $updates['success_count'] = $existing['success_count'] + 1;
                } else {
                    $updates['error_count'] = $existing['error_count'] + 1;
                }

                $this->db->update('ai_provider_usage_stats', $updates, 'id = ?', [$existing['id']]);
            } else {
                // Create new record
                $this->db->insert('ai_provider_usage_stats', [
                    'provider_type' => $providerType,
                    'usage_date' => $today,
                    'translation_count' => 1,
                    'character_count' => $characterCount,
                    'success_count' => $success ? 1 : 0,
                    'error_count' => $success ? 0 : 1,
                    'total_execution_time' => $executionTime
                ]);
            }

        } catch (Exception $e) {
            error_log("AIProviderManager: Failed to log usage statistics: " . $e->getMessage());
        }
    }

    /**
     * Get provider usage statistics
     */
    public function getUsageStatistics(int $days = 30): array {
        $startDate = date('Y-m-d', strtotime("-{$days} days"));
        
        return $this->db->fetchAll(
            "SELECT * FROM ai_provider_usage_stats 
             WHERE usage_date >= ? 
             ORDER BY usage_date DESC, provider_type ASC",
            [$startDate]
        );
    }

    /**
     * Get available providers
     */
    public function getAvailableProviders(): array {
        return SUPPORTED_AI_PROVIDERS;
    }

    /**
     * Test provider connection
     */
    public function testProvider(string $providerType): array {
        try {
            $service = $this->getTranslationService($providerType);
            
            // Simple test translation
            $testResult = $service->translateText(
                'こんにちは',
                'en',
                'ja',
                ['simple' => true, 'type' => 'test']
            );

            return [
                'success' => $testResult['success'],
                'provider' => $providerType,
                'execution_time' => $testResult['execution_time'] ?? 0,
                'error' => $testResult['error'] ?? null
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'provider' => $providerType,
                'execution_time' => 0,
                'error' => $e->getMessage()
            ];
        }
    }
}
