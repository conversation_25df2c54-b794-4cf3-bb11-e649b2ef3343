<?php
/**
 * Test force 3rd person with actual chapter 171 content
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/DeepSeekTranslationService.php';

echo "=== Testing Chapter 171 with Force 3rd Person ===\n\n";

$db = Database::getInstance();

// Get chapter 171 first chunk
$chapter = $db->fetchOne(
    'SELECT id, novel_id FROM chapters WHERE novel_id = 1 AND chapter_number = 171',
    []
);

if (!$chapter) {
    echo "❌ Chapter 171 not found\n";
    exit;
}

$firstChunk = $db->fetchOne(
    'SELECT id, chunk_number, original_content FROM chapter_chunks 
     WHERE chapter_id = ? ORDER BY chunk_number LIMIT 1',
    [$chapter['id']]
);

if (!$firstChunk) {
    echo "❌ No chunks found for chapter 171\n";
    exit;
}

echo "Testing with:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Chunk ID: {$firstChunk['id']}\n";
echo "- Chunk Number: {$firstChunk['chunk_number']}\n";
echo "- Content length: " . strlen($firstChunk['original_content']) . " characters\n\n";

// Show first 200 characters of original content
echo "Original content preview:\n";
echo substr($firstChunk['original_content'], 0, 200) . "...\n\n";

// Create narrative context that triggers force 3rd person
$narrativeContext = [
    'narrative_voice' => 'first_person',
    'narrative_confidence' => 0.8,
    'perspective_indicators' => [
        'first_person' => ['私は', '我々は', '私の'],
        'third_person' => []
    ]
];

$fullContext = [
    'type' => 'chunk',
    'user_pov_preference' => 'third_person_omniscient',
    'narrative_context' => $narrativeContext
];

echo "=== Testing Translation ===\n";

try {
    $deepSeekService = new DeepSeekTranslationService();
    
    // Translate a smaller portion for testing (first 500 characters)
    $testContent = substr($firstChunk['original_content'], 0, 500);
    
    echo "Translating first 500 characters...\n";
    
    $result = $deepSeekService->translateText($testContent, 'en', 'auto', $fullContext);
    
    if ($result['success']) {
        echo "✅ Translation successful!\n\n";
        echo "Translated text:\n";
        echo "================\n";
        echo $result['translated_text'];
        echo "\n================\n\n";
        
        // Analyze the result for POV
        $translatedText = $result['translated_text'];
        
        // Count first-person pronouns
        $firstPersonPronouns = ['I ', ' I ', 'I\'', 'me ', ' me', 'my ', ' my', 'we ', ' we', 'us ', ' us', 'our ', ' our'];
        $firstPersonCount = 0;
        $firstPersonFound = [];
        
        foreach ($firstPersonPronouns as $pronoun) {
            $count = substr_count($translatedText, $pronoun);
            if ($count > 0) {
                $firstPersonCount += $count;
                $firstPersonFound[] = trim($pronoun) . ": {$count}";
            }
        }
        
        // Count third-person pronouns
        $thirdPersonPronouns = ['he ', ' he', 'she ', ' she', 'they ', ' they', 'him ', ' him', 'her ', ' her', 'them ', ' them', 'his ', ' his', 'their ', ' their'];
        $thirdPersonCount = 0;
        $thirdPersonFound = [];
        
        foreach ($thirdPersonPronouns as $pronoun) {
            $count = substr_count($translatedText, $pronoun);
            if ($count > 0) {
                $thirdPersonCount += $count;
                $thirdPersonFound[] = trim($pronoun) . ": {$count}";
            }
        }
        
        echo "=== POV Analysis ===\n";
        echo "First-person pronouns: {$firstPersonCount}\n";
        if (!empty($firstPersonFound)) {
            echo "- " . implode(', ', $firstPersonFound) . "\n";
        }
        
        echo "Third-person pronouns: {$thirdPersonCount}\n";
        if (!empty($thirdPersonFound)) {
            echo "- " . implode(', ', $thirdPersonFound) . "\n";
        }
        
        $totalPronouns = $firstPersonCount + $thirdPersonCount;
        if ($totalPronouns > 0) {
            $thirdPersonRatio = ($thirdPersonCount / $totalPronouns) * 100;
            echo "Third-person ratio: " . round($thirdPersonRatio, 1) . "%\n";
            
            if ($firstPersonCount == 0 && $thirdPersonCount > 0) {
                echo "✅ PERFECT: Force 3rd person working perfectly!\n";
            } elseif ($thirdPersonRatio >= 80) {
                echo "✅ GOOD: Mostly third-person perspective\n";
            } elseif ($thirdPersonRatio >= 50) {
                echo "⚠️ MIXED: Mixed perspective (may include dialogue)\n";
            } else {
                echo "❌ ISSUE: Still primarily first-person\n";
            }
        } else {
            echo "⚠️ No personal pronouns found\n";
        }
        
    } else {
        echo "❌ Translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during translation: " . $e->getMessage() . "\n";
}

echo "\n=== Recommendation ===\n";
echo "If the force 3rd person is working correctly, you can now:\n";
echo "1. Clear chapter 171 translation\n";
echo "2. Translate each part/chunk again\n";
echo "3. The result should use third-person perspective consistently\n";
echo "4. No POV preference setting needed - force 3rd person is automatic\n";

echo "\nDone.\n";
