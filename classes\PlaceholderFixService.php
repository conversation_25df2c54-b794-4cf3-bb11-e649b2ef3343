<?php
/**
 * Service to fix AI-generated placeholder patterns in translations
 * Handles patterns like [-chan7], [-kun1], etc. that the AI creates when it's unsure about translations
 */

class PlaceholderFixService {
    
    /**
     * Fix AI-generated placeholder patterns in translated text
     */
    public function fixPlaceholders(string $translatedText, string $originalText = '', array $context = []): array {
        $fixedText = $translatedText;
        $fixes = [];
        
        // Find all placeholder patterns like [-chan7], [-kun1], etc. (must have numbers)
        if (preg_match_all('/\[-([\w]+)(\d+)\]/', $translatedText, $matches, PREG_OFFSET_CAPTURE)) {
            
            foreach ($matches[0] as $index => $match) {
                $fullPattern = $match[0];
                $position = $match[1];
                $honorificType = $matches[1][$index][0]; // chan, kun, san, etc.
                $number = $matches[2][$index][0]; // the number part
                
                // Try to find the appropriate replacement
                $replacement = $this->findReplacement($fullPattern, $honorificType, $number, $originalText, $context);
                
                if ($replacement !== null) {
                    $fixedText = str_replace($fullPattern, $replacement, $fixedText);
                    $fixes[] = [
                        'pattern' => $fullPattern,
                        'replacement' => $replacement,
                        'position' => $position,
                        'type' => $honorificType
                    ];
                }
            }
        }
        
        return [
            'text' => $fixedText,
            'fixes' => $fixes,
            'fixed_count' => count($fixes)
        ];
    }
    
    /**
     * Find appropriate replacement for a placeholder pattern
     */
    private function findReplacement(string $pattern, string $honorificType, string $number, string $originalText, array $context): ?string {
        
        // Handle different honorific types
        switch ($honorificType) {
            case 'chan':
                // For -chan, first try family terms, then character names
                $replacement = $this->findFamilyTermReplacement($pattern, $originalText, $context);
                if ($replacement === null) {
                    $replacement = $this->findCharacterNameReplacement($pattern, $originalText, $context);
                }
                break;

            case 'kun':
            case 'san':
            case 'sama':
                // For other honorifics, try character names first
                $replacement = $this->findCharacterNameReplacement($pattern, $originalText, $context);
                break;

            default:
                $replacement = null;
        }

        if ($replacement !== null) {
            return $replacement;
        }
        
        // Fallback: use generic replacement
        return $this->getGenericReplacement($honorificType, $number);
    }
    
    /**
     * Find family term replacement (like mother, father, etc.)
     */
    private function findFamilyTermReplacement(string $pattern, string $originalText, array $context): ?string {
        
        // Common Japanese family terms that might become placeholders
        $familyTerms = [
            'お母さん' => 'mother',
            'おかあさん' => 'mother', 
            'ママ' => 'mom',
            'お父さん' => 'father',
            'おとうさん' => 'father',
            'パパ' => 'dad',
            'お姉さん' => 'older sister',
            'おねえさん' => 'older sister',
            'お兄さん' => 'older brother',
            'おにいさん' => 'older brother',
            'おばあさん' => 'grandmother',
            'おじいさん' => 'grandfather',
            'おばさん' => 'aunt',
            'おじさん' => 'uncle'
        ];
        
        // Search for these terms in the original text
        foreach ($familyTerms as $japanese => $english) {
            if (mb_strpos($originalText, $japanese) !== false) {
                return $english;
            }
        }
        
        // If we can't find a specific term, default to common family terms
        return 'mother'; // Most common case based on the example
    }
    
    /**
     * Find character name replacement
     */
    private function findCharacterNameReplacement(string $pattern, string $originalText, array $context): ?string {
        
        // Try to find character names with honorifics in the original text
        $namePatterns = [
            '/([ぁ-んァ-ヶ一-龯]{2,6})ちゃん/u',
            '/([ぁ-んァ-ヶ一-龯]{2,6})くん/u',
            '/([ぁ-んァ-ヶ一-龯]{2,6})さん/u',
            '/([ぁ-んァ-ヶ一-龯]{2,6})様/u',
            '/([ぁ-んァ-ヶ一-龯]{2,6})君/u'  // Added: name+君 pattern (not standalone 君)
        ];
        
        $foundNames = [];
        foreach ($namePatterns as $namePattern) {
            if (preg_match_all($namePattern, $originalText, $matches)) {
                $foundNames = array_merge($foundNames, $matches[1]);
            }
        }
        
        // If we have names from the context (name dictionary), try to match them with the original text
        if (isset($context['names']) && !empty($context['names'])) {
            // Try to find which name from the dictionary appears in the original text
            foreach ($context['names'] as $nameEntry) {
                $originalName = $nameEntry['original_name'];
                if (mb_strpos($originalText, $originalName) !== false) {
                    // This name appears in the original text, use its translation
                    if (isset($nameEntry['translation']) && !empty($nameEntry['translation'])) {
                        return $nameEntry['translation'];
                    } elseif (isset($nameEntry['romanization']) && !empty($nameEntry['romanization'])) {
                        return $nameEntry['romanization'];
                    }
                }
            }

            // If no specific match found, use the first available name as fallback
            foreach ($context['names'] as $nameEntry) {
                if (isset($nameEntry['translation']) && !empty($nameEntry['translation'])) {
                    return $nameEntry['translation'];
                } elseif (isset($nameEntry['romanization']) && !empty($nameEntry['romanization'])) {
                    return $nameEntry['romanization'];
                }
            }
        }
        
        // Use found names if available
        if (!empty($foundNames)) {
            // Return the first unique name found
            $uniqueNames = array_unique($foundNames);
            return $uniqueNames[0]; // Return romanized version
        }
        
        return null;
    }
    
    /**
     * Get generic replacement when specific replacement can't be found
     */
    private function getGenericReplacement(string $honorificType, string $number): string {

        // More specific generic replacements based on honorific type
        $genericReplacements = [
            'chan' => 'her',      // -chan is often used for girls/women or family members
            'kun' => 'him',       // -kun is often used for boys/men
            'san' => 'them',      // -san is neutral, formal
            'sama' => 'them'      // -sama is very formal, neutral
        ];

        return $genericReplacements[$honorificType] ?? 'someone';
    }
    
    /**
     * Log placeholder fixes for debugging
     */
    public function logFixes(array $fixes, string $context = ''): void {
        if (!empty($fixes)) {
            $logMessage = "PlaceholderFixService: Fixed " . count($fixes) . " placeholder patterns";
            if (!empty($context)) {
                $logMessage .= " in $context";
            }
            $logMessage .= "\n";
            
            foreach ($fixes as $fix) {
                $logMessage .= "  - {$fix['pattern']} → {$fix['replacement']} (type: {$fix['type']})\n";
            }
            
            file_put_contents('debug.log', $logMessage, FILE_APPEND);
        }
    }
}
?>
