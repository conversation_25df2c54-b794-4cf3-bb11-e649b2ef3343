<?php
/**
 * Debug POV preference storage and retrieval issues
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/POVPreferenceManager.php';

echo "=== Debugging POV Preference Issues ===\n\n";

$db = Database::getInstance();
$povManager = new POVPreferenceManager();

// Check if pov_preferences table exists
echo "=== Database Table Check ===\n";
try {
    $tables = $db->fetchAll("SHOW TABLES LIKE 'pov_preferences'");
    if (empty($tables)) {
        echo "❌ pov_preferences table does not exist!\n";
        echo "Creating table...\n";
        
        $createTableSQL = "
        CREATE TABLE pov_preferences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            novel_id INT NOT NULL,
            chapter_id INT NULL,
            pov_preference VARCHAR(50) NOT NULL,
            is_default BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
            FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
            UNIQUE KEY unique_novel_default (novel_id, is_default),
            UNIQUE KEY unique_chapter_pov (novel_id, chapter_id),
            INDEX idx_novel_pov (novel_id),
            INDEX idx_chapter_pov (chapter_id)
        )";
        
        $db->query($createTableSQL);
        echo "✅ pov_preferences table created\n";
    } else {
        echo "✅ pov_preferences table exists\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking/creating table: " . $e->getMessage() . "\n";
}

// Check current POV preferences for novel 1
echo "\n=== Current POV Preferences for Novel 1 ===\n";
try {
    $preferences = $db->fetchAll(
        "SELECT * FROM pov_preferences WHERE novel_id = 1 ORDER BY created_at DESC",
        []
    );
    
    if (empty($preferences)) {
        echo "❌ No POV preferences found for novel 1\n";
    } else {
        echo "Found " . count($preferences) . " POV preference(s):\n";
        foreach ($preferences as $pref) {
            echo "- ID: {$pref['id']}, Chapter: " . ($pref['chapter_id'] ?: 'DEFAULT') . 
                 ", POV: {$pref['pov_preference']}, Default: " . ($pref['is_default'] ? 'YES' : 'NO') . 
                 ", Created: {$pref['created_at']}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error fetching preferences: " . $e->getMessage() . "\n";
}

// Test setting POV preference for novel 1
echo "\n=== Testing POV Preference Setting ===\n";
try {
    echo "Setting novel 1 POV to 'third_person_omniscient'...\n";
    $result = $povManager->setNovelPOVPreference(1, 'third_person_omniscient');
    
    if ($result) {
        echo "✅ POV preference set successfully\n";
        
        // Verify it was saved
        $savedPOV = $povManager->getNovelPOVPreference(1);
        echo "Verification: Novel POV = " . ($savedPOV ?: 'NULL') . "\n";
        
        // Check chapter 171 POV
        $chapterPOV = $povManager->getChapterPOVPreference(1, 171);
        echo "Chapter 171 POV = " . ($chapterPOV ?: 'NULL') . "\n";
    } else {
        echo "❌ Failed to set POV preference\n";
    }
} catch (Exception $e) {
    echo "❌ Error setting POV preference: " . $e->getMessage() . "\n";
}

// Check the latest translation logs to see if POV was used
echo "\n=== Recent Translation Logs ===\n";
try {
    $logs = $db->fetchAll(
        "SELECT * FROM translation_logs 
         WHERE novel_id = 1 AND chapter_id = 171 
         ORDER BY created_at DESC LIMIT 5",
        []
    );
    
    if (empty($logs)) {
        echo "No translation logs found for chapter 171\n";
    } else {
        echo "Found " . count($logs) . " recent translation log(s):\n";
        foreach ($logs as $log) {
            echo "- ID: {$log['id']}, Type: {$log['translation_type']}, Status: {$log['status']}, Time: {$log['created_at']}\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error fetching translation logs: " . $e->getMessage() . "\n";
}

// Check debug log for POV-related entries
echo "\n=== Debug Log Check ===\n";
$debugLogPath = 'debug.log';
if (file_exists($debugLogPath)) {
    $debugContent = file_get_contents($debugLogPath);
    $povLines = [];
    
    $lines = explode("\n", $debugContent);
    foreach ($lines as $line) {
        if (stripos($line, 'pov') !== false || stripos($line, 'perspective') !== false) {
            $povLines[] = $line;
        }
    }
    
    if (empty($povLines)) {
        echo "No POV-related entries found in debug log\n";
    } else {
        echo "Found " . count($povLines) . " POV-related entries:\n";
        foreach (array_slice($povLines, -10) as $line) {
            echo "- " . trim($line) . "\n";
        }
    }
} else {
    echo "Debug log file not found\n";
}

// Test the perspective service directly
echo "\n=== Testing PerspectiveService Directly ===\n";
try {
    require_once 'classes/PerspectiveService.php';
    $perspectiveService = new PerspectiveService();
    
    $testContent = "I think this is a test. We should check if it works.";
    $testContext = ['user_pov_preference' => 'third_person_omniscient'];
    
    echo "Test content: '{$testContent}'\n";
    echo "Test context: " . json_encode($testContext) . "\n";
    
    $result = $perspectiveService->applyUserPOVPreference($testContent, $testContext);
    
    echo "PerspectiveService result:\n";
    echo "- Content Type: {$result['content_type']}\n";
    echo "- Optimal Perspective: {$result['optimal_perspective']}\n";
    echo "- User Selected: " . ($result['user_selected'] ? 'YES' : 'NO') . "\n";
    echo "- Instructions: " . substr($result['instructions'], 0, 200) . "...\n";
    
} catch (Exception $e) {
    echo "❌ Error testing PerspectiveService: " . $e->getMessage() . "\n";
}

echo "\nDone.\n";
