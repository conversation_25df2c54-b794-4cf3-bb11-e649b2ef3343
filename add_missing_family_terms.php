<?php
/**
 * Add Missing Family Terms to Name Dictionary
 * Ensures all common family terms are in the dictionary with correct translations
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Adding Missing Family Terms to Name Dictionary ===\n\n";

$db = Database::getInstance();

// Define family terms that should be in the dictionary
$familyTermsToAdd = [
    // Basic family terms
    'お母さん' => ['romanization' => 'okaa-san', 'translation' => 'mother-san', 'type' => 'character'],
    '母' => ['romanization' => 'haha', 'translation' => 'mother', 'type' => 'character'],
    'お父様' => ['romanization' => 'otou-sama', 'translation' => 'father-sama', 'type' => 'character'],
    'お母様' => ['romanization' => 'okaa-sama', 'translation' => 'mother-sama', 'type' => 'character'],
    'お兄ちゃん' => ['romanization' => 'onii-chan', 'translation' => 'big brother-chan', 'type' => 'character'],
    'お姉ちゃん' => ['romanization' => 'onee-chan', 'translation' => 'big sister-chan', 'type' => 'character'],
    'お兄さん' => ['romanization' => 'onii-san', 'translation' => 'big brother-san', 'type' => 'character'],
    'お姉さん' => ['romanization' => 'onee-san', 'translation' => 'big sister-san', 'type' => 'character'],
    'おじさん' => ['romanization' => 'ojisan', 'translation' => 'uncle-san', 'type' => 'character'],
    'おばさん' => ['romanization' => 'obasan', 'translation' => 'aunt-san', 'type' => 'character'],
    'おじいさん' => ['romanization' => 'ojiisan', 'translation' => 'grandfather-san', 'type' => 'character'],
    'おばあさん' => ['romanization' => 'obaasan', 'translation' => 'grandmother-san', 'type' => 'character'],
    'おじいちゃん' => ['romanization' => 'ojiichan', 'translation' => 'grandfather-chan', 'type' => 'character'],
    'おばあちゃん' => ['romanization' => 'obaachan', 'translation' => 'grandmother-chan', 'type' => 'character']
];

$novelId = 7; // Novel ID for testing
$addedCount = 0;
$updatedCount = 0;
$skippedCount = 0;

foreach ($familyTermsToAdd as $originalName => $termData) {
    // Check if this term already exists
    $existing = $db->fetchOne(
        'SELECT id, original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = ? AND original_name = ?',
        [$novelId, $originalName]
    );
    
    if ($existing) {
        echo "Found existing: {$originalName}\n";
        echo "  Current: romanization='{$existing['romanization']}', translation='{$existing['translation']}'\n";
        echo "  Target:  romanization='{$termData['romanization']}', translation='{$termData['translation']}'\n";
        
        // Check if we need to update
        $needsUpdate = false;
        $updates = [];
        
        if ($existing['translation'] !== $termData['translation']) {
            $updates['translation'] = $termData['translation'];
            $needsUpdate = true;
        }
        
        if ($existing['romanization'] !== $termData['romanization']) {
            $updates['romanization'] = $termData['romanization'];
            $needsUpdate = true;
        }
        
        if ($needsUpdate) {
            $updates['updated_at'] = date('Y-m-d H:i:s');
            
            $updated = $db->update(
                'name_dictionary',
                $updates,
                'id = ?',
                [$existing['id']]
            );
            
            if ($updated > 0) {
                echo "  ✓ Updated\n";
                $updatedCount++;
            } else {
                echo "  ✗ Update failed\n";
            }
        } else {
            echo "  ✓ Already correct\n";
            $skippedCount++;
        }
    } else {
        echo "Adding new: {$originalName} → {$termData['translation']}\n";
        
        // Insert new entry
        $inserted = $db->insert('name_dictionary', [
            'novel_id' => $novelId,
            'original_name' => $originalName,
            'romanization' => $termData['romanization'],
            'translation' => $termData['translation'],
            'name_type' => $termData['type'],
            'frequency' => 1,
            'first_appearance_chapter' => null,
            'is_verified' => false
        ]);
        
        if ($inserted) {
            echo "  ✓ Added successfully\n";
            $addedCount++;
        } else {
            echo "  ✗ Failed to add\n";
        }
    }
    echo "\n";
}

echo "=== Summary ===\n";
echo "New entries added: {$addedCount}\n";
echo "Existing entries updated: {$updatedCount}\n";
echo "Entries already correct: {$skippedCount}\n";
echo "Total processed: " . ($addedCount + $updatedCount + $skippedCount) . "\n";

echo "\n✓ Family term dictionary update complete!\n";
echo "=== Complete ===\n";
