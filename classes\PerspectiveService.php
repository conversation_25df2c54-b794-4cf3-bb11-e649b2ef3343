<?php

/**
 * Intelligent Perspective Detection and Management Service
 * 
 * This service provides intelligent point-of-view detection and management
 * for translation processes, determining the most appropriate perspective
 * based on content type and context.
 */
class PerspectiveService {
    
    /**
     * Content type constants for perspective determination
     */
    const CONTENT_TYPE_UI = 'ui';
    const CONTENT_TYPE_ERROR = 'error';
    const CONTENT_TYPE_NOTIFICATION = 'notification';
    const CONTENT_TYPE_DOCUMENTATION = 'documentation';
    const CONTENT_TYPE_HELP = 'help';
    const CONTENT_TYPE_NARRATIVE = 'narrative';
    const CONTENT_TYPE_DIALOGUE = 'dialogue';
    const CONTENT_TYPE_SYSTEM = 'system';
    const CONTENT_TYPE_TUTORIAL = 'tutorial';
    const CONTENT_TYPE_MENU = 'menu';
    
    /**
     * Perspective constants
     */
    const PERSPECTIVE_FIRST_PERSON = 'first_person';
    const PERSPECTIVE_SECOND_PERSON = 'second_person';
    const PERSPECTIVE_THIRD_PERSON = 'third_person';
    const PERSPECTIVE_THIRD_PERSON_LIMITED = 'third_person_limited';
    const PERSPECTIVE_THIRD_PERSON_OMNISCIENT = 'third_person_omniscient';
    const PERSPECTIVE_PRESERVE_ORIGINAL = 'preserve_original';
    
    /**
     * Analyze content and determine the most appropriate perspective
     */
    public function determineOptimalPerspective(string $content, array $context = []): array {
        // Check if user has specified a POV preference
        if (isset($context['user_pov_preference']) && !empty($context['user_pov_preference'])) {
            return $this->applyUserPOVPreference($content, $context);
        }

        // Detect content type first
        $contentType = $this->detectContentType($content, $context);

        // Analyze original perspective
        $originalPerspective = $this->analyzeOriginalPerspective($content);

        // Determine optimal perspective based on content type and context
        $optimalPerspective = $this->getOptimalPerspectiveForContentType($contentType, $originalPerspective, $context);

        // Generate perspective instructions
        $instructions = $this->generatePerspectiveInstructions($contentType, $optimalPerspective, $originalPerspective, $context);

        return [
            'content_type' => $contentType,
            'original_perspective' => $originalPerspective,
            'optimal_perspective' => $optimalPerspective,
            'instructions' => $instructions,
            'confidence' => $this->calculateConfidence($contentType, $originalPerspective, $content),
            'reasoning' => $this->generateReasoning($contentType, $optimalPerspective, $originalPerspective)
        ];
    }

    /**
     * Apply user-selected POV preference, overriding automatic detection
     */
    public function applyUserPOVPreference(string $content, array $context = []): array {
        $userPOV = $context['user_pov_preference'];
        $contentType = $this->detectContentType($content, $context);
        $originalPerspective = $this->analyzeOriginalPerspective($content);

        // Validate user POV preference
        $validPerspectives = [
            self::PERSPECTIVE_FIRST_PERSON,
            self::PERSPECTIVE_SECOND_PERSON,
            self::PERSPECTIVE_THIRD_PERSON,
            self::PERSPECTIVE_THIRD_PERSON_LIMITED,
            self::PERSPECTIVE_THIRD_PERSON_OMNISCIENT,
            self::PERSPECTIVE_PRESERVE_ORIGINAL
        ];

        if (!in_array($userPOV, $validPerspectives)) {
            // Invalid POV preference, fall back to automatic detection
            $userPOV = self::PERSPECTIVE_PRESERVE_ORIGINAL;
        }

        // Generate perspective instructions based on user preference
        $instructions = $this->generateUserPOVInstructions($contentType, $userPOV, $originalPerspective, $context);

        return [
            'content_type' => $contentType,
            'original_perspective' => $originalPerspective,
            'optimal_perspective' => $userPOV,
            'instructions' => $instructions,
            'confidence' => 1.0, // High confidence since it's user-selected
            'reasoning' => $this->generateUserPOVReasoning($userPOV, $originalPerspective),
            'user_selected' => true
        ];
    }
    
    /**
     * Detect the type of content to determine appropriate perspective handling
     */
    private function detectContentType(string $content, array $context = []): string {
        // Check explicit context type first
        if (isset($context['content_type'])) {
            return $context['content_type'];
        }
        
        // Check translation context type
        if (isset($context['type'])) {
            switch ($context['type']) {
                case 'chapter':
                case 'content':
                case 'chunk':
                    return self::CONTENT_TYPE_NARRATIVE;
                case 'title':
                    return self::CONTENT_TYPE_NARRATIVE;
                case 'synopsis':
                    return self::CONTENT_TYPE_DOCUMENTATION;
            }
        }
        
        // Analyze content patterns to detect type
        $contentLower = mb_strtolower($content);

        // Check patterns in order of specificity (most specific first)

        // Error message patterns (high priority - very specific)
        if ($this->containsErrorPatterns($content)) {
            return self::CONTENT_TYPE_ERROR;
        }

        // System notification patterns (high priority - specific)
        if ($this->containsNotificationPatterns($content)) {
            return self::CONTENT_TYPE_NOTIFICATION;
        }

        // Dialogue patterns (high priority - very specific)
        if ($this->containsDialoguePatterns($content)) {
            return self::CONTENT_TYPE_DIALOGUE;
        }

        // Tutorial/Instructional patterns (medium priority)
        if ($this->containsTutorialPatterns($content)) {
            return self::CONTENT_TYPE_TUTORIAL;
        }

        // Documentation/Help patterns (medium priority)
        if ($this->containsDocumentationPatterns($content)) {
            return self::CONTENT_TYPE_DOCUMENTATION;
        }

        // Menu/Navigation patterns (medium priority)
        if ($this->containsMenuPatterns($content)) {
            return self::CONTENT_TYPE_MENU;
        }

        // UI/Interface patterns (lower priority - can be broad)
        if ($this->containsUIPatterns($content)) {
            return self::CONTENT_TYPE_UI;
        }

        // Default to narrative for novel content
        return self::CONTENT_TYPE_NARRATIVE;
    }
    
    /**
     * Check if content contains UI/interface patterns
     */
    private function containsUIPatterns(string $content): bool {
        $uiPatterns = [
            // Button/action patterns with context
            '/(?:click|tap|press).*(?:button|link|icon|tab|here|to)/i',
            '/(?:select|choose).*(?:option|item|dropdown|checkbox)/i',
            '/(?:enable|disable|toggle).*(?:setting|option|feature)/i',

            // Direct action commands (imperative UI instructions)
            '/^(?:click|tap|press|select|choose|enter|type|fill|save|cancel|submit|reset)/i',

            // Form interaction patterns
            '/(?:enter|input|type).*(?:your|the).*(?:username|password|email|name|address|phone|text|value)/i',
            '/(?:fill|complete).*(?:form|field|information)/i',
            '/(?:required|optional).*(?:field|information|data)/i',

            // Navigation with context
            '/(?:go to|navigate to|return to|back to).*(?:page|section|menu|home)/i',
            '/(?:save|cancel|ok|apply|confirm|submit|reset).*(?:changes|form|data|settings|work)/i',
        ];

        foreach ($uiPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Check if content contains error message patterns
     */
    private function containsErrorPatterns(string $content): bool {
        $errorPatterns = [
            '/^error\s*:/i',  // Starts with "Error:"
            '/(?:error|failed|failure|invalid|incorrect|wrong).*(?:please|try|check)/i',
            '/(?:cannot|can\'t|unable to|failed to|could not).*(?:connect|access|load|save)/i',
            '/(?:please try|try again|check|verify|ensure).*(?:again|connection|input)/i',
            '/(?:required|missing|empty|blank|not found).*(?:field|value|file|data)/i',
        ];

        foreach ($errorPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Check if content contains system notification patterns
     */
    private function containsNotificationPatterns(string $content): bool {
        $notificationPatterns = [
            '/(?:has been|was|will be|is now).*(?:successfully|completed|saved|updated|deleted)/i',
            '/(?:successfully|completed|finished).*(?:saved|updated|processed|sent)/i',
            '/(?:new|updated|changed|modified).*(?:message|file|data|content)/i',
            '/(?:notification|alert|reminder).*(?:received|sent|scheduled)/i',
        ];

        foreach ($notificationPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Check if content contains documentation patterns
     */
    private function containsDocumentationPatterns(string $content): bool {
        $docPatterns = [
            '/(?:documentation|guide|manual).*(?:will help|explains|describes|covers)/i',
            '/(?:overview|introduction).*(?:to|of).*(?:system|application|feature)/i',
            '/(?:this\s+(?:guide|manual|documentation)).*(?:will|explains|describes)/i',
            '/(?:reference|api|technical).*(?:documentation|guide|manual)/i',
        ];

        foreach ($docPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Check if content contains tutorial patterns
     */
    private function containsTutorialPatterns(string $content): bool {
        $tutorialPatterns = [
            '/(?:step\s+\d+|first\s+step|next\s+step|final\s+step)/i',
            '/(?:lesson|tutorial|exercise).*(?:\d+|one|two|three)/i',
            '/(?:beginner|intermediate|advanced).*(?:guide|tutorial|course)/i',
            '/(?:follow\s+these|complete\s+the|finish\s+this).*(?:steps|instructions|tutorial)/i',
        ];

        foreach ($tutorialPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Check if content contains menu patterns
     */
    private function containsMenuPatterns(string $content): bool {
        $menuPatterns = [
            '/(?:file\s+menu|edit\s+menu|view\s+menu|tools\s+menu|help\s+menu)/i',
            '/(?:menu\s+item|menu\s+option|dropdown\s+menu|context\s+menu)/i',
            '/(?:file\s*>\s*open|edit\s*>\s*copy|view\s*>\s*zoom)/i',
            '/(?:ctrl\s*\+|alt\s*\+|shift\s*\+).*(?:key|shortcut)/i',
        ];

        foreach ($menuPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Check if content contains dialogue patterns
     */
    private function containsDialoguePatterns(string $content): bool {
        // Check for dialogue markers
        $dialogueMarkers = [
            '/[「『"\']/u',  // Japanese and English quotation marks
            '/(?:said|asked|replied|answered|whispered|shouted|exclaimed)/i',
            '/(?:he said|she said|they said|I said)/i',
        ];

        foreach ($dialogueMarkers as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Analyze the original perspective of the content
     */
    private function analyzeOriginalPerspective(string $content): array {
        $firstPersonIndicators = [];
        $secondPersonIndicators = [];
        $thirdPersonIndicators = [];
        $confidence = 0.0;

        // English perspective patterns
        $englishFirstPerson = [
            '/\b(?:I|me|my|mine|myself)\b/i' => 1.0,
            '/\b(?:we|us|our|ours|ourselves)\b/i' => 0.9,
            '/\bI\'(?:m|ve|ll|d)\b/i' => 1.0,
        ];

        $englishSecondPerson = [
            '/\b(?:you|your|yours|yourself|yourselves)\b/i' => 1.0,
            '/\byou\'(?:re|ve|ll|d)\b/i' => 1.0,
        ];

        $englishThirdPerson = [
            '/\b(?:he|she|it|they|him|her|them|his|hers|its|their|theirs)\b/i' => 0.8,
            '/\b(?:himself|herself|itself|themselves)\b/i' => 0.9,
        ];

        // Japanese perspective patterns
        $japaneseFirstPerson = [
            '/(?:私|僕|俺|わたし|ぼく|おれ)(?:は|が|を|に|の|と|で|から|まで)/u' => 1.0,
            '/(?:私|僕|俺)(?:たち|達)(?:は|が|を|に)/u' => 0.9,
            '/(?:私|僕|俺)(?:自身|じしん)/u' => 0.8,
        ];

        $japaneseSecondPerson = [
            '/(?:あなた|君|きみ|お前|おまえ)(?:は|が|を|に)/u' => 1.0,
            '/(?:あなた|君)(?:たち|達)/u' => 0.9,
        ];

        $japaneseThirdPerson = [
            '/(?:彼|彼女|彼ら|彼女ら)(?:は|が|を|に)/u' => 0.8,
            '/(?:[ぁ-んァ-ヶ一-龯]{2,6})(?:は|が)(?:思った|考えた|感じた)/u' => 0.6,
        ];

        // Count perspective indicators
        $perspectives = [
            'first_person' => array_merge($englishFirstPerson, $japaneseFirstPerson),
            'second_person' => array_merge($englishSecondPerson, $japaneseSecondPerson),
            'third_person' => array_merge($englishThirdPerson, $japaneseThirdPerson)
        ];

        $scores = ['first_person' => 0, 'second_person' => 0, 'third_person' => 0];
        $indicators = ['first_person' => [], 'second_person' => [], 'third_person' => []];

        foreach ($perspectives as $perspective => $patterns) {
            foreach ($patterns as $pattern => $weight) {
                if (preg_match_all($pattern, $content, $matches)) {
                    $count = count($matches[0]);
                    $scores[$perspective] += $count * $weight;
                    $indicators[$perspective] = array_merge($indicators[$perspective], $matches[0]);
                }
            }
        }

        // Determine dominant perspective
        $dominantPerspective = array_keys($scores, max($scores))[0];
        $totalScore = array_sum($scores);
        $confidence = $totalScore > 0 ? $scores[$dominantPerspective] / $totalScore : 0.3;

        return [
            'perspective' => $dominantPerspective,
            'confidence' => min($confidence, 1.0),
            'scores' => $scores,
            'indicators' => $indicators,
            'total_indicators' => $totalScore
        ];
    }

    /**
     * Determine optimal perspective based on content type
     */
    private function getOptimalPerspectiveForContentType(string $contentType, array $originalPerspective, array $context = []): string {
        switch ($contentType) {
            case self::CONTENT_TYPE_UI:
                // UI text typically uses second person ("Click here", "Enter your name")
                return self::PERSPECTIVE_SECOND_PERSON;

            case self::CONTENT_TYPE_ERROR:
                // Error messages typically address the user directly
                return self::PERSPECTIVE_SECOND_PERSON;

            case self::CONTENT_TYPE_NOTIFICATION:
                // Notifications should be third person or passive ("File has been saved")
                return self::PERSPECTIVE_THIRD_PERSON;

            case self::CONTENT_TYPE_DOCUMENTATION:
                // Documentation often uses second person for instructions
                return self::PERSPECTIVE_SECOND_PERSON;

            case self::CONTENT_TYPE_HELP:
                // Help text typically addresses the user
                return self::PERSPECTIVE_SECOND_PERSON;

            case self::CONTENT_TYPE_TUTORIAL:
                // Tutorials guide the user through steps
                return self::PERSPECTIVE_SECOND_PERSON;

            case self::CONTENT_TYPE_MENU:
                // Menu items are typically imperative (no explicit perspective)
                return self::PERSPECTIVE_PRESERVE_ORIGINAL;

            case self::CONTENT_TYPE_DIALOGUE:
                // Dialogue should preserve original perspective
                return self::PERSPECTIVE_PRESERVE_ORIGINAL;

            case self::CONTENT_TYPE_NARRATIVE:
                // For narrative content, we need more intelligent handling
                return $this->determineNarrativePerspective($originalPerspective, $context);

            case self::CONTENT_TYPE_SYSTEM:
                // System messages are typically third person or passive
                return self::PERSPECTIVE_THIRD_PERSON;

            default:
                // Default to preserving original perspective
                return self::PERSPECTIVE_PRESERVE_ORIGINAL;
        }
    }

    /**
     * Determine optimal perspective for narrative content
     */
    private function determineNarrativePerspective(array $originalPerspective, array $context = []): string {
        // Check if there's a strong original perspective that should be preserved
        if ($originalPerspective['confidence'] > 0.7) {
            // High confidence in original perspective - preserve it
            return self::PERSPECTIVE_PRESERVE_ORIGINAL;
        }

        // Check for specific narrative context clues
        if (isset($context['narrative_style'])) {
            switch ($context['narrative_style']) {
                case 'first_person_pov':
                    return self::PERSPECTIVE_FIRST_PERSON;
                case 'third_person_pov':
                    return self::PERSPECTIVE_THIRD_PERSON;
                case 'second_person_pov':
                    return self::PERSPECTIVE_SECOND_PERSON;
            }
        }

        // For novels/stories, check the dominant perspective
        $dominantPerspective = $originalPerspective['perspective'];

        // If it's clearly first person narrative, preserve it
        if ($dominantPerspective === 'first_person' && $originalPerspective['confidence'] > 0.5) {
            return self::PERSPECTIVE_PRESERVE_ORIGINAL;
        }

        // If it's clearly third person, preserve it
        if ($dominantPerspective === 'third_person' && $originalPerspective['confidence'] > 0.5) {
            return self::PERSPECTIVE_PRESERVE_ORIGINAL;
        }

        // Default to preserving original for narrative content
        return self::PERSPECTIVE_PRESERVE_ORIGINAL;
    }

    /**
     * Generate perspective instructions for translation
     */
    private function generatePerspectiveInstructions(string $contentType, string $optimalPerspective, array $originalPerspective, array $context = []): string {
        $instructions = "\n🎯 INTELLIGENT PERSPECTIVE HANDLING 🎯\n\n";

        $instructions .= "📋 CONTENT TYPE: " . strtoupper(str_replace('_', ' ', $contentType)) . "\n";
        $instructions .= "🎭 OPTIMAL PERSPECTIVE: " . strtoupper(str_replace('_', ' ', $optimalPerspective)) . "\n";
        $instructions .= "📊 ORIGINAL PERSPECTIVE: " . strtoupper($originalPerspective['perspective']) . " (confidence: " . round($originalPerspective['confidence'] * 100, 1) . "%)\n\n";

        switch ($optimalPerspective) {
            case self::PERSPECTIVE_FIRST_PERSON:
                $instructions .= $this->getFirstPersonInstructions($contentType);
                break;

            case self::PERSPECTIVE_SECOND_PERSON:
                $instructions .= $this->getSecondPersonInstructions($contentType);
                break;

            case self::PERSPECTIVE_THIRD_PERSON:
                $instructions .= $this->getThirdPersonInstructions($contentType);
                break;

            case self::PERSPECTIVE_PRESERVE_ORIGINAL:
                $instructions .= $this->getPreserveOriginalInstructions($contentType, $originalPerspective);
                break;
        }

        // Add consistency requirements
        $instructions .= "\n🔒 CONSISTENCY REQUIREMENTS:\n";
        $instructions .= "- Maintain the chosen perspective throughout the entire text\n";
        $instructions .= "- Do not mix different perspectives within the same sentence or paragraph\n";
        $instructions .= "- Ensure pronoun consistency and grammatical correctness\n";
        $instructions .= "- Preserve the natural flow and readability of the text\n\n";

        return $instructions;
    }

    /**
     * Generate first person perspective instructions
     */
    private function getFirstPersonInstructions(string $contentType): string {
        $instructions = "📝 FIRST PERSON PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'I', 'me', 'my', 'mine', 'myself' for singular first person\n";
        $instructions .= "- Use 'we', 'us', 'our', 'ours', 'ourselves' for plural first person\n";
        $instructions .= "- Express thoughts and feelings directly: 'I think', 'I feel', 'I believe'\n";
        $instructions .= "- Use active voice: 'I did', 'I saw', 'I went'\n";
        $instructions .= "- Maintain personal, intimate tone appropriate for first person narration\n";

        if ($contentType === self::CONTENT_TYPE_NARRATIVE) {
            $instructions .= "- For narrative: Express internal thoughts and emotions directly\n";
            $instructions .= "- Describe actions and observations from personal viewpoint\n";
            $instructions .= "- Use 'I thought', 'I realized', 'I noticed' for mental processes\n";
        }

        return $instructions;
    }

    /**
     * Generate second person perspective instructions
     */
    private function getSecondPersonInstructions(string $contentType): string {
        $instructions = "📝 SECOND PERSON PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'you', 'your', 'yours', 'yourself' to address the reader/user\n";
        $instructions .= "- Use imperative mood for instructions: 'Click here', 'Enter your name'\n";
        $instructions .= "- Make the text feel direct and engaging\n";
        $instructions .= "- Use active voice: 'You can', 'You should', 'You will'\n";

        switch ($contentType) {
            case self::CONTENT_TYPE_UI:
                $instructions .= "- For UI elements: Use clear, actionable language\n";
                $instructions .= "- Guide user actions: 'Click to continue', 'Select your option'\n";
                break;

            case self::CONTENT_TYPE_ERROR:
                $instructions .= "- For errors: Be helpful and direct: 'You entered an invalid email'\n";
                $instructions .= "- Provide clear next steps: 'Please check your input and try again'\n";
                break;

            case self::CONTENT_TYPE_DOCUMENTATION:
            case self::CONTENT_TYPE_TUTORIAL:
                $instructions .= "- For instructions: Use step-by-step guidance\n";
                $instructions .= "- Be clear and helpful: 'You can find this option in the menu'\n";
                break;
        }

        return $instructions;
    }

    /**
     * Generate third person perspective instructions
     */
    private function getThirdPersonInstructions(string $contentType): string {
        $instructions = "📝 THIRD PERSON PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'he', 'she', 'it', 'they' for subjects\n";
        $instructions .= "- Use 'him', 'her', 'it', 'them' for objects\n";
        $instructions .= "- Use 'his', 'her', 'its', 'their' for possession\n";
        $instructions .= "- Maintain objective, external viewpoint\n";
        $instructions .= "- Use 'he said', 'she thought', 'they realized' for attribution\n";

        if ($contentType === self::CONTENT_TYPE_NARRATIVE) {
            $instructions .= "- For narrative: Describe actions from external observer perspective\n";
            $instructions .= "- Express character thoughts as: 'he thought', 'she wondered'\n";
            $instructions .= "- Describe emotions objectively: 'his heart raced', 'her eyes widened'\n";
        } elseif ($contentType === self::CONTENT_TYPE_NOTIFICATION) {
            $instructions .= "- For notifications: Use passive or neutral tone\n";
            $instructions .= "- Focus on actions and states: 'The file has been saved'\n";
        }

        return $instructions;
    }

    /**
     * Generate preserve original perspective instructions
     */
    private function getPreserveOriginalInstructions(string $contentType, array $originalPerspective): string {
        $instructions = "📝 PRESERVE ORIGINAL PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Maintain the original point of view as detected in the source text\n";
        $instructions .= "- Do not convert between first, second, or third person\n";
        $instructions .= "- Keep the natural voice and tone of the original content\n";
        $instructions .= "- Preserve the author's intended narrative style\n";

        $dominantPerspective = $originalPerspective['perspective'];
        $instructions .= "- Original perspective detected: " . strtoupper($dominantPerspective) . "\n";

        if ($contentType === self::CONTENT_TYPE_DIALOGUE) {
            $instructions .= "- For dialogue: Preserve character speech patterns exactly\n";
            $instructions .= "- Maintain quotation marks and dialogue formatting\n";
            $instructions .= "- Keep character voice authentic to original\n";
        } elseif ($contentType === self::CONTENT_TYPE_NARRATIVE) {
            $instructions .= "- For narrative: Respect the author's chosen narrative voice\n";
            $instructions .= "- Maintain consistency with the established perspective\n";
            $instructions .= "- Preserve the intimacy or distance created by the original perspective\n";
        }

        return $instructions;
    }

    /**
     * Calculate confidence score for perspective determination
     */
    private function calculateConfidence(string $contentType, array $originalPerspective, string $content): float {
        $baseConfidence = 0.5;

        // Content type confidence modifiers
        $contentTypeConfidence = [
            self::CONTENT_TYPE_UI => 0.9,
            self::CONTENT_TYPE_ERROR => 0.9,
            self::CONTENT_TYPE_NOTIFICATION => 0.8,
            self::CONTENT_TYPE_DOCUMENTATION => 0.8,
            self::CONTENT_TYPE_TUTORIAL => 0.8,
            self::CONTENT_TYPE_MENU => 0.7,
            self::CONTENT_TYPE_DIALOGUE => 0.9,
            self::CONTENT_TYPE_NARRATIVE => 0.6,
            self::CONTENT_TYPE_SYSTEM => 0.8,
        ];

        $typeConfidence = $contentTypeConfidence[$contentType] ?? 0.5;

        // Original perspective confidence
        $originalConfidence = $originalPerspective['confidence'];

        // Content length factor (longer content = more reliable detection)
        $lengthFactor = min(mb_strlen($content) / 200, 1.0);

        // Combine factors
        $finalConfidence = ($typeConfidence * 0.4) + ($originalConfidence * 0.4) + ($lengthFactor * 0.2);

        return min($finalConfidence, 1.0);
    }

    /**
     * Generate reasoning for perspective choice
     */
    private function generateReasoning(string $contentType, string $optimalPerspective, array $originalPerspective): string {
        $reasoning = "Content type '{$contentType}' ";

        switch ($optimalPerspective) {
            case self::PERSPECTIVE_FIRST_PERSON:
                $reasoning .= "typically uses first person perspective for personal, direct communication.";
                break;

            case self::PERSPECTIVE_SECOND_PERSON:
                $reasoning .= "typically uses second person perspective to directly address and guide the user.";
                break;

            case self::PERSPECTIVE_THIRD_PERSON:
                $reasoning .= "typically uses third person perspective for objective, neutral communication.";
                break;

            case self::PERSPECTIVE_PRESERVE_ORIGINAL:
                $reasoning .= "should preserve the original perspective to maintain the author's intended voice and style. ";
                $reasoning .= "Original perspective detected: " . $originalPerspective['perspective'] . " ";
                $reasoning .= "(confidence: " . round($originalPerspective['confidence'] * 100, 1) . "%).";
                break;
        }

        return $reasoning;
    }

    /**
     * Validate perspective consistency across multiple text sections
     */
    public function validatePerspectiveConsistency(array $textSections, array $context = []): array {
        $consistencyReport = [
            'is_consistent' => true,
            'dominant_perspective' => null,
            'inconsistencies' => [],
            'recommendations' => [],
            'confidence' => 0.0
        ];

        if (empty($textSections)) {
            return $consistencyReport;
        }

        $perspectiveAnalyses = [];
        $perspectiveCounts = [
            self::PERSPECTIVE_FIRST_PERSON => 0,
            self::PERSPECTIVE_SECOND_PERSON => 0,
            self::PERSPECTIVE_THIRD_PERSON => 0
        ];

        // Analyze each text section
        foreach ($textSections as $index => $section) {
            $analysis = $this->analyzeOriginalPerspective($section);
            $perspectiveAnalyses[$index] = $analysis;

            if (isset($perspectiveCounts[$analysis['perspective']])) {
                $perspectiveCounts[$analysis['perspective']]++;
            }
        }

        // Determine dominant perspective
        $dominantPerspective = array_keys($perspectiveCounts, max($perspectiveCounts))[0];
        $consistencyReport['dominant_perspective'] = $dominantPerspective;

        // Check for inconsistencies
        $totalSections = count($textSections);
        $dominantCount = $perspectiveCounts[$dominantPerspective];
        $consistencyRatio = $dominantCount / $totalSections;

        $consistencyReport['confidence'] = $consistencyRatio;

        // Flag inconsistencies if less than 80% consistency
        if ($consistencyRatio < 0.8) {
            $consistencyReport['is_consistent'] = false;

            foreach ($perspectiveAnalyses as $index => $analysis) {
                if ($analysis['perspective'] !== $dominantPerspective && $analysis['confidence'] > 0.5) {
                    $consistencyReport['inconsistencies'][] = [
                        'section_index' => $index,
                        'detected_perspective' => $analysis['perspective'],
                        'expected_perspective' => $dominantPerspective,
                        'confidence' => $analysis['confidence'],
                        'text_preview' => mb_substr($textSections[$index], 0, 100) . '...'
                    ];
                }
            }
        }

        // Generate recommendations
        $consistencyReport['recommendations'] = $this->generateConsistencyRecommendations(
            $consistencyReport,
            $perspectiveAnalyses,
            $context
        );

        return $consistencyReport;
    }

    /**
     * Generate recommendations for improving perspective consistency
     */
    private function generateConsistencyRecommendations(array $consistencyReport, array $perspectiveAnalyses, array $context = []): array {
        $recommendations = [];

        if ($consistencyReport['is_consistent']) {
            $recommendations[] = [
                'type' => 'maintain',
                'message' => 'Perspective is consistent across all sections. Continue using ' .
                           strtolower(str_replace('_', ' ', $consistencyReport['dominant_perspective'])) . ' perspective.',
                'priority' => 'low'
            ];
        } else {
            $dominantPerspective = $consistencyReport['dominant_perspective'];

            $recommendations[] = [
                'type' => 'standardize',
                'message' => 'Consider standardizing all sections to use ' .
                           strtolower(str_replace('_', ' ', $dominantPerspective)) . ' perspective for consistency.',
                'priority' => 'high'
            ];

            if (count($consistencyReport['inconsistencies']) > 0) {
                $recommendations[] = [
                    'type' => 'review_sections',
                    'message' => 'Review sections with perspective inconsistencies: ' .
                               implode(', ', array_column($consistencyReport['inconsistencies'], 'section_index')),
                    'priority' => 'medium'
                ];
            }

            // Content-specific recommendations
            $contentType = $context['content_type'] ?? 'unknown';
            switch ($contentType) {
                case self::CONTENT_TYPE_NARRATIVE:
                    $recommendations[] = [
                        'type' => 'narrative_specific',
                        'message' => 'For narrative content, maintain consistent perspective throughout the story. ' .
                                   'Consider if perspective changes are intentional (e.g., different character viewpoints).',
                        'priority' => 'medium'
                    ];
                    break;

                case self::CONTENT_TYPE_UI:
                    $recommendations[] = [
                        'type' => 'ui_specific',
                        'message' => 'UI text should consistently use second person ("you") to address users directly.',
                        'priority' => 'high'
                    ];
                    break;

                case self::CONTENT_TYPE_DOCUMENTATION:
                    $recommendations[] = [
                        'type' => 'documentation_specific',
                        'message' => 'Documentation should consistently use second person for instructions and procedures.',
                        'priority' => 'medium'
                    ];
                    break;
            }
        }

        return $recommendations;
    }

    /**
     * Apply perspective corrections to text based on consistency analysis
     */
    public function applyPerspectiveCorrections(string $text, string $targetPerspective, array $context = []): array {
        $originalAnalysis = $this->analyzeOriginalPerspective($text);

        if ($originalAnalysis['perspective'] === $targetPerspective) {
            return [
                'corrected_text' => $text,
                'changes_made' => false,
                'corrections' => [],
                'confidence' => 1.0
            ];
        }

        // This is a simplified implementation - in a full system, you would implement
        // sophisticated pronoun and perspective conversion logic
        $corrections = [];
        $correctedText = $text;

        // Basic pronoun mapping (this would be much more sophisticated in practice)
        $pronounMappings = $this->getPronounMappings($originalAnalysis['perspective'], $targetPerspective);

        foreach ($pronounMappings as $from => $to) {
            $pattern = '/\b' . preg_quote($from, '/') . '\b/i';
            if (preg_match($pattern, $correctedText)) {
                $correctedText = preg_replace($pattern, $to, $correctedText);
                $corrections[] = [
                    'from' => $from,
                    'to' => $to,
                    'type' => 'pronoun_replacement'
                ];
            }
        }

        return [
            'corrected_text' => $correctedText,
            'changes_made' => !empty($corrections),
            'corrections' => $corrections,
            'confidence' => 0.7 // This would be calculated based on the complexity of changes
        ];
    }

    /**
     * Get pronoun mappings for perspective conversion
     */
    private function getPronounMappings(string $fromPerspective, string $toPerspective): array {
        $mappings = [];

        // This is a simplified mapping - a full implementation would be much more comprehensive
        if ($fromPerspective === self::PERSPECTIVE_FIRST_PERSON && $toPerspective === self::PERSPECTIVE_THIRD_PERSON) {
            $mappings = [
                'I' => 'he/she',
                'me' => 'him/her',
                'my' => 'his/her',
                'mine' => 'his/hers',
                'myself' => 'himself/herself',
                'we' => 'they',
                'us' => 'them',
                'our' => 'their',
                'ours' => 'theirs',
                'ourselves' => 'themselves'
            ];
        } elseif ($fromPerspective === self::PERSPECTIVE_THIRD_PERSON && $toPerspective === self::PERSPECTIVE_FIRST_PERSON) {
            $mappings = [
                'he' => 'I',
                'she' => 'I',
                'him' => 'me',
                'her' => 'me',
                'his' => 'my',
                'hers' => 'mine',
                'himself' => 'myself',
                'herself' => 'myself',
                'they' => 'we',
                'them' => 'us',
                'their' => 'our',
                'theirs' => 'ours',
                'themselves' => 'ourselves'
            ];
        }

        return $mappings;
    }

    /**
     * Generate perspective instructions for user-selected POV
     */
    private function generateUserPOVInstructions(string $contentType, string $userPOV, array $originalPerspective, array $context = []): string {
        $instructions = "\n🎯 USER-SELECTED PERSPECTIVE OVERRIDE 🎯\n\n";

        $instructions .= "📋 CONTENT TYPE: " . strtoupper(str_replace('_', ' ', $contentType)) . "\n";
        $instructions .= "👤 USER SELECTED: " . strtoupper(str_replace('_', ' ', $userPOV)) . "\n";
        $instructions .= "📊 ORIGINAL PERSPECTIVE: " . strtoupper($originalPerspective['perspective']) . " (confidence: " . round($originalPerspective['confidence'] * 100, 1) . "%)\n\n";

        $instructions .= "⚠️ IMPORTANT: User has specifically chosen this perspective. Override automatic detection and apply the selected POV consistently.\n\n";

        switch ($userPOV) {
            case self::PERSPECTIVE_FIRST_PERSON:
                $instructions .= $this->getFirstPersonInstructions($contentType);
                break;

            case self::PERSPECTIVE_SECOND_PERSON:
                $instructions .= $this->getSecondPersonInstructions($contentType);
                break;

            case self::PERSPECTIVE_THIRD_PERSON:
            case self::PERSPECTIVE_THIRD_PERSON_LIMITED:
                $instructions .= $this->getThirdPersonLimitedInstructions($contentType);
                break;

            case self::PERSPECTIVE_THIRD_PERSON_OMNISCIENT:
                $instructions .= $this->getThirdPersonOmniscientInstructions($contentType);
                break;

            case self::PERSPECTIVE_PRESERVE_ORIGINAL:
                $instructions .= $this->getPreserveOriginalInstructions($contentType, $originalPerspective);
                break;
        }

        // Add user-specific consistency requirements
        $instructions .= "\n🔒 USER-SELECTED PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- MANDATORY: Apply the user-selected perspective throughout the entire text\n";
        $instructions .= "- OVERRIDE: Ignore automatic perspective detection in favor of user choice\n";
        $instructions .= "- CONSISTENCY: Maintain the chosen perspective across all sentences and paragraphs\n";
        $instructions .= "- QUALITY: Ensure natural flow and readability with the selected perspective\n";
        $instructions .= "- DIALOGUE: For dialogue, preserve character speech but adjust narrative descriptions\n\n";

        return $instructions;
    }

    /**
     * Generate third person limited instructions
     */
    private function getThirdPersonLimitedInstructions(string $contentType): string {
        $instructions = "📝 THIRD PERSON LIMITED PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'he', 'she', 'it', 'they' for subjects\n";
        $instructions .= "- Use 'him', 'her', 'it', 'them' for objects\n";
        $instructions .= "- Use 'his', 'her', 'its', 'their' for possession\n";
        $instructions .= "- Focus on ONE character's thoughts and feelings at a time\n";
        $instructions .= "- Describe only what the focal character can see, hear, or know\n";
        $instructions .= "- Use 'he thought', 'she wondered', 'they realized' for the focal character\n";
        $instructions .= "- Avoid revealing other characters' internal thoughts directly\n";
        $instructions .= "- Maintain close psychological distance with the focal character\n";

        if ($contentType === self::CONTENT_TYPE_NARRATIVE) {
            $instructions .= "- For narrative: Stay close to the main character's perspective\n";
            $instructions .= "- Express the focal character's emotions and thoughts intimately\n";
            $instructions .= "- Describe other characters only through the focal character's observations\n";
        }

        return $instructions;
    }

    /**
     * Generate third person omniscient instructions
     */
    private function getThirdPersonOmniscientInstructions(string $contentType): string {
        $instructions = "📝 THIRD PERSON OMNISCIENT PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'he', 'she', 'it', 'they' for subjects\n";
        $instructions .= "- Use 'him', 'her', 'it', 'them' for objects\n";
        $instructions .= "- Use 'his', 'her', 'its', 'their' for possession\n";
        $instructions .= "- Access to ALL characters' thoughts and feelings\n";
        $instructions .= "- Can reveal information unknown to any single character\n";
        $instructions .= "- Use 'he thought', 'she wondered' for multiple characters\n";
        $instructions .= "- Provide broader perspective and context\n";
        $instructions .= "- Can shift focus between different characters\n";
        $instructions .= "- Maintain objective, all-knowing narrator voice\n";

        if ($contentType === self::CONTENT_TYPE_NARRATIVE) {
            $instructions .= "- For narrative: Provide insights into multiple characters' minds\n";
            $instructions .= "- Can reveal background information and broader context\n";
            $instructions .= "- Balance between different characters' perspectives as needed\n";
        }

        return $instructions;
    }

    /**
     * Generate reasoning for user-selected POV
     */
    private function generateUserPOVReasoning(string $userPOV, array $originalPerspective): string {
        $reasoning = "User has specifically selected " . strtolower(str_replace('_', ' ', $userPOV)) . " perspective for this translation. ";

        $originalPerspectiveName = $originalPerspective['perspective'];
        $originalConfidence = round($originalPerspective['confidence'] * 100, 1);

        if ($userPOV === self::PERSPECTIVE_PRESERVE_ORIGINAL) {
            $reasoning .= "This choice preserves the original narrative voice and style. ";
            $reasoning .= "Original perspective detected: " . $originalPerspectiveName . " (confidence: " . $originalConfidence . "%).";
        } else {
            $reasoning .= "This overrides the automatically detected perspective (" . $originalPerspectiveName . ", confidence: " . $originalConfidence . "%) ";
            $reasoning .= "in favor of the user's preferred narrative style. ";
            $reasoning .= "The translation will be adapted to consistently use " . strtolower(str_replace('_', ' ', $userPOV)) . " perspective throughout.";
        }

        return $reasoning;
    }

    /**
     * Get available POV options for user selection
     */
    public function getAvailablePOVOptions(): array {
        return [
            self::PERSPECTIVE_PRESERVE_ORIGINAL => [
                'name' => 'Preserve Original',
                'description' => 'Maintain the original perspective as detected in the source text',
                'suitable_for' => ['narrative', 'dialogue', 'all_content']
            ],
            self::PERSPECTIVE_FIRST_PERSON => [
                'name' => 'First Person',
                'description' => 'Use "I", "me", "my" - Personal, intimate narrative from the protagonist\'s viewpoint',
                'suitable_for' => ['narrative', 'personal_stories', 'memoirs']
            ],
            self::PERSPECTIVE_SECOND_PERSON => [
                'name' => 'Second Person',
                'description' => 'Use "you", "your" - Direct address to the reader (rare in novels)',
                'suitable_for' => ['interactive_fiction', 'instructions', 'experimental_narrative']
            ],
            self::PERSPECTIVE_THIRD_PERSON_LIMITED => [
                'name' => 'Third Person Limited',
                'description' => 'Use "he", "she", "they" - Focus on one character\'s thoughts and feelings',
                'suitable_for' => ['narrative', 'character_focused_stories', 'novels']
            ],
            self::PERSPECTIVE_THIRD_PERSON_OMNISCIENT => [
                'name' => 'Third Person Omniscient',
                'description' => 'Use "he", "she", "they" - Access to all characters\' thoughts and feelings',
                'suitable_for' => ['epic_narratives', 'complex_stories', 'multi_character_novels']
            ]
        ];
    }

    /**
     * Validate POV preference and provide suggestions
     */
    public function validatePOVPreference(string $povPreference, string $content, array $context = []): array {
        $availableOptions = $this->getAvailablePOVOptions();

        if (!isset($availableOptions[$povPreference])) {
            return [
                'valid' => false,
                'error' => 'Invalid POV preference provided',
                'suggestion' => self::PERSPECTIVE_PRESERVE_ORIGINAL
            ];
        }

        // Analyze content to provide warnings or suggestions
        $contentType = $this->detectContentType($content, $context);
        $originalPerspective = $this->analyzeOriginalPerspective($content);

        $warnings = [];
        $suggestions = [];

        // Check for potential issues with the selected POV
        if ($povPreference === self::PERSPECTIVE_SECOND_PERSON && $contentType === self::CONTENT_TYPE_NARRATIVE) {
            $warnings[] = 'Second person perspective is uncommon in narrative fiction and may feel unnatural to readers.';
            $suggestions[] = 'Consider using first person or third person limited for better readability.';
        }

        if ($povPreference !== self::PERSPECTIVE_PRESERVE_ORIGINAL && $originalPerspective['confidence'] > 0.8) {
            $warnings[] = 'The original text has a strong ' . $originalPerspective['perspective'] . ' perspective. Changing it may alter the author\'s intended voice.';
            $suggestions[] = 'Consider using "Preserve Original" to maintain the author\'s narrative style.';
        }

        return [
            'valid' => true,
            'warnings' => $warnings,
            'suggestions' => $suggestions,
            'content_analysis' => [
                'content_type' => $contentType,
                'original_perspective' => $originalPerspective,
                'suitability' => $this->assessPOVSuitability($povPreference, $contentType, $originalPerspective)
            ]
        ];
    }

    /**
     * Assess how suitable a POV preference is for the given content
     */
    private function assessPOVSuitability(string $povPreference, string $contentType, array $originalPerspective): array {
        $suitability = [
            'score' => 0.5, // Default neutral score
            'reasoning' => '',
            'recommendation' => 'neutral'
        ];

        $availableOptions = $this->getAvailablePOVOptions();
        $povInfo = $availableOptions[$povPreference];

        // Check if content type is suitable for this POV
        if (in_array($contentType, $povInfo['suitable_for']) || in_array('all_content', $povInfo['suitable_for'])) {
            $suitability['score'] += 0.3;
            $suitability['reasoning'] .= 'POV is well-suited for this content type. ';
        }

        // Check alignment with original perspective
        if ($povPreference === self::PERSPECTIVE_PRESERVE_ORIGINAL) {
            $suitability['score'] = 0.9;
            $suitability['reasoning'] .= 'Preserving original perspective maintains author\'s intended voice. ';
            $suitability['recommendation'] = 'excellent';
        } elseif ($originalPerspective['confidence'] > 0.7) {
            if ($this->isPOVCompatible($povPreference, $originalPerspective['perspective'])) {
                $suitability['score'] += 0.2;
                $suitability['reasoning'] .= 'Selected POV is compatible with the original perspective. ';
            } else {
                $suitability['score'] -= 0.2;
                $suitability['reasoning'] .= 'Selected POV differs significantly from the original perspective. ';
            }
        }

        // Determine recommendation based on score
        if ($suitability['score'] >= 0.8) {
            $suitability['recommendation'] = 'excellent';
        } elseif ($suitability['score'] >= 0.6) {
            $suitability['recommendation'] = 'good';
        } elseif ($suitability['score'] >= 0.4) {
            $suitability['recommendation'] = 'neutral';
        } else {
            $suitability['recommendation'] = 'caution';
        }

        return $suitability;
    }

    /**
     * Check if two POV preferences are compatible
     */
    private function isPOVCompatible(string $pov1, string $pov2): bool {
        // Same POV is always compatible
        if ($pov1 === $pov2) {
            return true;
        }

        // Third person variants are compatible with each other
        $thirdPersonVariants = [
            self::PERSPECTIVE_THIRD_PERSON,
            self::PERSPECTIVE_THIRD_PERSON_LIMITED,
            self::PERSPECTIVE_THIRD_PERSON_OMNISCIENT
        ];

        if (in_array($pov1, $thirdPersonVariants) && in_array($pov2, $thirdPersonVariants)) {
            return true;
        }

        return false;
    }
}
