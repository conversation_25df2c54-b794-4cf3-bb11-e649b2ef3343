<?php
/**
 * Test differences between Gemini 1.5 and 2.0 for family terms
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing Gemini Model Differences ===\n\n";

try {
    $providerManager = new AIProviderManager();
    $db = Database::getInstance();
    
    // Get family terms from name dictionary
    $familyNames = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['兄さん', '兄', '父', '母', 'お父さん', 'お母さん', 'お兄ちゃん', 'お兄さん']
    );
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $familyNames
    ];
    
    // Test text that contains family terms
    $testText = "彼女の母は貴族だった。父からの手紙が届いた。兄は忙しい。「兄さん、お疲れ様です」と言った。";
    
    echo "Test text: {$testText}\n\n";
    
    // Test with different Gemini versions
    $geminiVersions = ['gemini_15', 'gemini_20'];
    
    foreach ($geminiVersions as $version) {
        echo "=== Testing {$version} ===\n";
        
        try {
            $providerManager->setActiveProvider($version);
            $translationService = $providerManager->getTranslationService($version);
            
            echo "Version: " . $translationService->getVersion() . "\n";
            
            $result = $translationService->translateText(
                $testText,
                'en',
                'ja',
                $context
            );
            
            if ($result['success']) {
                $translation = $result['translated_text'];
                echo "Translation: {$translation}\n";
                echo "Execution time: " . $result['execution_time'] . "s\n";
                
                // Check for family term issues
                $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
                $foundForbidden = [];
                
                foreach ($forbiddenTerms as $term) {
                    if (stripos($translation, $term) !== false) {
                        $foundForbidden[] = $term;
                    }
                }
                
                // Check for romanized terms
                $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san'];
                $foundExpected = [];
                
                foreach ($expectedTerms as $term) {
                    if (stripos($translation, $term) !== false) {
                        $foundExpected[] = $term;
                    }
                }
                
                // Assessment
                if (empty($foundForbidden) && !empty($foundExpected)) {
                    echo "🎉 PERFECT: No forbidden terms, found romanized: " . implode(', ', $foundExpected) . "\n";
                } elseif (empty($foundForbidden)) {
                    echo "✅ GOOD: No forbidden terms found\n";
                } else {
                    echo "❌ PROBLEM: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
                }
                
            } else {
                echo "❌ Translation failed: " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error with {$version}: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    // Test with longer content to see if behavior changes
    echo "=== Testing with Longer Content ===\n";
    
    $longerText = str_repeat($testText . " ", 5); // Make it longer
    echo "Longer text length: " . strlen($longerText) . " characters\n\n";
    
    foreach ($geminiVersions as $version) {
        echo "=== {$version} with longer content ===\n";
        
        try {
            $providerManager->setActiveProvider($version);
            $translationService = $providerManager->getTranslationService($version);
            
            $result = $translationService->translateText(
                $longerText,
                'en',
                'ja',
                $context
            );
            
            if ($result['success']) {
                echo "Translation successful\n";
                echo "Execution time: " . $result['execution_time'] . "s\n";
                
                // Check for family term issues
                $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
                $foundForbidden = [];
                
                foreach ($forbiddenTerms as $term) {
                    $count = substr_count(strtolower($result['translated_text']), strtolower($term));
                    if ($count > 0) {
                        $foundForbidden[] = "{$term}: {$count}";
                    }
                }
                
                if (empty($foundForbidden)) {
                    echo "✅ GOOD: No forbidden family terms in longer content\n";
                } else {
                    echo "❌ PROBLEM: Found forbidden terms in longer content: " . implode(', ', $foundForbidden) . "\n";
                }
                
                // Show sample
                echo "Sample: " . substr($result['translated_text'], 0, 200) . "...\n";
                
            } else {
                echo "❌ Longer content failed: " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    // Test with actual Chapter 56 problematic section
    echo "=== Testing with Chapter 56 Problematic Section ===\n";
    
    $chapter = $db->fetchOne(
        'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
        []
    );
    
    // Get the section that was problematic (around 2000-4000 chars)
    $problematicSection = substr($chapter['original_content'], 2000, 2000);
    
    echo "Problematic section length: " . strlen($problematicSection) . " characters\n";
    echo "Sample: " . substr($problematicSection, 0, 100) . "...\n\n";
    
    foreach ($geminiVersions as $version) {
        echo "=== {$version} with problematic section ===\n";
        
        try {
            $providerManager->setActiveProvider($version);
            $translationService = $providerManager->getTranslationService($version);
            
            $result = $translationService->translateText(
                $problematicSection,
                'en',
                'ja',
                $context
            );
            
            if ($result['success']) {
                echo "Translation successful\n";
                echo "Execution time: " . $result['execution_time'] . "s\n";
                
                // Check for family term issues
                $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
                $foundForbidden = [];
                
                foreach ($forbiddenTerms as $term) {
                    $count = substr_count(strtolower($result['translated_text']), strtolower($term));
                    if ($count > 0) {
                        $foundForbidden[] = "{$term}: {$count}";
                    }
                }
                
                if (empty($foundForbidden)) {
                    echo "✅ EXCELLENT: No forbidden family terms in problematic section\n";
                } else {
                    echo "❌ CONFIRMED ISSUE: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
                    echo "🔍 {$version} has issues with this specific content\n";
                }
                
            } else {
                echo "❌ Problematic section failed: " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "❌ General Error: " . $e->getMessage() . "\n";
}

echo "=== Test Complete ===\n";
