<?php
require_once 'config/config.php';
require_once 'classes/TranslationService.php';

echo "Testing DeepSeek simple translation...\n";

$ts = new TranslationService();
$ts->setActiveProvider('deepseek');

$result = $ts->translateText('こんにちは', 'en', 'ja', ['simple' => true, 'type' => 'test']);

echo 'Result: ' . ($result['success'] ? 'SUCCESS - ' . $result['translated_text'] : 'FAILED - ' . $result['error']) . "\n";

if ($result['success']) {
    echo "Execution time: " . $result['execution_time'] . "s\n";
    echo "Provider used: " . ($result['provider_used'] ?? 'unknown') . "\n";
}
?>
