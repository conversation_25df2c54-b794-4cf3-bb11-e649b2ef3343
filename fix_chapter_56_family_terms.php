<?php
/**
 * Fix family term translations in Chapter 56
 */

require_once 'config/config.php';
require_once 'config/database.php';

$db = Database::getInstance();

echo "=== Fixing Family Terms in Chapter 56 ===\n\n";

// Get chapter 56
$chapter = $db->fetchOne(
    'SELECT id, translated_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

$originalContent = $chapter['translated_content'];
$fixedContent = $originalContent;

echo "Original content length: " . strlen($originalContent) . " characters\n\n";

// Define the corrections based on the name dictionary
$corrections = [
    // English family terms that should be romanized
    '/\bfather\b/i' => 'Otou-san',
    '/\bmother\b/i' => 'Okaa-san', 
    '/\bmom\b/i' => 'Okaa-san',
    '/\bdad\b/i' => 'Otou-san',
    
    // Make sure we don't accidentally replace words that contain these terms
    // We'll be more specific with context
];

// More careful replacements with context
$contextualCorrections = [
    // "Mother," at start of dialogue
    '/\bMother,/i' => 'Okaa-san,',
    '/\bMother!/i' => 'Okaa-san!',
    '/\bMother\?/i' => 'Okaa-san?',
    '/\bMother\./i' => 'Okaa-san.',
    '/\bMother"/i' => 'Okaa-san"',
    
    // "Father" in similar contexts
    '/\bFather,/i' => 'Otou-san,',
    '/\bFather!/i' => 'Otou-san!',
    '/\bFather\?/i' => 'Otou-san?',
    '/\bFather\./i' => 'Otou-san.',
    '/\bFather"/i' => 'Otou-san"',
    
    // Specific phrases we found in the analysis
    '/called Father a worthless/i' => 'called Otou-san a worthless',
    '/from her real father to/i' => 'from her real Otou-san to',
    '/Her mother had told/i' => 'Her Okaa-san had told',
];

$changesCount = 0;
$changeLog = [];

// Apply contextual corrections first (more specific)
foreach ($contextualCorrections as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $fixedContent, -1, $count);
    if ($count > 0) {
        $fixedContent = $newContent;
        $changesCount += $count;
        $changeLog[] = "Replaced '{$pattern}' with '{$replacement}' ({$count} times)";
    }
}

// Then apply general corrections for any remaining instances
foreach ($corrections as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $fixedContent, -1, $count);
    if ($count > 0) {
        $fixedContent = $newContent;
        $changesCount += $count;
        $changeLog[] = "Replaced '{$pattern}' with '{$replacement}' ({$count} times)";
    }
}

echo "Changes made:\n";
if ($changesCount > 0) {
    foreach ($changeLog as $change) {
        echo "- {$change}\n";
    }
    echo "\nTotal changes: {$changesCount}\n\n";
    
    // Update the database
    $updateResult = $db->query(
        'UPDATE chapters SET translated_content = ?, updated_at = NOW() WHERE id = ?',
        [$fixedContent, $chapter['id']]
    );

    if ($updateResult) {
        echo "✅ Chapter 56 updated successfully!\n";

        // Log the fix
        $db->query(
            'INSERT INTO translation_logs (novel_id, chapter_id, translation_type, original_text_length, translated_text_length, status, error_message, created_at)
             VALUES (7, ?, "chapter_fix", ?, ?, "success", "Fixed family term translations", NOW())',
            [$chapter['id'], strlen($originalContent), strlen($fixedContent)]
        );

        echo "✅ Fix logged in translation_logs\n";
    } else {
        echo "❌ Failed to update chapter in database\n";
    }
    
} else {
    echo "No changes needed - family terms already correct\n";
}

// Verify the fix by checking for remaining English family terms
echo "\n=== Verification ===\n";
$problematicTerms = ['father', 'mother', 'mom', 'dad'];
$remainingIssues = [];

foreach ($problematicTerms as $term) {
    $count = substr_count(strtolower($fixedContent), strtolower($term));
    if ($count > 0) {
        $remainingIssues[] = "{$term}: {$count} occurrences";
    }
}

if (empty($remainingIssues)) {
    echo "✅ No English family terms found - fix successful!\n";
} else {
    echo "⚠️ Remaining issues:\n";
    foreach ($remainingIssues as $issue) {
        echo "- {$issue}\n";
    }
}

// Show sample of fixed content
echo "\n=== Sample of Fixed Content (first 500 characters) ===\n";
echo substr($fixedContent, 0, 500) . "...\n";

echo "\n=== Fix Complete ===\n";
