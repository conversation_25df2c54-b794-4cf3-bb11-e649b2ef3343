<?php
/**
 * Debug force 3rd person implementation
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/POVPreferenceManager.php';
require_once 'classes/PerspectiveService.php';
require_once 'classes/DeepSeekTranslationService.php';

echo "=== Debugging Force 3rd Person Implementation ===\n\n";

// Test text with first-person pronouns that should be converted
$testText = "私は考えている。我々はこれをテストする必要がある。私の意見では、これは重要だ。";

echo "Original Japanese text:\n";
echo $testText . "\n\n";

// Create narrative context that should trigger force 3rd person
$narrativeContext = [
    'narrative_voice' => 'first_person',
    'narrative_confidence' => 0.8,
    'perspective_indicators' => [
        'first_person' => ['私は', '我々は', '私の'],
        'third_person' => []
    ]
];

$fullContext = [
    'type' => 'chunk',
    'user_pov_preference' => 'third_person_omniscient',
    'narrative_context' => $narrativeContext
];

echo "=== Testing buildNarrativePerspectiveInstructions ===\n";

try {
    $deepSeekService = new DeepSeekTranslationService();
    
    // Use reflection to call the protected method
    $reflection = new ReflectionClass($deepSeekService);
    $method = $reflection->getMethod('buildNarrativePerspectiveInstructions');
    $method->setAccessible(true);
    
    $instructions = $method->invoke($deepSeekService, $narrativeContext, $fullContext);
    
    echo "Generated instructions:\n";
    echo "================\n";
    echo $instructions;
    echo "\n================\n\n";
    
    // Check for key force 3rd person elements
    echo "=== Force 3rd Person Check ===\n";
    
    if (strpos($instructions, 'DEFAULT THIRD PERSON RULE') !== false) {
        echo "✅ 'DEFAULT THIRD PERSON RULE' found\n";
    } else {
        echo "❌ 'DEFAULT THIRD PERSON RULE' NOT found\n";
    }
    
    if (strpos($instructions, 'NEVER USE FIRST PERSON IN NARRATIVE') !== false) {
        echo "✅ 'NEVER USE FIRST PERSON IN NARRATIVE' found\n";
    } else {
        echo "❌ 'NEVER USE FIRST PERSON IN NARRATIVE' NOT found\n";
    }
    
    if (strpos($instructions, 'I thought to myself') !== false) {
        echo "✅ Conversion examples found\n";
    } else {
        echo "❌ Conversion examples NOT found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing buildNarrativePerspectiveInstructions: " . $e->getMessage() . "\n";
}

// Test the full prompt generation
echo "\n=== Testing Full Prompt Generation ===\n";

try {
    $method2 = $reflection->getMethod('buildTranslationPrompt');
    $method2->setAccessible(true);
    
    $prompt = $method2->invoke($deepSeekService, $testText, 'en', 'auto', $fullContext, []);
    
    echo "Full prompt length: " . strlen($prompt) . " characters\n\n";
    
    // Check if force 3rd person instructions are in the full prompt
    if (strpos($prompt, 'DEFAULT THIRD PERSON RULE') !== false) {
        echo "✅ Force 3rd person instructions found in full prompt\n";
    } else {
        echo "❌ Force 3rd person instructions NOT found in full prompt\n";
    }
    
    // Show relevant parts of the prompt
    echo "\n=== Prompt Sections ===\n";
    
    $lines = explode("\n", $prompt);
    $inPerspectiveSection = false;
    $perspectiveLines = [];
    
    foreach ($lines as $line) {
        if (strpos($line, 'CRITICAL NARRATIVE PERSPECTIVE') !== false) {
            $inPerspectiveSection = true;
        }
        
        if ($inPerspectiveSection) {
            $perspectiveLines[] = $line;
            
            // Stop after the perspective section
            if (trim($line) === '' && count($perspectiveLines) > 10) {
                break;
            }
        }
    }
    
    if (!empty($perspectiveLines)) {
        echo "Perspective section found:\n";
        echo "---\n";
        foreach (array_slice($perspectiveLines, 0, 20) as $line) {
            echo $line . "\n";
        }
        echo "---\n";
    } else {
        echo "❌ No perspective section found in prompt\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing full prompt: " . $e->getMessage() . "\n";
}

// Test actual translation with debug
echo "\n=== Testing Translation with Debug ===\n";

try {
    // Enable debug logging
    file_put_contents('debug.log', "\n=== FORCE 3RD PERSON DEBUG TEST ===\n", FILE_APPEND);
    
    $result = $deepSeekService->translateText($testText, 'en', 'auto', $fullContext);
    
    if ($result['success']) {
        echo "Translation result: " . $result['translated_text'] . "\n";
        
        // Check if it's still first person
        $translatedText = $result['translated_text'];
        $firstPersonCount = substr_count($translatedText, 'I ') + substr_count($translatedText, ' I ') + substr_count($translatedText, 'my ') + substr_count($translatedText, 'we ');
        
        if ($firstPersonCount > 0) {
            echo "❌ STILL FIRST PERSON: Found {$firstPersonCount} first-person pronouns\n";
            echo "This indicates the force 3rd person instructions are not working\n";
        } else {
            echo "✅ SUCCESS: No first-person pronouns found\n";
        }
    } else {
        echo "❌ Translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during translation: " . $e->getMessage() . "\n";
}

echo "\nDone.\n";
