<?php
/**
 * Boost Family Term Frequency in Name Dictionary
 * Ensures family terms have high priority in translation prompts
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Boosting Family Term Frequency ===\n\n";

$db = Database::getInstance();

// Family terms that should have high priority
$importantFamilyTerms = [
    '父さん',
    'お父さん', 
    '母さん',
    'お母さん',
    '兄さん',
    'お兄さん',
    'お兄ちゃん',
    'お姉ちゃん'
];

$boostedCount = 0;

foreach ($importantFamilyTerms as $term) {
    $existing = $db->fetchOne(
        'SELECT id, original_name, frequency 
         FROM name_dictionary 
         WHERE novel_id = 7 AND original_name = ?',
        [$term]
    );
    
    if ($existing) {
        $newFrequency = max(10, $existing['frequency']); // Ensure at least frequency 10
        
        if ($existing['frequency'] < $newFrequency) {
            $updated = $db->update(
                'name_dictionary',
                [
                    'frequency' => $newFrequency,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$existing['id']]
            );
            
            if ($updated > 0) {
                echo "✓ Boosted {$term} frequency: {$existing['frequency']} → {$newFrequency}\n";
                $boostedCount++;
            } else {
                echo "✗ Failed to boost {$term} frequency\n";
            }
        } else {
            echo "✓ {$term} already has high frequency: {$existing['frequency']}\n";
        }
    } else {
        echo "⚠️  {$term} not found in dictionary\n";
    }
}

echo "\n=== Summary ===\n";
echo "Family terms boosted: {$boostedCount}\n";

if ($boostedCount > 0) {
    echo "\n✓ Family terms now have high priority and will be included in Gemini prompts!\n";
}

echo "\n=== Complete ===\n";
