<?php
/**
 * Test all Gemini versions to ensure they work normally
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing All Gemini Versions ===\n\n";

// Test text with family terms
$testText = "「父さん、お疲れ様です」と母さんが言った。兄さんは微笑んだ。";

echo "Test text: {$testText}\n";
echo "Expected: Family terms should use romanized forms (Tou-san, Kaa-san, Nii-san)\n\n";

try {
    $providerManager = new AIProviderManager();
    
    // Test all Gemini versions
    $geminiVersions = ['gemini_15', 'gemini_20', 'gemini_25'];
    
    // Prepare context with name dictionary for novel 7
    $db = Database::getInstance();
    $names = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['父さん', 'お父さん', '父', '母さん', 'お母さん', '母', '兄さん', 'お兄ちゃん']
    );
    
    $context = [
        'type' => 'test',
        'novel_id' => 7,
        'names' => $names
    ];
    
    echo "Using name dictionary with " . count($names) . " family term entries\n\n";
    
    foreach ($geminiVersions as $version) {
        echo "=== Testing {$version} ===\n";
        
        try {
            // Set the active provider
            $providerManager->setActiveProvider($version);
            $translationService = $providerManager->getTranslationService($version);
            
            echo "Version: " . $translationService->getVersion() . "\n";
            
            // Perform translation
            $result = $translationService->translateText(
                $testText,
                'en',
                'ja',
                $context
            );
            
            if ($result['success']) {
                $translation = $result['translated_text'];
                echo "Translation: {$translation}\n";
                
                // Check for problematic English family terms
                $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
                $foundProblems = [];
                
                foreach ($problematicTerms as $term) {
                    if (stripos($translation, $term) !== false) {
                        $foundProblems[] = $term;
                    }
                }
                
                // Check for expected romanized terms
                $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san'];
                $foundExpected = [];
                
                foreach ($expectedTerms as $term) {
                    if (stripos($translation, $term) !== false) {
                        $foundExpected[] = $term;
                    }
                }
                
                // Assessment
                if (empty($foundProblems) && !empty($foundExpected)) {
                    echo "✅ PASS: Correct family term handling\n";
                    echo "   - No English family terms: ✅\n";
                    echo "   - Found romanized terms: " . implode(', ', $foundExpected) . "\n";
                } elseif (!empty($foundProblems)) {
                    echo "❌ FAIL: Found English family terms: " . implode(', ', $foundProblems) . "\n";
                } else {
                    echo "⚠️ WARNING: No romanized terms found\n";
                }
                
                echo "Execution time: " . $result['execution_time'] . "s\n";
                
            } else {
                echo "❌ Translation failed: " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ Error with {$version}: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    // Test basic functionality (non-family terms)
    echo "=== Testing Basic Translation Functionality ===\n";
    $basicTest = "こんにちは、世界！";
    
    foreach ($geminiVersions as $version) {
        echo "{$version}: ";
        
        try {
            $providerManager->setActiveProvider($version);
            $translationService = $providerManager->getTranslationService($version);
            
            $result = $translationService->translateText(
                $basicTest,
                'en',
                'ja',
                ['type' => 'test']
            );
            
            if ($result['success']) {
                echo "✅ WORKING - " . $result['translated_text'] . "\n";
            } else {
                echo "❌ FAILED - " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "❌ ERROR - " . $e->getMessage() . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ General Error: " . $e->getMessage() . "\n";
}

echo "\n=== All Tests Complete ===\n";
