<?php
/**
 * Application Configuration
 * Novel Translation Application
 */

// Application constants (define these first)
define('APP_NAME', 'Novel Translator');
define('APP_VERSION', '1.0.0');
define('APP_ROOT', dirname(__DIR__));
define('APP_URL', 'http://localhost/wc');

// Error reporting for development
error_reporting(E_ALL);

// Disable error display for API requests to prevent HTML output in JSON responses
// Check if this is an API request
$requestUri = $_SERVER['REQUEST_URI'] ?? '';
$scriptName = $_SERVER['SCRIPT_NAME'] ?? '';
$isApiRequest = (
    strpos($requestUri, '/api/') !== false ||
    strpos($requestUri, 'api/') !== false ||
    strpos($scriptName, '/api/') !== false ||
    strpos($scriptName, 'api/') !== false
);

if ($isApiRequest) {
    // For API requests: log errors but don't display them
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
    ini_set('error_log', APP_ROOT . '/logs/php_errors.log');
} else {
    // For regular pages: display errors for development
    ini_set('display_errors', 1);
}

// Set timezone
date_default_timezone_set('UTC');

// AI Provider Configuration
// =========================

// DeepSeek AI Configuration
define('DEEPSEEK_API_KEY', '***********************************');
define('DEEPSEEK_API_URL', 'https://api.deepseek.com/chat/completions');

// DeepSeek models
define('DEEPSEEK_CHAT_MODEL', 'deepseek-chat'); // Points to DeepSeek-V3-0324
define('DEEPSEEK_REASONER_MODEL', 'deepseek-reasoner'); // Points to DeepSeek-R1-0528

// Google Gemini AI Configuration
define('GEMINI_API_KEY', 'AIzaSyBMBpIhaID40sjcQgQSEwlgjbw9bOGCWRU');

// Gemini 1.5 Configuration
define('GEMINI_15_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent');
define('GEMINI_15_MODEL', 'gemini-1.5-flash');

// Gemini 2.0 Configuration
define('GEMINI_20_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent');
define('GEMINI_20_MODEL', 'gemini-2.0-flash');

// Gemini 2.5 Configuration
define('GEMINI_25_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent');
define('GEMINI_25_MODEL', 'gemini-2.5-flash-preview-05-20');

// Legacy Gemini configuration (for backward compatibility)
define('GEMINI_API_URL', GEMINI_15_API_URL); // Default to 1.5 for backward compatibility
define('GEMINI_FALLBACK_URL', GEMINI_20_API_URL); // Default fallback to 2.0
define('GEMINI_EXPERIMENTAL_URL', GEMINI_25_API_URL); // Experimental points to 2.5

// AI Provider Settings
define('DEFAULT_AI_PROVIDER', 'deepseek'); // Default provider: 'deepseek', 'gemini_15', 'gemini_20', 'gemini_25', 'mixed'
define('MIXED_MODE_PRIMARY', 'deepseek'); // Primary provider for mixed mode
define('MIXED_MODE_FALLBACK', 'gemini_20'); // Fallback provider for mixed mode

// Supported AI Providers
define('SUPPORTED_AI_PROVIDERS', [
    'deepseek' => [
        'name' => 'DeepSeek',
        'description' => 'DeepSeek V3 - High performance AI model',
        'api_url' => DEEPSEEK_API_URL,
        'model' => DEEPSEEK_CHAT_MODEL,
        'type' => 'openai_compatible'
    ],
    'gemini_15' => [
        'name' => 'Gemini 1.5',
        'description' => 'Google Gemini 1.5 Flash - Fast and efficient',
        'api_url' => GEMINI_15_API_URL,
        'model' => GEMINI_15_MODEL,
        'type' => 'gemini'
    ],
    'gemini_20' => [
        'name' => 'Gemini 2.0',
        'description' => 'Google Gemini 2.0 Flash - Latest stable version',
        'api_url' => GEMINI_20_API_URL,
        'model' => GEMINI_20_MODEL,
        'type' => 'gemini'
    ],
    'gemini_25' => [
        'name' => 'Gemini 2.5',
        'description' => 'Google Gemini 2.5 Flash - Experimental preview',
        'api_url' => GEMINI_25_API_URL,
        'model' => GEMINI_25_MODEL,
        'type' => 'gemini'
    ],
    'mixed' => [
        'name' => 'Mixed Mode',
        'description' => 'Use multiple providers with automatic fallback',
        'primary' => MIXED_MODE_PRIMARY,
        'fallback' => MIXED_MODE_FALLBACK,
        'type' => 'mixed'
    ]
]);

// Translation settings
define('DEFAULT_TARGET_LANGUAGE', 'en');
define('MAX_TRANSLATION_LENGTH', 30000); // Characters per request (deprecated - use chunking)
define('TRANSLATION_TIMEOUT', 480); // 8 minutes to match frontend timeout expectations
define('CHUNK_SIZE_LIMIT', 5000); // Reduced to 5000 characters for reliable chunk translation
define('CHUNK_OVERLAP_SIZE', 300); // Reduced overlap for efficiency
define('ENABLE_SMART_CHUNKING', true); // Use intelligent content splitting
define('ENABLE_DYNAMIC_CHUNKING', true); // Enable adaptive chunk sizing
define('MIN_CHUNK_SIZE', 5000); // Minimum chunk size
define('MAX_CHUNK_SIZE', 15000); // Reduced maximum chunk size for timeout prevention
define('CONTEXT_PRESERVATION_SIZE', 200); // Characters to preserve for context

// Crawling settings
define('USER_AGENT', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
define('CRAWL_DELAY', 1); // Seconds between requests
define('MAX_RETRIES', 3);

// Preview settings
define('MAX_PREVIEW_CHAPTERS', 1000); // Maximum chapters to show in preview (0 = unlimited)

// Supported platforms
define('SUPPORTED_PLATFORMS', [
    'kakuyomu' => [
        'name' => 'Kakuyomu',
        'base_url' => 'https://kakuyomu.jp',
        'pattern' => '/kakuyomu\.jp\/works\/\d+/',
        'language' => 'ja'
    ],
    'syosetu' => [
        'name' => 'Syosetu',
        'base_url' => 'https://ncode.syosetu.com',
        'pattern' => '/ncode\.syosetu\.com\/n\w+/',
        'language' => 'ja'
    ],
    'shuba69' => [
        'name' => '69书吧',
        'base_url' => 'https://69shuba.cx',
        'pattern' => '/(69shuba\.cx|69shu\.com|69shuba\.com)\/(book|txt)\/\d+/',
        'language' => 'zh'
    ]
]);

// Name detection patterns
define('NAME_PATTERNS', [
    'japanese' => [
        'character' => '/(?:[\p{Han}]{1,3}[\p{Hiragana}\p{Katakana}]{1,4}|[\p{Hiragana}\p{Katakana}]{2,6}|[\p{Han}]{2,4})(?=さん|くん|ちゃん|様|殿|氏|先生)/u',
        'location' => '/[\p{Han}]{2,6}(?=国|県|市|町|村|島|山|川|湖|海|城|宮|神社|寺|院)/u',
        'skill' => '/【[\p{Hiragana}\p{Katakana}\p{Han}・ー\s]+】/u',
        'organization' => '/[\p{Han}]{2,6}(?=会社|企業|組織|団体|協会|財団|学園|学院|研究所|工業|産業|金属)/u',
    ],
    'chinese' => [
        'character' => '/[\p{Han}]{2,4}(?=先生|小姐|公子|少爷|大人|前辈|师父|师兄|师姐|师弟|师妹|师伯|师叔|师姑|师公|师爷|宗主|长老|掌门|老祖|王爷|殿下|陛下|夫人|千金|哥哥|姐姐|弟弟|妹妹|大哥|大姐|二哥|二姐|三哥|三姐|四哥|四姐|五哥|五姐)/u',
        'location' => '/[\p{Han}]{2,6}(?=国|省|市|县|镇|村|山|河|湖|海|城|宫|寺|院|宗|门|派|峰|谷|洞|府|阁|楼|台|亭|轩|斋|堂|殿|庙|观|塔|桥|关|岭|坡|坊|街|巷|路|道|径|林|园|苑|池|潭|泉|井|洲|岛|礁|滩|港|湾|江|溪|涧|瀑|崖|壁|石|岩|洞|穴|窟|坑|沟|渠|堤|坝|桥|津|渡|码|埠|镇|集|市|场|店|铺|馆|院|所|厂|矿|场|园|庄|村|寨|堡|城|都|京|州|郡|府|县|区|乡|镇|街|里|坊|巷|胡同)/u',
        'skill' => '/《[\p{Han}\s]+》|【[\p{Han}\s]+】|〖[\p{Han}\s]+〗|『[\p{Han}\s]+』/u',
        'organization' => '/[\p{Han}]{2,6}(?=公司|企业|组织|团体|协会|基金会|学院|研究所|宗门|门派|教派|帮派|联盟|同盟|商会|工会|社团|俱乐部|集团|财团|康采恩|托拉斯|辛迪加|卡特尔|垄断|寡头|巨头|龙头|领头|带头|牵头|主导|主控|主管|主办|主持|主理|主事|主政|主政|主宰|主导|主控|主管|主办|主持|主理|主事|主政|主宰)/u',
    ],
    'korean' => [
        'character' => '/[\p{Hangul}]{2,4}(?=씨|님|군|양|선생|교수|박사|대표|회장|사장|부장|과장|팀장|주임|대리|사원)/u',
        'location' => '/[\p{Hangul}]{2,6}(?=시|도|군|구|동|면|리|읍|마을|학교|대학|회사|병원|공원|역|공항|항구|산|강|바다|호수|섬|궁|성|절|교회|성당|사찰|암자)/u',
        'skill' => '/\[[\p{Hangul}\s]+\]|【[\p{Hangul}\s]+】|《[\p{Hangul}\s]+》/u',
        'organization' => '/[\p{Hangul}]{2,6}(?=회사|기업|조직|단체|협회|재단|학원|연구소|길드|파티|클랜|그룹|팀|부대|군단|연합|동맹|상회|조합|클럽|사단|법인|공사|공단|공기업|사기업|대기업|중기업|소기업)/u',
    ]
]);

// File upload settings
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_DIR', APP_ROOT . '/uploads/');

// Session settings
ini_set('session.cookie_httponly', 1);
ini_set('session.use_strict_mode', 1);
session_start();

// Include Composer autoloader for PHPWord and other dependencies
require_once APP_ROOT . '/vendor/autoload.php';

// Include Database class
require_once APP_ROOT . '/config/database.php';

// Autoloader
spl_autoload_register(function ($class) {
    $paths = [
        APP_ROOT . '/classes/',
        APP_ROOT . '/crawlers/',
        APP_ROOT . '/config/'
    ];

    foreach ($paths as $path) {
        $file = $path . $class . '.php';
        if (file_exists($file)) {
            require_once $file;
            return;
        }
    }
});

// Helper functions
function sanitizeInput($input, $preserveEntities = false) {
    $trimmed = trim($input);

    if ($preserveEntities) {
        // For titles and content where we want to preserve decoded entities
        return $trimmed;
    }

    return htmlspecialchars($trimmed, ENT_QUOTES, 'UTF-8');
}

/**
 * Process novel/chapter titles by decoding HTML entities and cleaning text
 * This ensures titles are stored and displayed properly without HTML entities
 */
function processTitleText($title) {
    if (empty($title)) {
        return '';
    }

    // Decode HTML entities (handles &#039;, &quot;, etc.)
    $decoded = html_entity_decode($title, ENT_QUOTES | ENT_HTML5, 'UTF-8');

    // Remove extra whitespace
    $cleaned = preg_replace('/\s+/', ' ', $decoded);

    // Trim and return
    return trim($cleaned);
}

function validateUrl($url) {
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        return false;
    }
    
    foreach (SUPPORTED_PLATFORMS as $platform => $config) {
        if (preg_match($config['pattern'], $url)) {
            return $platform;
        }
    }
    
    return false;
}

function formatFileSize($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= pow(1024, $pow);
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

function logError($message, $context = []) {
    $logFile = APP_ROOT . '/logs/error.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = $context ? ' | Context: ' . json_encode($context) : '';
    $logMessage = "[{$timestamp}] {$message}{$contextStr}" . PHP_EOL;
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

function jsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    exit;
}
