<?php
/**
 * Fix remaining "brother" and "sister" terms in Chapter 56
 */

require_once 'config/config.php';
require_once 'config/database.php';

$db = Database::getInstance();

echo "=== Fixing Remaining 'brother' and 'sister' Terms in Chapter 56 ===\n\n";

// Get chapter 56
$chapter = $db->fetchOne(
    'SELECT id, translated_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

$content = $chapter['translated_content'];

// Function to find and show context for terms
function findTermContext($text, $term, $contextLength = 80) {
    $positions = [];
    $offset = 0;
    
    while (($pos = stripos($text, $term, $offset)) !== false) {
        $beforeChar = $pos > 0 ? $text[$pos - 1] : ' ';
        $afterChar = $pos + strlen($term) < strlen($text) ? $text[$pos + strlen($term)] : ' ';
        
        // Check if it's a standalone word
        if (!ctype_alnum($beforeChar) && !ctype_alnum($afterChar)) {
            $start = max(0, $pos - $contextLength);
            $end = min(strlen($text), $pos + strlen($term) + $contextLength);
            $context = substr($text, $start, $end - $start);
            
            $positions[] = [
                'position' => $pos,
                'context' => $context,
                'before' => substr($text, $start, $pos - $start),
                'term' => substr($text, $pos, strlen($term)),
                'after' => substr($text, $pos + strlen($term), $end - $pos - strlen($term))
            ];
        }
        
        $offset = $pos + 1;
    }
    
    return $positions;
}

// Find "brother" instances
echo "=== Finding 'brother' instances ===\n";
$brotherInstances = findTermContext($content, 'brother');
foreach ($brotherInstances as $i => $instance) {
    echo "Brother instance " . ($i + 1) . ":\n";
    echo "Context: \"...{$instance['before']}[{$instance['term']}]{$instance['after']}...\"\n\n";
}

// Find "sister" instances  
echo "=== Finding 'sister' instances ===\n";
$sisterInstances = findTermContext($content, 'sister');
foreach ($sisterInstances as $i => $instance) {
    echo "Sister instance " . ($i + 1) . ":\n";
    echo "Context: \"...{$instance['before']}[{$instance['term']}]{$instance['after']}...\"\n\n";
}

// Apply fixes
$fixedContent = $content;
$changesCount = 0;
$changeLog = [];

// Specific contextual replacements for "brother"
$brotherCorrections = [
    '/My brother/i' => 'My Onii-san',
    '/my brother/i' => 'my Onii-san', 
    '/the brother/i' => 'the Onii-san',
    '/her brother/i' => 'her Onii-san',
    '/his brother/i' => 'his Onii-san',
    '/big brother/i' => 'Onii-chan',
    '/\bbrother\b/i' => 'Onii-san', // General fallback
];

// Specific contextual replacements for "sister"
$sisterCorrections = [
    '/big sister/i' => 'Onee-chan',
    '/my sister/i' => 'my Onee-san',
    '/the sister/i' => 'the Onee-san', 
    '/her sister/i' => 'her Onee-san',
    '/his sister/i' => 'his Onee-san',
    '/\bsister\b/i' => 'Onee-san', // General fallback
];

// Apply brother corrections
foreach ($brotherCorrections as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $fixedContent, -1, $count);
    if ($count > 0) {
        $fixedContent = $newContent;
        $changesCount += $count;
        $changeLog[] = "Replaced '{$pattern}' with '{$replacement}' ({$count} times)";
    }
}

// Apply sister corrections
foreach ($sisterCorrections as $pattern => $replacement) {
    $newContent = preg_replace($pattern, $replacement, $fixedContent, -1, $count);
    if ($count > 0) {
        $fixedContent = $newContent;
        $changesCount += $count;
        $changeLog[] = "Replaced '{$pattern}' with '{$replacement}' ({$count} times)";
    }
}

echo "=== Changes Made ===\n";
if ($changesCount > 0) {
    foreach ($changeLog as $change) {
        echo "- {$change}\n";
    }
    echo "\nTotal changes: {$changesCount}\n\n";
    
    // Update the database
    $updateResult = $db->query(
        'UPDATE chapters SET translated_content = ?, updated_at = NOW() WHERE id = ?',
        [$fixedContent, $chapter['id']]
    );
    
    if ($updateResult) {
        echo "✅ Chapter 56 updated successfully!\n";
    } else {
        echo "❌ Failed to update chapter in database\n";
    }
    
} else {
    echo "No changes needed\n";
}

// Final verification
echo "\n=== Final Verification ===\n";
$problematicTerms = ['father', 'mother', 'mom', 'dad', 'brother', 'sister'];
$remainingIssues = [];

foreach ($problematicTerms as $term) {
    $instances = findTermContext($fixedContent, $term);
    if (!empty($instances)) {
        $remainingIssues[] = "{$term}: " . count($instances) . " standalone occurrences";
    }
}

if (empty($remainingIssues)) {
    echo "✅ SUCCESS: No English family terms found - all fixes complete!\n";
    echo "✅ Chapter 56 now follows the established family term rules\n";
} else {
    echo "⚠️ Remaining issues:\n";
    foreach ($remainingIssues as $issue) {
        echo "- {$issue}\n";
    }
}

echo "\n=== Fix Complete ===\n";
