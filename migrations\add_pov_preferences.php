<?php
/**
 * Migration: Add POV (Point of View) Preferences System
 * Creates pov_preferences table for storing narrative perspective preferences
 */

require_once __DIR__ . '/../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Starting POV preferences migration...\n";
    
    // Create pov_preferences table
    $povTableExists = $db->fetchOne("SHOW TABLES LIKE 'pov_preferences'");
    
    if (!$povTableExists) {
        echo "Creating pov_preferences table...\n";
        
        $createTableSQL = "
        CREATE TABLE pov_preferences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            novel_id INT NOT NULL,
            chapter_id INT NULL,
            pov_preference ENUM(
                'preserve_original',
                'first_person',
                'second_person',
                'third_person_limited',
                'third_person_omniscient'
            ) NOT NULL DEFAULT 'preserve_original',
            is_default BOOLEAN DEFAULT FALSE,
            applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
            FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
            UNIQUE KEY unique_novel_default (novel_id, is_default),
            UNIQUE KEY unique_chapter_pov (chapter_id),
            INDEX idx_novel_pov (novel_id),
            INDEX idx_chapter_pov (chapter_id),
            INDEX idx_pov_type (pov_preference)
        )";
        
        $db->query($createTableSQL);
        echo "✓ pov_preferences table created successfully\n";
    } else {
        echo "✓ pov_preferences table already exists\n";
    }
    
    // Add POV-related preferences to user_preferences
    $povPreferences = [
        'default_pov_preference' => 'preserve_original',
        'show_pov_selection_modal' => 'true',
        'remember_pov_choices' => 'true',
        'pov_consistency_warnings' => 'true'
    ];
    
    foreach ($povPreferences as $key => $value) {
        $existing = $db->fetchOne(
            "SELECT id FROM user_preferences WHERE preference_key = ?",
            [$key]
        );
        
        if (!$existing) {
            $db->insert('user_preferences', [
                'preference_key' => $key,
                'preference_value' => $value
            ]);
            echo "✓ Added preference: {$key} = {$value}\n";
        } else {
            echo "✓ Preference already exists: {$key}\n";
        }
    }
    
    // Add POV tracking to translation_logs if not exists
    $povColumnExists = $db->fetchOne(
        "SELECT COUNT(*) as count FROM information_schema.columns 
         WHERE table_schema = DATABASE() 
         AND table_name = 'translation_logs' 
         AND column_name = 'pov_used'"
    );
    
    if (!$povColumnExists['count']) {
        echo "Adding POV tracking to translation_logs...\n";
        $db->query("
            ALTER TABLE translation_logs 
            ADD COLUMN pov_used ENUM(
                'preserve_original',
                'first_person',
                'second_person',
                'third_person_limited',
                'third_person_omniscient'
            ) DEFAULT 'preserve_original' AFTER translation_time_seconds
        ");
        echo "✓ Added pov_used column to translation_logs\n";
    } else {
        echo "✓ POV tracking already exists in translation_logs\n";
    }
    
    echo "\n✅ POV preferences migration completed successfully!\n";
    
} catch (Exception $e) {
    echo "\n❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
