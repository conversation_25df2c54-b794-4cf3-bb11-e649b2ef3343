<?php
/**
 * Test actual POV translation with a sample text
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/POVPreferenceManager.php';
require_once 'classes/PerspectiveService.php';
require_once 'classes/DeepSeekTranslationService.php';

echo "=== Testing Actual POV Translation ===\n\n";

// Test text with first-person pronouns that should be converted
$testText = "私は考えている。我々はこれをテストする必要がある。私の意見では、これは重要だ。";

echo "Original Japanese text:\n";
echo $testText . "\n\n";

// Create POV context
$povManager = new POVPreferenceManager();
$perspectiveService = new PerspectiveService();

// Generate perspective analysis with POV preference
$translationContext = ['user_pov_preference' => 'third_person_omniscient'];
$perspectiveAnalysis = $perspectiveService->determineOptimalPerspective($testText, $translationContext);

// Create full translation context
$fullContext = [
    'type' => 'chunk',
    'user_pov_preference' => 'third_person_omniscient',
    'narrative_context' => [
        'content_type' => $perspectiveAnalysis['content_type'],
        'optimal_perspective' => $perspectiveAnalysis['optimal_perspective'],
        'perspective_reasoning' => $perspectiveAnalysis['reasoning'],
        'perspective_instructions' => $perspectiveAnalysis['instructions'],
        'user_selected' => $perspectiveAnalysis['user_selected'] ?? false
    ]
];

echo "=== Translation Context ===\n";
echo "POV Preference: {$fullContext['user_pov_preference']}\n";
echo "Optimal Perspective: {$fullContext['narrative_context']['optimal_perspective']}\n";
echo "User Selected: " . ($fullContext['narrative_context']['user_selected'] ? 'YES' : 'NO') . "\n\n";

// Test translation
echo "=== Performing Translation ===\n";

try {
    $deepSeekService = new DeepSeekTranslationService();
    
    // Translate the text
    $result = $deepSeekService->translateText($testText, 'en', 'auto', $fullContext);
    
    if ($result['success']) {
        echo "✅ Translation successful!\n";
        echo "Translated text:\n";
        echo $result['translated_text'] . "\n\n";
        
        // Analyze the result
        echo "=== POV Analysis of Result ===\n";
        
        $translatedText = $result['translated_text'];
        
        // Check for first-person pronouns (should be minimal)
        $firstPersonPronouns = ['I ', ' I ', 'I\'', 'me ', ' me', 'my ', ' my', 'we ', ' we', 'us ', ' us', 'our ', ' our'];
        $firstPersonCount = 0;
        foreach ($firstPersonPronouns as $pronoun) {
            $firstPersonCount += substr_count($translatedText, $pronoun);
        }
        
        // Check for third-person pronouns (should be present)
        $thirdPersonPronouns = ['he ', ' he', 'she ', ' she', 'they ', ' they', 'him ', ' him', 'her ', ' her', 'them ', ' them', 'his ', ' his', 'their ', ' their'];
        $thirdPersonCount = 0;
        foreach ($thirdPersonPronouns as $pronoun) {
            $thirdPersonCount += substr_count($translatedText, $pronoun);
        }
        
        echo "First-person pronouns found: {$firstPersonCount}\n";
        echo "Third-person pronouns found: {$thirdPersonCount}\n";
        
        if ($firstPersonCount == 0 && $thirdPersonCount > 0) {
            echo "✅ PERFECT: Successfully converted to third-person perspective!\n";
        } elseif ($firstPersonCount < $thirdPersonCount) {
            echo "✅ GOOD: Mostly third-person perspective\n";
        } elseif ($firstPersonCount == 0 && $thirdPersonCount == 0) {
            echo "⚠️ NEUTRAL: No personal pronouns found\n";
        } else {
            echo "❌ ISSUE: Still contains first-person pronouns\n";
        }
        
        // Check for specific conversion patterns
        if (strpos($translatedText, 'he thought') !== false || strpos($translatedText, 'they thought') !== false) {
            echo "✅ Found converted thought patterns (he/they thought)\n";
        }
        
        if (strpos($translatedText, 'he believes') !== false || strpos($translatedText, 'they believe') !== false) {
            echo "✅ Found converted belief patterns (he/they believes)\n";
        }
        
    } else {
        echo "❌ Translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during translation: " . $e->getMessage() . "\n";
}

echo "\nDone.\n";
