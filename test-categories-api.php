<?php
/**
 * Test the categories API endpoint directly
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Categories API Test</h1>\n";

try {
    echo "<h2>Step 1: Loading configuration</h2>\n";
    require_once 'config/config.php';
    echo "<p style='color: green;'>✅ Configuration loaded</p>\n";
    
    echo "<h2>Step 2: Database connection</h2>\n";
    $db = Database::getInstance();
    echo "<p style='color: green;'>✅ Database connected</p>\n";
    
    echo "<h2>Step 3: Finding WordPress profiles</h2>\n";
    $profiles = $db->fetchAll("SELECT * FROM wordpress_profiles WHERE is_active = 1 LIMIT 1");
    
    if (empty($profiles)) {
        echo "<p style='color: red;'>❌ No active WordPress profiles found</p>\n";
        echo "<p>Please create a WordPress profile in Settings first.</p>\n";
        exit;
    }
    
    $profile = $profiles[0];
    echo "<p style='color: green;'>✅ Using profile: {$profile['profile_name']}</p>\n";
    
    echo "<h2>Step 4: Testing API endpoint directly</h2>\n";
    
    // Simulate the API request
    $_GET['profile_id'] = $profile['id'];
    $_SERVER['REQUEST_METHOD'] = 'GET';
    
    echo "<p>Simulating request: <code>GET /api/wordpress-categories.php?profile_id={$profile['id']}</code></p>\n";
    
    // Capture output from the API
    ob_start();
    
    try {
        // Include the API file to execute it
        include 'api/wordpress-categories.php';
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ API Exception: " . $e->getMessage() . "</p>\n";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
        exit;
    } catch (Error $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ API Fatal Error: " . $e->getMessage() . "</p>\n";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
        exit;
    }
    
    $apiOutput = ob_get_clean();
    
    echo "<h3>API Output:</h3>\n";
    echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars($apiOutput);
    echo "</pre>\n";
    
    echo "<h3>API Output Analysis:</h3>\n";
    
    // Check if output starts with HTML (indicating an error)
    if (strpos(trim($apiOutput), '<') === 0) {
        echo "<p style='color: red;'>❌ API returned HTML instead of JSON (likely a PHP error)</p>\n";
        
        // Try to extract error information
        if (preg_match('/<b>(.+?)<\/b>/', $apiOutput, $matches)) {
            echo "<p><strong>Detected Error:</strong> " . htmlspecialchars($matches[1]) . "</p>\n";
        }
        
        if (preg_match('/Fatal error:(.+?)in/', $apiOutput, $matches)) {
            echo "<p><strong>Fatal Error:</strong> " . htmlspecialchars(trim($matches[1])) . "</p>\n";
        }
        
        if (preg_match('/Parse error:(.+?)in/', $apiOutput, $matches)) {
            echo "<p><strong>Parse Error:</strong> " . htmlspecialchars(trim($matches[1])) . "</p>\n";
        }
        
    } else {
        // Try to parse as JSON
        $result = json_decode($apiOutput, true);
        
        if ($result === null) {
            echo "<p style='color: red;'>❌ API output is not valid JSON</p>\n";
            echo "<p><strong>JSON Error:</strong> " . json_last_error_msg() . "</p>\n";
        } else {
            echo "<p style='color: green;'>✅ API returned valid JSON</p>\n";
            
            if (isset($result['success'])) {
                if ($result['success']) {
                    echo "<p style='color: green;'>✅ API call successful</p>\n";
                    if (isset($result['categories'])) {
                        echo "<p>Found " . count($result['categories']) . " categories</p>\n";
                        if (!empty($result['categories'])) {
                            echo "<p><strong>Sample categories:</strong></p>\n";
                            echo "<ul>\n";
                            foreach (array_slice($result['categories'], 0, 5) as $category) {
                                echo "<li>{$category['name']} (ID: {$category['id']})</li>\n";
                            }
                            echo "</ul>\n";
                        }
                    }
                } else {
                    echo "<p style='color: red;'>❌ API call failed</p>\n";
                    echo "<p><strong>Error:</strong> " . ($result['error'] ?? 'Unknown error') . "</p>\n";
                }
            } else {
                echo "<p style='color: orange;'>⚠️ API response doesn't have 'success' field</p>\n";
                echo "<p><strong>Response structure:</strong></p>\n";
                echo "<pre>" . htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT)) . "</pre>\n";
            }
        }
    }
    
    echo "<h2>Step 5: Manual WordPress Service Test</h2>\n";
    
    try {
        echo "<p>Creating WordPressService instance...</p>\n";
        $wordpressService = new WordPressService($db);
        echo "<p style='color: green;'>✅ WordPressService created</p>\n";
        
        echo "<p>Loading profile...</p>\n";
        $wordpressService->loadProfile($profile['id']);
        echo "<p style='color: green;'>✅ Profile loaded</p>\n";
        
        echo "<p>Fetching categories...</p>\n";
        $categoriesResult = $wordpressService->fetchAvailableCategories();
        
        if ($categoriesResult['success']) {
            echo "<p style='color: green;'>✅ Categories fetched successfully</p>\n";
            echo "<p>Found " . count($categoriesResult['categories']) . " categories</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Failed to fetch categories: {$categoriesResult['error']}</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ WordPressService Error: " . $e->getMessage() . "</p>\n";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Test Error: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2, h3 { color: #333; }
p { margin: 10px 0; }
ul { margin: 5px 0 5px 20px; }
code { background: #f5f5f5; padding: 2px 4px; border-radius: 3px; }
pre { background: #f5f5f5; padding: 10px; border: 1px solid #ddd; border-radius: 3px; overflow-x: auto; }
</style>
