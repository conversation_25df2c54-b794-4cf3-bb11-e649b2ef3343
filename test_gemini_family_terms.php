<?php
/**
 * Test Gemini Family Term Translation with Fixed Name Dictionary
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing Gemini Family Term Translation ===\n\n";

// Test text with family terms
$testText = "「父さん、お疲れ様です」と言った。母さんは微笑んだ。";

echo "Test text: {$testText}\n";
echo "Expected: Family terms should use romanized forms like 'Tou-san' and 'Kaa-san'\n\n";

try {
    $providerManager = new AIProviderManager();
    
    // Test with different Gemini versions
    $geminiVersions = ['gemini_15', 'gemini_20', 'gemini_25'];
    
    foreach ($geminiVersions as $version) {
        echo "--- Testing {$version} ---\n";
        
        // Set the active provider
        $providerManager->setActiveProvider($version);
        
        // Get the translation service
        $translationService = $providerManager->getTranslationService($version);
        
        // Prepare context with name dictionary for novel 7
        $db = Database::getInstance();
        $names = $db->fetchAll(
            'SELECT original_name, romanization, translation, name_type 
             FROM name_dictionary 
             WHERE novel_id = 7 
             ORDER BY frequency DESC',
            []
        );
        
        $context = [
            'type' => 'test',
            'novel_id' => 7,
            'names' => $names
        ];
        
        echo "Using name dictionary with " . count($names) . " entries\n";
        
        // Perform translation
        $result = $translationService->translateText(
            $testText,
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            echo "Translation: {$result['translated_text']}\n";
            
            // Check if family terms are correctly translated (case-insensitive)
            $translation = $result['translated_text'];
            $translationLower = strtolower($translation);
            $hasCorrectTousan = strpos($translationLower, 'tou-san') !== false;
            $hasCorrectKaasan = strpos($translationLower, 'kaa-san') !== false;
            $hasIncorrectFatherSan = strpos($translationLower, 'father-san') !== false;
            $hasIncorrectMotherSan = strpos($translationLower, 'mother-san') !== false;
            
            echo "Analysis:\n";
            echo "  ✓ Contains 'Tou-san': " . ($hasCorrectTousan ? 'YES' : 'NO') . "\n";
            echo "  ✓ Contains 'Kaa-san': " . ($hasCorrectKaasan ? 'YES' : 'NO') . "\n";
            echo "  ✗ Contains 'father-san': " . ($hasIncorrectFatherSan ? 'YES (BAD)' : 'NO (GOOD)') . "\n";
            echo "  ✗ Contains 'mother-san': " . ($hasIncorrectMotherSan ? 'YES (BAD)' : 'NO (GOOD)') . "\n";

            if ($hasCorrectTousan && $hasCorrectKaasan && !$hasIncorrectFatherSan && !$hasIncorrectMotherSan) {
                echo "  🎉 PERFECT: Family terms correctly romanized!\n";
            } elseif ($hasCorrectTousan || $hasCorrectKaasan) {
                echo "  ⚠️  PARTIAL: Some family terms correctly romanized\n";
            } else {
                echo "  ❌ ISSUE: Family terms not using correct romanized format\n";
            }
            
        } else {
            echo "Translation failed: {$result['error']}\n";
        }
        
        echo "\n";
        
        // Small delay between tests
        sleep(2);
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "=== Test Complete ===\n";
