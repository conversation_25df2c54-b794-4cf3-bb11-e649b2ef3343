<?php

class LanguageDetectionService {
    
    /**
     * Detect the primary language of a text
     */
    public function detectLanguage(string $text): array {
        if (empty(trim($text))) {
            return [
                'language' => 'unknown',
                'confidence' => 0,
                'is_chinese' => false,
                'is_english' => false,
                'is_japanese' => false
            ];
        }
        
        $analysis = $this->analyzeTextLanguage($text);
        
        return [
            'language' => $analysis['primary_language'],
            'confidence' => $analysis['confidence'],
            'is_chinese' => $analysis['is_chinese'],
            'is_english' => $analysis['is_english'],
            'is_japanese' => $analysis['is_japanese'],
            'character_breakdown' => $analysis['character_breakdown']
        ];
    }
    
    /**
     * Check if translation was successful by comparing source and target languages
     */
    public function validateTranslation(string $originalText, string $translatedText, string $expectedTargetLanguage = 'en'): array {
        $originalLang = $this->detectLanguage($originalText);
        $translatedLang = $this->detectLanguage($translatedText);
        
        $isValid = false;
        $issues = [];
        
        // Check if translated text is in the expected target language
        switch (strtolower($expectedTargetLanguage)) {
            case 'en':
            case 'english':
                $isValid = $translatedLang['is_english'];
                if (!$isValid) {
                    $issues[] = "Expected English translation but detected: " . $translatedLang['language'];
                }
                break;
                
            case 'zh':
            case 'chinese':
                $isValid = $translatedLang['is_chinese'];
                if (!$isValid) {
                    $issues[] = "Expected Chinese translation but detected: " . $translatedLang['language'];
                }
                break;
                
            case 'ja':
            case 'japanese':
                $isValid = $translatedLang['is_japanese'];
                if (!$isValid) {
                    $issues[] = "Expected Japanese translation but detected: " . $translatedLang['language'];
                }
                break;
        }
        
        // Check if translation actually changed the language
        if ($originalLang['language'] === $translatedLang['language'] && 
            $originalLang['language'] !== 'unknown' && 
            $translatedLang['language'] !== 'unknown') {
            $issues[] = "Translation did not change language (both are " . $originalLang['language'] . ")";
            $isValid = false;
        }
        
        // Check for identical content (indicating failed translation)
        if ($originalText === $translatedText) {
            $issues[] = "Original and translated content are identical";
            $isValid = false;
        }
        
        // Check for minimal changes (possible failed translation)
        $similarity = $this->calculateTextSimilarity($originalText, $translatedText);
        if ($similarity > 0.9) {
            $issues[] = "Content similarity too high ({$similarity}), possible failed translation";
            $isValid = false;
        }
        
        return [
            'is_valid' => $isValid,
            'issues' => $issues,
            'original_language' => $originalLang,
            'translated_language' => $translatedLang,
            'similarity_score' => $similarity ?? 0
        ];
    }
    
    /**
     * Analyze text to determine language composition
     */
    private function analyzeTextLanguage(string $text): array {
        $totalChars = mb_strlen($text, 'UTF-8');
        if ($totalChars === 0) {
            return $this->getEmptyAnalysis();
        }
        
        // Count different character types
        $chineseCount = preg_match_all('/[\x{4e00}-\x{9fff}]/u', $text);
        $englishCount = preg_match_all('/[a-zA-Z]/', $text);
        $japaneseHiraganaCount = preg_match_all('/[\x{3040}-\x{309f}]/u', $text);
        $japaneseKatakanaCount = preg_match_all('/[\x{30a0}-\x{30ff}]/u', $text);
        $japaneseCount = $japaneseHiraganaCount + $japaneseKatakanaCount;
        
        // Calculate ratios
        $chineseRatio = $chineseCount / $totalChars;
        $englishRatio = $englishCount / $totalChars;
        $japaneseRatio = $japaneseCount / $totalChars;
        
        // Determine primary language
        $primaryLanguage = 'unknown';
        $confidence = 0;
        
        if ($chineseRatio > 0.3) {
            $primaryLanguage = 'chinese';
            $confidence = min(1.0, $chineseRatio * 2);
        } elseif ($englishRatio > 0.5) {
            $primaryLanguage = 'english';
            $confidence = min(1.0, $englishRatio * 1.5);
        } elseif ($japaneseRatio > 0.2) {
            $primaryLanguage = 'japanese';
            $confidence = min(1.0, $japaneseRatio * 3);
        } elseif ($englishRatio > 0.3) {
            $primaryLanguage = 'english';
            $confidence = $englishRatio;
        }
        
        return [
            'primary_language' => $primaryLanguage,
            'confidence' => round($confidence, 2),
            'is_chinese' => $chineseRatio > 0.3,
            'is_english' => $englishRatio > 0.5,
            'is_japanese' => $japaneseRatio > 0.2,
            'character_breakdown' => [
                'total_chars' => $totalChars,
                'chinese_chars' => $chineseCount,
                'english_chars' => $englishCount,
                'japanese_chars' => $japaneseCount,
                'chinese_ratio' => round($chineseRatio, 3),
                'english_ratio' => round($englishRatio, 3),
                'japanese_ratio' => round($japaneseRatio, 3)
            ]
        ];
    }
    
    /**
     * Calculate similarity between two texts
     */
    private function calculateTextSimilarity(string $text1, string $text2): float {
        if (empty($text1) || empty($text2)) {
            return 0.0;
        }
        
        // Simple character-based similarity
        $len1 = mb_strlen($text1, 'UTF-8');
        $len2 = mb_strlen($text2, 'UTF-8');
        
        if ($len1 === 0 && $len2 === 0) {
            return 1.0;
        }
        
        if ($len1 === 0 || $len2 === 0) {
            return 0.0;
        }
        
        // Calculate Levenshtein distance for shorter texts
        if ($len1 < 1000 && $len2 < 1000) {
            $distance = levenshtein(
                mb_substr($text1, 0, 255, 'UTF-8'),
                mb_substr($text2, 0, 255, 'UTF-8')
            );
            $maxLen = max(mb_strlen(mb_substr($text1, 0, 255, 'UTF-8')), 
                         mb_strlen(mb_substr($text2, 0, 255, 'UTF-8')));
            return 1 - ($distance / $maxLen);
        }
        
        // For longer texts, use a simpler approach
        $commonChars = 0;
        $minLen = min($len1, $len2);
        
        for ($i = 0; $i < $minLen; $i++) {
            if (mb_substr($text1, $i, 1, 'UTF-8') === mb_substr($text2, $i, 1, 'UTF-8')) {
                $commonChars++;
            }
        }
        
        return $commonChars / max($len1, $len2);
    }
    
    /**
     * Get empty analysis structure
     */
    private function getEmptyAnalysis(): array {
        return [
            'primary_language' => 'unknown',
            'confidence' => 0,
            'is_chinese' => false,
            'is_english' => false,
            'is_japanese' => false,
            'character_breakdown' => [
                'total_chars' => 0,
                'chinese_chars' => 0,
                'english_chars' => 0,
                'japanese_chars' => 0,
                'chinese_ratio' => 0,
                'english_ratio' => 0,
                'japanese_ratio' => 0
            ]
        ];
    }
    
    /**
     * Check if text contains mixed languages (for validation)
     */
    public function hasMixedLanguages(string $text): array {
        $analysis = $this->analyzeTextLanguage($text);
        $breakdown = $analysis['character_breakdown'];
        
        $languageCount = 0;
        if ($breakdown['chinese_ratio'] > 0.1) $languageCount++;
        if ($breakdown['english_ratio'] > 0.1) $languageCount++;
        if ($breakdown['japanese_ratio'] > 0.1) $languageCount++;
        
        return [
            'has_mixed' => $languageCount > 1,
            'language_count' => $languageCount,
            'breakdown' => $breakdown
        ];
    }
}
?>
