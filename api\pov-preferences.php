<?php
/**
 * API Endpoint: POV Preferences Management
 * GET /api/pov-preferences.php - Get POV preferences for a novel
 * POST /api/pov-preferences.php - Set POV preferences for a novel or chapter
 * DELETE /api/pov-preferences.php - Remove POV preferences
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $povManager = new POVPreferenceManager();
} catch (Exception $e) {
    logError('Failed to initialize POVPreferenceManager: ' . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'POV preference service initialization failed'
    ], 500);
}

try {
    switch ($method) {
        case 'GET':
            handleGetPOVPreferences($povManager);
            break;

        case 'POST':
            handleSetPOVPreferences($povManager);
            break;

        case 'DELETE':
            handleDeletePOVPreferences($povManager);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    logError('POV Preferences API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'POV preferences operation failed: ' . $e->getMessage()
    ], 500);
}

/**
 * Handle GET request - retrieve POV preferences
 */
function handleGetPOVPreferences($povManager) {
    $novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;
    $chapterId = isset($_GET['chapter_id']) ? (int)$_GET['chapter_id'] : 0;

    if (!$novelId) {
        jsonResponse([
            'success' => false,
            'error' => 'Novel ID is required'
        ], 400);
        return;
    }

    try {
        if ($chapterId) {
            // Get specific chapter POV preference
            $povPreference = $povManager->getChapterPOVPreference($novelId, $chapterId);
            
            jsonResponse([
                'success' => true,
                'data' => [
                    'novel_id' => $novelId,
                    'chapter_id' => $chapterId,
                    'pov_preference' => $povPreference,
                    'type' => 'chapter'
                ]
            ]);
        } else {
            // Get all POV preferences for the novel
            $preferences = $povManager->getNovelPOVPreferences($novelId);
            $statistics = $povManager->getPOVStatistics($novelId);
            
            jsonResponse([
                'success' => true,
                'data' => array_merge($preferences, [
                    'novel_id' => $novelId,
                    'statistics' => $statistics,
                    'available_options' => $povManager->getAvailablePOVOptions()
                ])
            ]);
        }
    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => 'Failed to retrieve POV preferences: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle POST request - set POV preferences
 */
function handleSetPOVPreferences($povManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse([
            'success' => false,
            'error' => 'Invalid JSON input'
        ], 400);
        return;
    }

    $novelId = isset($input['novel_id']) ? (int)$input['novel_id'] : 0;
    $chapterId = isset($input['chapter_id']) ? (int)$input['chapter_id'] : 0;
    $povPreference = isset($input['pov_preference']) ? trim($input['pov_preference']) : '';
    $isDefault = isset($input['is_default']) ? (bool)$input['is_default'] : false;

    if (!$novelId || !$povPreference) {
        jsonResponse([
            'success' => false,
            'error' => 'Novel ID and POV preference are required'
        ], 400);
        return;
    }

    try {
        if ($chapterId && !$isDefault) {
            // Set chapter-specific POV preference
            $success = $povManager->setChapterPOVPreference($novelId, $chapterId, $povPreference);
            $type = 'chapter';
        } else {
            // Set novel default POV preference
            $success = $povManager->setNovelPOVPreference($novelId, $povPreference);
            $type = 'novel_default';
        }

        if ($success) {
            jsonResponse([
                'success' => true,
                'data' => [
                    'novel_id' => $novelId,
                    'chapter_id' => $chapterId ?: null,
                    'pov_preference' => $povPreference,
                    'type' => $type,
                    'message' => 'POV preference saved successfully'
                ]
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => 'Failed to save POV preference'
            ], 500);
        }
    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => 'Failed to set POV preference: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle DELETE request - remove POV preferences
 */
function handleDeletePOVPreferences($povManager) {
    $novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;
    $chapterId = isset($_GET['chapter_id']) ? (int)$_GET['chapter_id'] : 0;

    if (!$novelId) {
        jsonResponse([
            'success' => false,
            'error' => 'Novel ID is required'
        ], 400);
        return;
    }

    try {
        if ($chapterId) {
            // Remove chapter-specific POV preference
            $success = $povManager->removeChapterPOVPreference($chapterId);
            $type = 'chapter';
        } else {
            // Remove novel default POV preference
            $success = $povManager->removeNovelPOVPreference($novelId);
            $type = 'novel_default';
        }

        if ($success) {
            jsonResponse([
                'success' => true,
                'data' => [
                    'novel_id' => $novelId,
                    'chapter_id' => $chapterId ?: null,
                    'type' => $type,
                    'message' => 'POV preference removed successfully'
                ]
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => 'Failed to remove POV preference'
            ], 500);
        }
    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => 'Failed to remove POV preference: ' . $e->getMessage()
        ], 500);
    }
}




?>
