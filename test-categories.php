<?php
/**
 * Test script for WordPress category functionality
 * Run this script to test the category selection features
 */

require_once 'config/config.php';

echo "<h1>WordPress Category Functionality Test</h1>\n";

try {
    $db = Database::getInstance();
    
    // Test 1: Check if WordPress profiles exist
    echo "<h2>Test 1: WordPress Profiles</h2>\n";
    $profiles = $db->fetchAll("SELECT * FROM wordpress_profiles WHERE is_active = 1");
    
    if (empty($profiles)) {
        echo "<p style='color: red;'>❌ No active WordPress profiles found. Please create a profile first.</p>\n";
        echo "<p><a href='settings.php'>Go to Settings to create a WordPress profile</a></p>\n";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Found " . count($profiles) . " active WordPress profile(s):</p>\n";
    foreach ($profiles as $profile) {
        echo "<ul><li>{$profile['profile_name']} - {$profile['site_url']}</li></ul>\n";
    }
    
    // Test 2: Test category API endpoint
    echo "<h2>Test 2: Category API Endpoint</h2>\n";
    $testProfile = $profiles[0];
    
    echo "<p>Testing category fetch for profile: {$testProfile['profile_name']}</p>\n";
    
    // Simulate API call
    $_GET['profile_id'] = $testProfile['id'];
    
    ob_start();
    try {
        include 'api/wordpress-categories.php';
        $apiOutput = ob_get_clean();
        $result = json_decode($apiOutput, true);
        
        if ($result && $result['success']) {
            echo "<p style='color: green;'>✅ Category API working. Found " . count($result['categories']) . " categories:</p>\n";
            echo "<ul>\n";
            foreach (array_slice($result['categories'], 0, 5) as $category) {
                echo "<li>{$category['name']} (ID: {$category['id']})</li>\n";
            }
            if (count($result['categories']) > 5) {
                echo "<li>... and " . (count($result['categories']) - 5) . " more</li>\n";
            }
            echo "</ul>\n";
        } else {
            echo "<p style='color: red;'>❌ Category API failed: " . ($result['error'] ?? 'Unknown error') . "</p>\n";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ Category API error: " . $e->getMessage() . "</p>\n";
    }
    
    // Test 3: Test WordPressService category methods
    echo "<h2>Test 3: WordPressService Category Methods</h2>\n";
    
    try {
        $wordpressService = new WordPressService($db);
        $wordpressService->loadProfile($testProfile['id']);
        
        echo "<p>Testing fetchAvailableCategories()...</p>\n";
        $categoriesResult = $wordpressService->fetchAvailableCategories();
        
        if ($categoriesResult['success']) {
            echo "<p style='color: green;'>✅ fetchAvailableCategories() working. Found " . count($categoriesResult['categories']) . " categories.</p>\n";
            
            // Test category validation
            if (!empty($categoriesResult['categories'])) {
                $testCategoryIds = [
                    $categoriesResult['categories'][0]['id'], // Valid category
                    999999 // Invalid category
                ];
                
                echo "<p>Testing category validation with IDs: " . implode(', ', $testCategoryIds) . "</p>\n";
                
                // Use reflection to test private method
                $reflection = new ReflectionClass($wordpressService);
                $validateMethod = $reflection->getMethod('validateCategories');
                $validateMethod->setAccessible(true);
                
                $validationResult = $validateMethod->invoke($wordpressService, $testCategoryIds);
                
                if (!$validationResult['success']) {
                    echo "<p style='color: green;'>✅ Category validation working - correctly rejected invalid category ID 999999</p>\n";
                } else {
                    echo "<p style='color: orange;'>⚠️ Category validation may not be working properly</p>\n";
                }
            }
        } else {
            echo "<p style='color: red;'>❌ fetchAvailableCategories() failed: " . $categoriesResult['error'] . "</p>\n";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ WordPressService error: " . $e->getMessage() . "</p>\n";
    }
    
    // Test 4: Check database schema
    echo "<h2>Test 4: Database Schema</h2>\n";
    
    $tables = ['wordpress_profiles', 'wordpress_posts'];
    foreach ($tables as $table) {
        $exists = $db->fetchOne("SHOW TABLES LIKE ?", [$table]);
        if ($exists) {
            echo "<p style='color: green;'>✅ Table '{$table}' exists</p>\n";
        } else {
            echo "<p style='color: red;'>❌ Table '{$table}' missing</p>\n";
        }
    }
    
    echo "<h2>Test Summary</h2>\n";
    echo "<p>If all tests passed, the WordPress category functionality should be working correctly.</p>\n";
    echo "<p><strong>Next steps:</strong></p>\n";
    echo "<ul>\n";
    echo "<li>Go to <a href='settings.php'>Settings</a> to test the category dropdown in WordPress profiles</li>\n";
    echo "<li>Try posting a novel or chapter to test category selection in the posting workflow</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Test failed with error: " . $e->getMessage() . "</p>\n";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
p { margin: 10px 0; }
ul { margin: 5px 0 5px 20px; }
</style>
