<?php
/**
 * API Endpoint: Chapter Title Translation
 * PUT /api/chapter-titles.php - Translate chapter titles
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $novelManager = new NovelManager();
} catch (Exception $e) {
    logError('Failed to initialize NovelManager: ' . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'Database connection failed'
    ], 500);
}

try {
    switch ($method) {
        case 'PUT':
            handleTranslateChapterTitles($novelManager);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    logError('Chapter Titles API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    // Provide specific error messages
    $errorMessage = 'An error occurred while translating titles';
    $statusCode = 500;

    if (strpos($e->getMessage(), 'HTTP error: 429') !== false || strpos($e->getMessage(), 'quota') !== false) {
        $errorMessage = 'Translation service quota exceeded. Please wait and try again later.';
        $statusCode = 429;
    } elseif (strpos($e->getMessage(), 'HTTP error: 503') !== false || strpos($e->getMessage(), 'overloaded') !== false) {
        $errorMessage = 'Translation service is temporarily overloaded. Please try again in a few minutes.';
        $statusCode = 503;
    } elseif (strpos($e->getMessage(), 'timeout') !== false || strpos($e->getMessage(), 'timed out') !== false) {
        $errorMessage = 'Translation request timed out. Please try again.';
        $statusCode = 408;
    }

    jsonResponse([
        'success' => false,
        'error' => $errorMessage
    ], $statusCode);
}

/**
 * Handle PUT request - Translate chapter titles
 */
function handleTranslateChapterTitles($novelManager) {
    $rawInput = file_get_contents('php://input');

    // Enhanced logging for debugging
    file_put_contents('debug.log', "Chapter Titles API: Raw input received: " . $rawInput . "\n", FILE_APPEND);

    $input = json_decode($rawInput, true);
    $jsonError = json_last_error();

    if (!$input || $jsonError !== JSON_ERROR_NONE) {
        $errorMsg = 'Invalid JSON input';
        if ($jsonError !== JSON_ERROR_NONE) {
            $errorMsg .= ': ' . json_last_error_msg();
        }
        file_put_contents('debug.log', "Chapter Titles API: JSON decode error - " . $errorMsg . "\n", FILE_APPEND);
        jsonResponse(['error' => $errorMsg], 400);
    }

    // Enhanced validation with detailed logging
    if (!isset($input['novel_id'])) {
        file_put_contents('debug.log', "Chapter Titles API: Missing novel_id parameter\n", FILE_APPEND);
        jsonResponse(['error' => 'novel_id parameter is required'], 400);
    }

    if (!is_numeric($input['novel_id'])) {
        file_put_contents('debug.log', "Chapter Titles API: Invalid novel_id format: " . $input['novel_id'] . "\n", FILE_APPEND);
        jsonResponse(['error' => 'novel_id must be a valid number'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $targetLanguage = isset($input['target_language']) ? sanitizeInput($input['target_language']) : DEFAULT_TARGET_LANGUAGE;
    $forceRetranslate = isset($input['force_retranslate']) ? (bool)$input['force_retranslate'] : false;

    // Check for clear operation
    if (isset($input['action']) && $input['action'] === 'clear_translations') {
        file_put_contents('debug.log', "Chapter Titles API: Clear translations mode\n", FILE_APPEND);
        handleClearTranslatedTitles($novelManager, $novelId, $input);
        return;
    }

    // Support both single chapter and bulk operations for translation
    $chapterNumbers = [];
    if (isset($input['chapter_number']) && is_numeric($input['chapter_number'])) {
        // Single chapter operation
        $chapterNumbers = [(int)$input['chapter_number']];
        file_put_contents('debug.log', "Chapter Titles API: Single chapter mode - Chapter: " . $input['chapter_number'] . "\n", FILE_APPEND);
    } elseif (isset($input['chapter_numbers']) && is_array($input['chapter_numbers'])) {
        // Bulk operation
        $chapterNumbers = array_map('intval', $input['chapter_numbers']);
        file_put_contents('debug.log', "Chapter Titles API: Bulk mode - Chapters: " . implode(',', $chapterNumbers) . "\n", FILE_APPEND);
    } elseif (isset($input['translate_all']) && $input['translate_all'] === true) {
        // Translate all chapter titles for the novel
        $chapterNumbers = 'all';
        file_put_contents('debug.log', "Chapter Titles API: Translate all mode\n", FILE_APPEND);
    } else {
        file_put_contents('debug.log', "Chapter Titles API: No valid chapter specification found in input\n", FILE_APPEND);
        jsonResponse(['error' => 'Either chapter_number, chapter_numbers array, or translate_all flag is required'], 400);
    }

    file_put_contents('debug.log', "Chapter Titles API: Starting title translation - Novel ID: {$novelId}, Chapters: " . (is_array($chapterNumbers) ? implode(',', $chapterNumbers) : 'all') . ", Language: {$targetLanguage}, Force: " . ($forceRetranslate ? 'yes' : 'no') . "\n", FILE_APPEND);

    try {
        $result = $novelManager->translateChapterTitles($novelId, $chapterNumbers, $targetLanguage, $forceRetranslate);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            // Determine appropriate HTTP status code based on error type
            $statusCode = 400;
            $errorMessage = $result['error'] ?? 'Unknown error';

            // Check for specific error types in bulk results
            if (isset($result['results']) && is_array($result['results'])) {
                foreach ($result['results'] as $chapterResult) {
                    if (!$chapterResult['success'] && isset($chapterResult['error'])) {
                        $chapterError = $chapterResult['error'];

                        if (strpos($chapterError, 'HTTP error: 503') !== false) {
                            $statusCode = 503;
                            $errorMessage = $chapterError;
                            break;
                        } elseif (strpos($chapterError, 'HTTP error: 429') !== false) {
                            $statusCode = 429;
                            $errorMessage = $chapterError;
                            break;
                        } elseif (strpos($chapterError, 'timeout') !== false) {
                            $statusCode = 408;
                            $errorMessage = $chapterError;
                            break;
                        }
                    }
                }
            }

            jsonResponse([
                'success' => false,
                'error' => $errorMessage
            ], $statusCode);
        }
    } catch (Exception $e) {
        file_put_contents('debug.log', "Title translation exception: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString() . "\n", FILE_APPEND);

        $statusCode = 500;
        $errorMessage = 'Title translation failed: ' . $e->getMessage();

        if (strpos($e->getMessage(), 'HTTP error: 503') !== false) {
            $statusCode = 503;
            $errorMessage = 'Translation service is temporarily overloaded. Please try again in a few minutes.';
        } elseif (strpos($e->getMessage(), 'HTTP error: 429') !== false) {
            $statusCode = 429;
            $errorMessage = 'Translation service quota exceeded. Please wait and try again later.';
        } elseif (strpos($e->getMessage(), 'timeout') !== false) {
            $statusCode = 408;
            $errorMessage = 'Translation request timed out. Please try again.';
        }

        jsonResponse([
            'success' => false,
            'error' => $errorMessage
        ], $statusCode);
    }
}

/**
 * Handle clear translated titles request
 */
function handleClearTranslatedTitles($novelManager, $novelId, $input) {
    // Determine which chapters to clear
    $chapterNumbers = 'all'; // Default to all chapters

    if (isset($input['chapter_number']) && is_numeric($input['chapter_number'])) {
        // Single chapter operation
        $chapterNumbers = [(int)$input['chapter_number']];
        file_put_contents('debug.log', "Chapter Titles API: Clear single chapter - Chapter: " . $input['chapter_number'] . "\n", FILE_APPEND);
    } elseif (isset($input['chapter_numbers']) && is_array($input['chapter_numbers'])) {
        // Bulk operation
        $chapterNumbers = array_map('intval', $input['chapter_numbers']);
        file_put_contents('debug.log', "Chapter Titles API: Clear bulk chapters - Chapters: " . implode(',', $chapterNumbers) . "\n", FILE_APPEND);
    } elseif (isset($input['clear_all']) && $input['clear_all'] === true) {
        // Clear all translated titles for the novel
        $chapterNumbers = 'all';
        file_put_contents('debug.log', "Chapter Titles API: Clear all translated titles\n", FILE_APPEND);
    }

    file_put_contents('debug.log', "Chapter Titles API: Starting clear operation - Novel ID: {$novelId}, Chapters: " . (is_array($chapterNumbers) ? implode(',', $chapterNumbers) : 'all') . "\n", FILE_APPEND);

    try {
        $result = $novelManager->clearTranslatedTitles($novelId, $chapterNumbers);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error'] ?? 'Unknown error occurred while clearing titles'
            ], 400);
        }
    } catch (Exception $e) {
        file_put_contents('debug.log', "Clear titles exception: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString() . "\n", FILE_APPEND);

        jsonResponse([
            'success' => false,
            'error' => 'Failed to clear translated titles: ' . $e->getMessage()
        ], 500);
    }
}
?>
