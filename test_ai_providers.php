<?php
/**
 * AI Provider Integration Test Script
 * Tests all AI providers to ensure they work correctly
 */

require_once 'config/config.php';
require_once 'classes/TranslationService.php';
require_once 'classes/AIProviderManager.php';

echo "=== AI Provider Integration Test ===\n\n";

try {
    // Initialize services
    $translationService = new TranslationService();
    $providerManager = new AIProviderManager();
    
    echo "✓ Services initialized successfully\n\n";
    
    // Test 1: Check available providers
    echo "1. Testing Available Providers:\n";
    $availableProviders = $translationService->getAvailableProviders();
    
    foreach ($availableProviders as $key => $provider) {
        echo "   - {$key}: {$provider['name']} - {$provider['description']}\n";
    }
    echo "\n";
    
    // Test 2: Check current active provider
    echo "2. Current Active Provider:\n";
    $currentProvider = $translationService->getActiveProvider();
    $currentProviderName = $availableProviders[$currentProvider]['name'] ?? $currentProvider;
    echo "   Active: {$currentProvider} ({$currentProviderName})\n\n";
    
    // Test 3: Test each provider with a simple translation
    echo "3. Testing Provider Connections:\n";
    $testText = "こんにちは";
    $testResults = [];
    
    foreach (array_keys($availableProviders) as $providerType) {
        echo "   Testing {$providerType}... ";
        
        $testResult = $translationService->testProvider($providerType);
        $testResults[$providerType] = $testResult;
        
        if ($testResult['success']) {
            echo "✓ SUCCESS (Time: {$testResult['execution_time']}s)\n";
        } else {
            echo "✗ FAILED: {$testResult['error']}\n";
        }
    }
    echo "\n";
    
    // Test 4: Test provider switching
    echo "4. Testing Provider Switching:\n";
    $originalProvider = $translationService->getActiveProvider();
    
    foreach (array_keys($availableProviders) as $providerType) {
        if ($providerType === $originalProvider) continue;
        
        echo "   Switching to {$providerType}... ";
        $switchResult = $translationService->setActiveProvider($providerType);
        
        if ($switchResult) {
            $newProvider = $translationService->getActiveProvider();
            if ($newProvider === $providerType) {
                echo "✓ SUCCESS\n";
            } else {
                echo "✗ FAILED: Provider not changed\n";
            }
        } else {
            echo "✗ FAILED: Switch operation failed\n";
        }
    }
    
    // Restore original provider
    echo "   Restoring original provider ({$originalProvider})... ";
    $restoreResult = $translationService->setActiveProvider($originalProvider);
    echo $restoreResult ? "✓ SUCCESS\n" : "✗ FAILED\n";
    echo "\n";
    
    // Test 5: Test actual translation with different providers
    echo "5. Testing Actual Translation:\n";
    $translationTestText = "今日は良い天気ですね。";
    
    foreach (array_keys($availableProviders) as $providerType) {
        // Skip providers that failed connection test
        if (!$testResults[$providerType]['success']) {
            echo "   Skipping {$providerType} (connection failed)\n";
            continue;
        }
        
        echo "   Testing translation with {$providerType}... ";
        
        // Switch to provider
        $translationService->setActiveProvider($providerType);
        
        // Perform translation
        $translationResult = $translationService->translateText(
            $translationTestText,
            'en',
            'ja',
            ['simple' => true, 'type' => 'test']
        );
        
        if ($translationResult['success']) {
            $translatedText = substr($translationResult['translated_text'], 0, 50);
            echo "✓ SUCCESS: \"{$translatedText}\" (Time: {$translationResult['execution_time']}s)\n";
        } else {
            echo "✗ FAILED: {$translationResult['error']}\n";
        }
    }
    
    // Restore original provider
    $translationService->setActiveProvider($originalProvider);
    echo "\n";
    
    // Test 6: Test mixed mode
    echo "6. Testing Mixed Mode:\n";
    echo "   Switching to mixed mode... ";
    $mixedResult = $translationService->setActiveProvider('mixed');
    
    if ($mixedResult) {
        echo "✓ SUCCESS\n";
        
        echo "   Testing mixed mode translation... ";
        $mixedTranslationResult = $translationService->translateText(
            $translationTestText,
            'en',
            'ja',
            ['simple' => true, 'type' => 'test']
        );
        
        if ($mixedTranslationResult['success']) {
            $providerUsed = $mixedTranslationResult['provider_used'] ?? 'unknown';
            $fallbackUsed = $mixedTranslationResult['fallback_used'] ?? false;
            $translatedText = substr($mixedTranslationResult['translated_text'], 0, 50);
            
            echo "✓ SUCCESS: \"{$translatedText}\"\n";
            echo "     Provider used: {$providerUsed}\n";
            echo "     Fallback used: " . ($fallbackUsed ? 'Yes' : 'No') . "\n";
            echo "     Time: {$mixedTranslationResult['execution_time']}s\n";
        } else {
            echo "✗ FAILED: {$mixedTranslationResult['error']}\n";
        }
    } else {
        echo "✗ FAILED\n";
    }
    
    // Restore original provider
    $translationService->setActiveProvider($originalProvider);
    echo "\n";
    
    // Test 7: Check usage statistics
    echo "7. Usage Statistics:\n";
    $usageStats = $translationService->getProviderUsageStatistics(1);
    
    if (!empty($usageStats)) {
        foreach ($usageStats as $stat) {
            $provider = $stat['provider_type'];
            $translations = $stat['translation_count'];
            $success = $stat['success_count'];
            $errors = $stat['error_count'];
            $successRate = $translations > 0 ? round(($success / $translations) * 100, 1) : 0;
            
            echo "   {$provider}: {$translations} translations, {$successRate}% success rate\n";
        }
    } else {
        echo "   No usage statistics available\n";
    }
    echo "\n";
    
    echo "=== Test Summary ===\n";
    echo "✓ All tests completed successfully!\n";
    echo "✓ AI Provider integration is working correctly\n";
    echo "✓ Provider switching is functional\n";
    echo "✓ Mixed mode is operational\n";
    echo "✓ Translation functionality is preserved\n\n";
    
    echo "Current configuration:\n";
    echo "- Active Provider: {$translationService->getActiveProvider()}\n";
    echo "- Available Providers: " . count($availableProviders) . "\n";
    echo "- Mixed Mode Primary: " . MIXED_MODE_PRIMARY . "\n";
    echo "- Mixed Mode Fallback: " . MIXED_MODE_FALLBACK . "\n";
    
} catch (Exception $e) {
    echo "\n✗ TEST FAILED: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}

echo "\n=== Integration Test Complete ===\n";
?>
