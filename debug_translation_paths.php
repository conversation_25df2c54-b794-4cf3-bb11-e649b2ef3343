<?php
/**
 * Debug which translation paths are being used
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

echo "=== Debugging Translation Paths ===\n\n";

// Clear debug log first
file_put_contents('debug.log', '');

try {
    $translationService = new TranslationService();
    $db = Database::getInstance();
    
    // Get family terms from name dictionary
    $familyNames = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['兄さん', '兄', '父', '母', 'お父さん', 'お母さん', 'お兄ちゃん', 'お兄さん']
    );
    
    echo "Family terms in dictionary:\n";
    foreach ($familyNames as $name) {
        echo "- {$name['original_name']} → {$name['romanization']} → {$name['translation']}\n";
    }
    echo "\n";
    
    // Test with a problematic sentence that contains family terms
    $testText = "彼女の母は貴族だった。父からの手紙が届いた。兄は忙しい。";
    
    echo "=== Testing Translation Path ===\n";
    echo "Test text: {$testText}\n";
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $familyNames
    ];
    
    $result = $translationService->translateText(
        $testText,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        $translation = $result['translated_text'];
        echo "Translation: {$translation}\n";
        echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
        echo "Provider used: " . ($result['provider_used'] ?? 'unknown') . "\n";
        echo "Fallback used: " . ($result['fallback_used'] ? 'Yes' : 'No') . "\n";
        
        // Check for family term issues
        $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $foundForbidden = [];
        
        foreach ($forbiddenTerms as $term) {
            if (stripos($translation, $term) !== false) {
                $foundForbidden[] = $term;
            }
        }
        
        if (empty($foundForbidden)) {
            echo "✅ GOOD: No forbidden family terms\n";
        } else {
            echo "❌ PROBLEM: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
        }
        
    } else {
        echo "❌ Translation failed: " . $result['error'] . "\n";
    }
    
    echo "\n=== Debug Log Analysis ===\n";
    
    // Read debug log to see which path was taken
    $debugLog = file_get_contents('debug.log');
    $debugLines = explode("\n", $debugLog);
    
    echo "Debug log entries:\n";
    foreach ($debugLines as $line) {
        if (!empty(trim($line))) {
            echo "- {$line}\n";
        }
    }
    
    // Analyze which translation service was actually used
    if (strpos($debugLog, 'Provider Manager') !== false) {
        echo "\n🔍 ANALYSIS: Used Provider Manager system\n";
    }
    
    if (strpos($debugLog, 'legacy Gemini fallback') !== false) {
        echo "\n🔍 ANALYSIS: Used Legacy Gemini Fallback system\n";
        echo "This means the provider manager failed and fell back to legacy method\n";
    }
    
    if (strpos($debugLog, 'gemini_fallback') !== false) {
        echo "\n🔍 ANALYSIS: Used Gemini Fallback API\n";
        echo "This means the primary API failed and used fallback API\n";
    }
    
    // Test with longer content to see if behavior changes
    echo "\n=== Testing with Longer Content ===\n";
    
    $longerText = str_repeat($testText . " ", 10); // Repeat to make it longer
    echo "Longer text length: " . strlen($longerText) . " characters\n";
    
    $result2 = $translationService->translateText(
        $longerText,
        'en',
        'ja',
        $context
    );
    
    if ($result2['success']) {
        echo "Longer translation successful\n";
        echo "API used: " . ($result2['api_used'] ?? 'unknown') . "\n";
        echo "Provider used: " . ($result2['provider_used'] ?? 'unknown') . "\n";
        echo "Fallback used: " . ($result2['fallback_used'] ? 'Yes' : 'No') . "\n";
        
        // Check for family term issues in longer text
        $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $foundForbidden = [];
        
        foreach ($forbiddenTerms as $term) {
            $count = substr_count(strtolower($result2['translated_text']), strtolower($term));
            if ($count > 0) {
                $foundForbidden[] = "{$term}: {$count}";
            }
        }
        
        if (empty($foundForbidden)) {
            echo "✅ GOOD: No forbidden family terms in longer text\n";
        } else {
            echo "❌ PROBLEM: Found forbidden terms in longer text: " . implode(', ', $foundForbidden) . "\n";
        }
        
    } else {
        echo "❌ Longer translation failed: " . $result2['error'] . "\n";
    }
    
    // Read updated debug log
    echo "\n=== Updated Debug Log ===\n";
    $updatedDebugLog = file_get_contents('debug.log');
    $newLines = explode("\n", $updatedDebugLog);
    
    // Show only new lines
    $startIndex = count($debugLines);
    for ($i = $startIndex; $i < count($newLines); $i++) {
        if (!empty(trim($newLines[$i]))) {
            echo "- {$newLines[$i]}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
