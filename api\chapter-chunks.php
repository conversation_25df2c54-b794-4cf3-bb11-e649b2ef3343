<?php
/**
 * API Endpoint: Chapter Chunks Management
 * GET /api/chapter-chunks.php?chapter_id=<id> - Get chunks for a chapter
 * POST /api/chapter-chunks.php - Create chunks for a chapter
 * PUT /api/chapter-chunks.php - Translate specific chunk
 * DELETE /api/chapter-chunks.php - Clear chunk translations
 */

// Prevent any output before JSON response
ob_start();

require_once '../config/config.php';

// Set longer execution time for translation requests
set_time_limit(600); // 10 minutes
ini_set('max_execution_time', 600);

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

// Clear any output buffer to prevent HTML/error output in JSON response
ob_clean();

try {
    $novelManager = new NovelManager();
    $chapterChunker = new ChapterChunker();
} catch (Exception $e) {
    logError('Failed to initialize services: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);
    jsonResponse([
        'success' => false,
        'error' => 'Service initialization failed: ' . $e->getMessage()
    ], 500);
}

try {
    switch ($method) {
        case 'GET':
            handleGetChapterChunks($novelManager, $chapterChunker);
            break;

        case 'POST':
            handleCreateChunks($novelManager, $chapterChunker);
            break;

        case 'PUT':
            handleTranslateChunk($novelManager);
            break;

        case 'DELETE':
            handleClearChunkTranslations($novelManager);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    logError('Chapter Chunks API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'An error occurred while processing your request'
    ], 500);
}

/**
 * Handle GET request - Get chunks for a chapter
 */
function handleGetChapterChunks($novelManager, $chapterChunker) {
    if (!isset($_GET['chapter_id']) || !is_numeric($_GET['chapter_id'])) {
        jsonResponse(['error' => 'Valid chapter_id parameter is required'], 400);
    }

    $chapterId = (int)$_GET['chapter_id'];

    try {
        // Log the request
        logError('Chapter Chunks API: GET request received', [
            'chapter_id' => $chapterId,
            'method' => 'GET'
        ]);

        $db = Database::getInstance();

        // Get chapter info
        $chapter = $db->fetchOne(
            "SELECT c.*, n.id as novel_id FROM chapters c
             JOIN novels n ON c.novel_id = n.id
             WHERE c.id = ?",
            [$chapterId]
        );

        if (!$chapter) {
            jsonResponse(['error' => 'Chapter not found'], 404);
        }

        // Get chunks for this chapter
        $chunks = $chapterChunker->getChapterChunks($chapterId);

        // Calculate progress
        $totalChunks = count($chunks);
        $completedChunks = 0;
        $translatingChunks = 0;
        $errorChunks = 0;

        foreach ($chunks as $chunk) {
            switch ($chunk['translation_status']) {
                case 'completed':
                    $completedChunks++;
                    break;
                case 'translating':
                    $translatingChunks++;
                    break;
                case 'error':
                    $errorChunks++;
                    break;
            }
        }

        jsonResponse([
            'success' => true,
            'data' => [
                'chapter' => $chapter,
                'chunks' => $chunks,
                'progress' => [
                    'total' => $totalChunks,
                    'completed' => $completedChunks,
                    'translating' => $translatingChunks,
                    'error' => $errorChunks,
                    'pending' => $totalChunks - $completedChunks - $translatingChunks - $errorChunks,
                    'percentage' => $totalChunks > 0 ? round(($completedChunks / $totalChunks) * 100, 1) : 0
                ]
            ]
        ]);

    } catch (Exception $e) {
        logError('Chapter Chunks API: GET request error', [
            'chapter_id' => $chapterId,
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);

        jsonResponse([
            'success' => false,
            'error' => 'Failed to retrieve chunks: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle POST request - Create chunks for a chapter
 */
function handleCreateChunks($novelManager, $chapterChunker) {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input || !isset($input['chapter_id'])) {
        jsonResponse(['error' => 'Chapter ID is required'], 400);
    }

    $chapterId = (int)$input['chapter_id'];
    $forceRechunk = isset($input['force_rechunk']) ? (bool)$input['force_rechunk'] : false;

    try {
        $db = Database::getInstance();
        
        // Get chapter content
        $chapter = $db->fetchOne(
            "SELECT * FROM chapters WHERE id = ?",
            [$chapterId]
        );

        if (!$chapter) {
            jsonResponse(['error' => 'Chapter not found'], 404);
        }

        if (empty($chapter['original_content'])) {
            jsonResponse(['error' => 'Chapter has no content to chunk'], 400);
        }

        // Check if already has chunks and not forcing rechunk
        if (!$forceRechunk && $chapterChunker->hasChunks($chapterId)) {
            jsonResponse([
                'success' => true,
                'message' => 'Chapter already has chunks',
                'chunks_created' => 0
            ]);
        }

        // Create chunks
        $result = $chapterChunker->splitChapter($chapterId, $chapter['original_content']);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'message' => 'Chunks created successfully',
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => 'Chunking failed: ' . $result['error']
            ], 500);
        }

    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * Handle PUT request - Translate specific chunk
 */
function handleTranslateChunk($novelManager) {
    try {
        $rawInput = file_get_contents('php://input');

        if ($rawInput === false) {
            jsonResponse(['error' => 'Failed to read request body'], 400);
        }

        $input = json_decode($rawInput, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            jsonResponse(['error' => 'Invalid JSON in request body: ' . json_last_error_msg()], 400);
        }

        if (!$input || !isset($input['chunk_id'])) {
            jsonResponse(['error' => 'Chunk ID is required'], 400);
        }

        $chunkId = (int)$input['chunk_id'];
        $targetLanguage = $input['target_language'] ?? 'en';
        $povPreferenceOverride = $input['pov_preference'] ?? null;

        // Log the translation request
        logError('Chapter Chunks API: Translation request received', [
            'chunk_id' => $chunkId,
            'target_language' => $targetLanguage,
            'raw_input' => $rawInput
        ]);



        // Get chunk info with chapter and novel data for validation and POV preferences
        $db = Database::getInstance();
        $chunk = $db->fetchOne(
            "SELECT cc.character_count, cc.original_content, cc.translation_status, cc.chapter_id,
                    c.novel_id, c.chapter_number
             FROM chapter_chunks cc
             JOIN chapters c ON cc.chapter_id = c.id
             WHERE cc.id = ?",
            [$chunkId]
        );

        if (!$chunk) {
            jsonResponse(['error' => 'Chunk not found'], 404);
        }

        // Get POV preference for this chapter/novel
        $povManager = new POVPreferenceManager();
        $povPreference = $povPreferenceOverride ?: $povManager->getChapterPOVPreference($chunk['novel_id'], $chunk['chapter_id']);

        // Create translation context with POV preference
        $translationContext = [];
        if ($povPreference) {
            $translationContext['user_pov_preference'] = $povPreference;
            logError('Chapter Chunks API: Using POV preference', [
                'chunk_id' => $chunkId,
                'novel_id' => $chunk['novel_id'],
                'chapter_id' => $chunk['chapter_id'],
                'pov_preference' => $povPreference,
                'source' => $povPreferenceOverride ? 'request_override' : 'stored_preference'
            ]);
        }

        // Add narrative context with POV analysis for proper perspective handling
        if ($povPreference) {
            require_once '../classes/PerspectiveService.php';
            $perspectiveService = new PerspectiveService();

            // Get chunk content for perspective analysis
            $chunkContent = $db->fetchOne(
                "SELECT original_content FROM chapter_chunks WHERE id = ?",
                [$chunkId]
            );

            if ($chunkContent && !empty($chunkContent['original_content'])) {
                // Generate perspective analysis with POV preference
                $perspectiveAnalysis = $perspectiveService->determineOptimalPerspective(
                    $chunkContent['original_content'],
                    array_merge($translationContext, ['type' => 'chunk'])
                );

                // Create narrative context with perspective analysis
                $translationContext['narrative_context'] = [
                    'content_type' => $perspectiveAnalysis['content_type'],
                    'optimal_perspective' => $perspectiveAnalysis['optimal_perspective'],
                    'perspective_reasoning' => $perspectiveAnalysis['reasoning'],
                    'perspective_instructions' => $perspectiveAnalysis['instructions'],
                    'user_selected' => $perspectiveAnalysis['user_selected'] ?? false
                ];

                logError('Chapter Chunks API: Added narrative context with POV analysis', [
                    'chunk_id' => $chunkId,
                    'optimal_perspective' => $perspectiveAnalysis['optimal_perspective'],
                    'user_selected' => $perspectiveAnalysis['user_selected'] ?? false
                ]);
            }
        }

        // Check if chunk is already being translated (prevent concurrent translations)
        if ($chunk['translation_status'] === 'translating') {
            jsonResponse([
                'error' => 'Chunk is already being translated. Please wait for the current translation to complete or reset the chunk status.'
            ], 409); // Conflict status
        }

        // Reset error status to allow retry
        if ($chunk['translation_status'] === 'error') {
            logError('Chapter Chunks API: Resetting error chunk for retry', [
                'chunk_id' => $chunkId,
                'previous_status' => 'error'
            ]);

            $db->update('chapter_chunks',
                ['translation_status' => 'pending'],
                'id = ?',
                [$chunkId]
            );
        }

        // Validate chunk size and provide helpful guidance
        $maxChunkSize = defined('CHUNK_SIZE_LIMIT') ? CHUNK_SIZE_LIMIT : 5000;
        $maxRecommendedSize = $maxChunkSize * 2; // Allow 2x buffer for edge cases

        if ($chunk['character_count'] > $maxRecommendedSize) {
            jsonResponse([
                'error' => "Chunk is too large ({$chunk['character_count']} characters). Maximum recommended size is {$maxRecommendedSize} characters. Large chunks may cause timeouts. Please re-chunk this chapter with smaller chunk sizes."
            ], 400);
        }

        // Warn about potentially slow chunks
        if ($chunk['character_count'] > $maxChunkSize) {
            logError('Large chunk translation attempted', [
                'chunk_id' => $chunkId,
                'character_count' => $chunk['character_count'],
                'max_recommended' => $maxChunkSize
            ]);
        }

        // Set up timeout recovery
        $startTime = time();

        // Register shutdown function to reset chunk status if script times out
        register_shutdown_function(function() use ($chunkId, $startTime) {
            $error = error_get_last();
            if ($error && (strpos($error['message'], 'Maximum execution time') !== false ||
                          strpos($error['message'], 'timeout') !== false)) {
                try {
                    $db = Database::getInstance();
                    $db->update('chapter_chunks',
                        ['translation_status' => 'error'],
                        'id = ?',
                        [$chunkId]
                    );
                    logError('Translation timeout recovery: Reset chunk status', [
                        'chunk_id' => $chunkId,
                        'execution_time' => time() - $startTime
                    ]);
                } catch (Exception $e) {
                    // Ignore errors in shutdown function
                }
            }
        });

        $result = $novelManager->translateSingleChunk($chunkId, $targetLanguage, $translationContext);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'message' => 'Chunk translated successfully',
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 500);
        }

    } catch (Exception $e) {
        logError('Chunk translation error: ' . $e->getMessage(), [
            'chunk_id' => $chunkId ?? 'unknown',
            'target_language' => $targetLanguage ?? 'unknown',
            'trace' => $e->getTraceAsString()
        ]);

        jsonResponse([
            'success' => false,
            'error' => 'Translation failed: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle DELETE request - Clear chunk translations
 */
function handleClearChunkTranslations($novelManager) {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input || !isset($input['chapter_id'])) {
        jsonResponse(['error' => 'Chapter ID is required'], 400);
    }

    $chapterId = (int)$input['chapter_id'];
    $chunkIds = $input['chunk_ids'] ?? null; // Optional: specific chunks to clear

    try {
        $db = Database::getInstance();
        
        if ($chunkIds && is_array($chunkIds)) {
            // Clear specific chunks
            $placeholders = str_repeat('?,', count($chunkIds) - 1) . '?';
            $stmt = $db->query(
                "UPDATE chapter_chunks SET translated_content = NULL, translation_status = 'pending', translation_date = NULL
                 WHERE id IN ($placeholders) AND chapter_id = ?",
                array_merge($chunkIds, [$chapterId])
            );
            $message = 'Selected chunk translations cleared';
        } else {
            // Clear all chunks for chapter
            $stmt = $db->query(
                "UPDATE chapter_chunks SET translated_content = NULL, translation_status = 'pending', translation_date = NULL
                 WHERE chapter_id = ?",
                [$chapterId]
            );
            $message = 'All chunk translations cleared';
        }

        jsonResponse([
            'success' => true,
            'message' => $message
        ]);

    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}
?>
