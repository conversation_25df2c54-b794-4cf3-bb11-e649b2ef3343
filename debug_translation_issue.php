<?php
/**
 * Debug translation issues with simpler approach
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Debugging Translation Issues ===\n\n";

try {
    $providerManager = new AIProviderManager();
    $providerManager->setActiveProvider('gemini_15');
    $translationService = $providerManager->getTranslationService('gemini_15');
    
    echo "Using Gemini 1.5\n\n";
    
    // Test 1: Very simple family term
    echo "=== Test 1: Simple Family Term ===\n";
    $simpleTest = "兄さんは元気です。";
    echo "Original: {$simpleTest}\n";
    
    $result = $translationService->translateText(
        $simpleTest,
        'en',
        'ja',
        ['type' => 'simple', 'simple' => true]
    );
    
    if ($result['success']) {
        echo "Translation: {$result['translated_text']}\n";
        echo "✅ Simple test passed\n";
    } else {
        echo "❌ Simple test failed: {$result['error']}\n";
    }
    
    echo "\n";
    
    // Test 2: With minimal name dictionary
    echo "=== Test 2: With Name Dictionary ===\n";
    $db = Database::getInstance();
    
    // Get only family terms from name dictionary
    $familyNames = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['兄さん', '兄', '父', '母', 'お父さん', 'お母さん']
    );
    
    echo "Family terms in dictionary: " . count($familyNames) . "\n";
    foreach ($familyNames as $name) {
        echo "- {$name['original_name']} → {$name['romanization']} → {$name['translation']}\n";
    }
    echo "\n";
    
    $context = [
        'type' => 'test',
        'novel_id' => 7,
        'names' => $familyNames
    ];
    
    $testText = "兄さんは忙しいです。お父さんも働いています。";
    echo "Original: {$testText}\n";
    
    $result = $translationService->translateText(
        $testText,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        echo "Translation: {$result['translated_text']}\n";
        
        // Check for issues
        $problematicTerms = ['father', 'mother', 'brother', 'sister'];
        $foundProblems = [];
        
        foreach ($problematicTerms as $term) {
            if (stripos($result['translated_text'], $term) !== false) {
                $foundProblems[] = $term;
            }
        }
        
        if (empty($foundProblems)) {
            echo "✅ No English family terms found\n";
        } else {
            echo "❌ Found English family terms: " . implode(', ', $foundProblems) . "\n";
        }
        
    } else {
        echo "❌ Test with name dictionary failed: {$result['error']}\n";
    }
    
    echo "\n";
    
    // Test 3: Check what happens with actual chapter content (small piece)
    echo "=== Test 3: Actual Chapter Content (Small) ===\n";
    
    $chapter = $db->fetchOne(
        'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
        []
    );
    
    // Get just the first paragraph with family terms
    $lines = explode("\n", $chapter['original_content']);
    $testParagraph = '';
    
    foreach ($lines as $line) {
        $line = trim($line);
        if (strlen($line) > 20 && (strpos($line, '兄') !== false || strpos($line, '父') !== false || strpos($line, '母') !== false)) {
            $testParagraph = $line;
            break;
        }
    }
    
    if (!empty($testParagraph)) {
        echo "Test paragraph: {$testParagraph}\n";
        echo "Length: " . strlen($testParagraph) . " characters\n";
        
        $result = $translationService->translateText(
            $testParagraph,
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            echo "Translation: {$result['translated_text']}\n";
            
            // Check for family term issues
            $problematicTerms = ['father', 'mother', 'brother', 'sister'];
            $foundProblems = [];
            
            foreach ($problematicTerms as $term) {
                if (stripos($result['translated_text'], $term) !== false) {
                    $foundProblems[] = $term;
                }
            }
            
            if (empty($foundProblems)) {
                echo "✅ No English family terms in actual content\n";
            } else {
                echo "❌ CONFIRMED ISSUE: Found English family terms in actual content: " . implode(', ', $foundProblems) . "\n";
                echo "🔍 This confirms the problem exists with real chapter content\n";
            }
            
        } else {
            echo "❌ Actual content test failed: {$result['error']}\n";
        }
    } else {
        echo "No suitable paragraph found\n";
    }
    
    // Test 4: Check current translated content
    echo "\n=== Test 4: Check Current Translated Content ===\n";
    $currentChapter = $db->fetchOne(
        'SELECT translated_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
        []
    );
    
    $currentTranslation = $currentChapter['translated_content'];
    echo "Current translation length: " . strlen($currentTranslation) . " characters\n";
    
    // Check for family term issues in current translation
    $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
    $currentProblems = [];
    
    foreach ($problematicTerms as $term) {
        $count = substr_count(strtolower($currentTranslation), strtolower($term));
        if ($count > 0) {
            $currentProblems[] = "{$term}: {$count}";
        }
    }
    
    if (empty($currentProblems)) {
        echo "✅ Current translation has no English family terms\n";
    } else {
        echo "❌ Current translation still has English family terms:\n";
        foreach ($currentProblems as $problem) {
            echo "  - {$problem}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
