<?php
/**
 * Debug Chapter 56 specific translation issues
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

echo "=== Debugging Chapter 56 Specific Issues ===\n\n";

// Clear debug log
file_put_contents('debug.log', '');

$db = Database::getInstance();

// Get the exact content that's causing problems
$chapter = $db->fetchOne(
    'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
    []
);

// Based on our analysis, the problematic area is around position 4000-6000
// Let's extract the exact sentences that contain family terms
$originalContent = $chapter['original_content'];

echo "=== Finding Problematic Sentences ===\n";

// Split into sentences and find those with family terms
$sentences = preg_split('/[。！？]/', $originalContent);
$problematicSentences = [];

foreach ($sentences as $i => $sentence) {
    $sentence = trim($sentence);
    if (empty($sentence)) continue;
    
    // Check if sentence contains family terms
    if (preg_match('/[父母兄姉]/', $sentence)) {
        $problematicSentences[] = [
            'index' => $i,
            'sentence' => $sentence . '。',
            'position' => strpos($originalContent, $sentence)
        ];
    }
}

echo "Found " . count($problematicSentences) . " sentences with family terms:\n\n";

try {
    $translationService = new TranslationService();
    
    // Get family terms from name dictionary
    $familyNames = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['兄さん', '兄', '父', '母', 'お父さん', 'お母さん', 'お兄ちゃん', 'お兄さん']
    );
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $familyNames
    ];
    
    // Test each problematic sentence individually
    foreach ($problematicSentences as $i => $sentenceData) {
        echo "=== Sentence " . ($i + 1) . " (Position: {$sentenceData['position']}) ===\n";
        echo "Original: {$sentenceData['sentence']}\n";
        
        $result = $translationService->translateText(
            $sentenceData['sentence'],
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            $translation = $result['translated_text'];
            echo "Translation: {$translation}\n";
            echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
            
            // Check for family term issues
            $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
            $foundForbidden = [];
            
            foreach ($forbiddenTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundForbidden[] = $term;
                }
            }
            
            // Check for romanized terms
            $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san'];
            $foundExpected = [];
            
            foreach ($expectedTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundExpected[] = $term;
                }
            }
            
            if (empty($foundForbidden) && !empty($foundExpected)) {
                echo "✅ PERFECT: Found romanized: " . implode(', ', $foundExpected) . "\n";
            } elseif (empty($foundForbidden)) {
                echo "✅ GOOD: No forbidden terms\n";
            } else {
                echo "❌ PROBLEM: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
                echo "🔍 This sentence is causing issues!\n";
            }
            
        } else {
            echo "❌ Translation failed: " . $result['error'] . "\n";
        }
        
        echo "\n";
        
        // Only test first 5 sentences to avoid too much output
        if ($i >= 4) {
            echo "... (testing first 5 sentences only)\n\n";
            break;
        }
    }
    
    // Now test a larger chunk that includes multiple family terms
    echo "=== Testing Larger Chunk with Multiple Family Terms ===\n";
    
    // Find a chunk that contains multiple family terms
    $chunkStart = 0;
    $chunkSize = 2000;
    $bestChunk = '';
    $maxFamilyTerms = 0;
    
    for ($pos = 0; $pos < strlen($originalContent) - $chunkSize; $pos += 1000) {
        $chunk = substr($originalContent, $pos, $chunkSize);
        $familyTermCount = preg_match_all('/[父母兄姉]/', $chunk);
        
        if ($familyTermCount > $maxFamilyTerms) {
            $maxFamilyTerms = $familyTermCount;
            $bestChunk = $chunk;
            $chunkStart = $pos;
        }
    }
    
    echo "Best chunk (position {$chunkStart}, {$maxFamilyTerms} family terms):\n";
    echo "Sample: " . substr($bestChunk, 0, 200) . "...\n";
    
    $result = $translationService->translateText(
        $bestChunk,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        $translation = $result['translated_text'];
        echo "Chunk translation successful!\n";
        echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
        
        // Check for family term issues in chunk
        $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $foundForbidden = [];
        
        foreach ($forbiddenTerms as $term) {
            $count = substr_count(strtolower($translation), strtolower($term));
            if ($count > 0) {
                $foundForbidden[] = "{$term}: {$count}";
            }
        }
        
        if (empty($foundForbidden)) {
            echo "✅ EXCELLENT: No forbidden family terms in chunk\n";
        } else {
            echo "❌ CHUNK PROBLEM: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
            echo "🔍 This suggests the issue appears in larger content\n";
        }
        
        // Show sample of translation
        echo "Translation sample: " . substr($translation, 0, 300) . "...\n";
        
    } else {
        echo "❌ Chunk translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Debug Complete ===\n";
