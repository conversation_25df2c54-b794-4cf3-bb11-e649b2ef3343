<?php
/**
 * Test translation with longer content to see if family term issues appear
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing Longer Content Translation ===\n\n";

$db = Database::getInstance();

// Get original content of chapter 56
$chapter = $db->fetchOne(
    'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
    []
);

$originalContent = $chapter['original_content'];

// Test with progressively longer samples
$testSizes = [1000, 2000, 3000];

try {
    $providerManager = new AIProviderManager();
    $providerManager->setActiveProvider('gemini_15'); // Use stable version
    $translationService = $providerManager->getTranslationService('gemini_15');
    
    // Get name dictionary
    $names = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         ORDER BY frequency DESC',
        []
    );
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $names
    ];
    
    echo "Using name dictionary with " . count($names) . " entries\n\n";
    
    foreach ($testSizes as $size) {
        echo "=== Testing with {$size} characters ===\n";
        
        $sample = substr($originalContent, 0, $size);
        echo "Sample length: " . strlen($sample) . " characters\n";
        
        // Count family terms in this sample
        $familyTerms = ['父', '母', '兄さん', '兄'];
        $termCounts = [];
        foreach ($familyTerms as $term) {
            $count = substr_count($sample, $term);
            if ($count > 0) {
                $termCounts[] = "{$term}: {$count}";
            }
        }
        echo "Family terms in sample: " . (empty($termCounts) ? "none" : implode(', ', $termCounts)) . "\n";
        
        $result = $translationService->translateText(
            $sample,
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            $translation = $result['translated_text'];
            echo "Translation successful!\n";
            echo "Translation length: " . strlen($translation) . " characters\n";
            echo "Execution time: " . $result['execution_time'] . "s\n";
            
            // Check for family term issues
            $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
            $foundProblems = [];
            
            foreach ($problematicTerms as $term) {
                $count = 0;
                $offset = 0;
                while (($pos = stripos($translation, $term, $offset)) !== false) {
                    $beforeChar = $pos > 0 ? $translation[$pos - 1] : ' ';
                    $afterChar = $pos + strlen($term) < strlen($translation) ? $translation[$pos + strlen($term)] : ' ';
                    
                    // Check if it's a standalone word
                    if (!ctype_alnum($beforeChar) && !ctype_alnum($afterChar)) {
                        $count++;
                    }
                    $offset = $pos + 1;
                }
                
                if ($count > 0) {
                    $foundProblems[] = "{$term}: {$count}";
                }
            }
            
            // Check for romanized terms
            $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san'];
            $foundExpected = [];
            
            foreach ($expectedTerms as $term) {
                $count = substr_count($translation, $term);
                if ($count > 0) {
                    $foundExpected[] = "{$term}: {$count}";
                }
            }
            
            // Assessment
            if (empty($foundProblems)) {
                echo "✅ EXCELLENT: No English family terms found\n";
            } else {
                echo "❌ PROBLEM FOUND: English family terms detected:\n";
                foreach ($foundProblems as $problem) {
                    echo "  - {$problem}\n";
                }
            }
            
            if (!empty($foundExpected)) {
                echo "✅ GOOD: Romanized terms found: " . implode(', ', $foundExpected) . "\n";
            } else {
                echo "⚠️ No romanized family terms found (might be normal if no family terms in sample)\n";
            }
            
            // Show first 300 characters of translation
            echo "\nFirst 300 chars of translation:\n";
            echo substr($translation, 0, 300) . "...\n";
            
        } else {
            echo "❌ Translation failed: " . $result['error'] . "\n";
        }
        
        echo "\n" . str_repeat("-", 50) . "\n\n";
    }
    
    // Final test: Try to translate the full chapter
    echo "=== Final Test: Full Chapter Translation ===\n";
    echo "Full chapter length: " . strlen($originalContent) . " characters\n";
    
    // This might be too long, so let's try a substantial portion
    $substantialSample = substr($originalContent, 0, 5000);
    echo "Testing with 5000 character sample...\n";
    
    $result = $translationService->translateText(
        $substantialSample,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        $translation = $result['translated_text'];
        echo "✅ Large sample translation successful!\n";
        echo "Translation length: " . strlen($translation) . " characters\n";
        
        // Final family term check
        $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $finalProblems = [];
        
        foreach ($problematicTerms as $term) {
            $count = substr_count(strtolower($translation), strtolower($term));
            if ($count > 0) {
                $finalProblems[] = "{$term}: {$count}";
            }
        }
        
        if (empty($finalProblems)) {
            echo "🎉 SUCCESS: No family term issues in large sample!\n";
        } else {
            echo "❌ CONFIRMED: Family term issues persist in larger content:\n";
            foreach ($finalProblems as $problem) {
                echo "  - {$problem}\n";
            }
        }
        
    } else {
        echo "❌ Large sample translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
