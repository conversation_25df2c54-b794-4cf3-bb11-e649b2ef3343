<?php
/**
 * Force re-translate Chapter 56 by clearing cache and using fixed system
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

echo "=== Force Re-translating Chapter 56 ===\n\n";

$db = Database::getInstance();

// Get chapter 56
$chapter = $db->fetchOne(
    'SELECT id, novel_id, chapter_number, original_content, translated_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

echo "Chapter 56 found:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Original content length: " . strlen($chapter['original_content']) . " characters\n";
echo "- Current translated content length: " . strlen($chapter['translated_content']) . " characters\n\n";

// Clear any existing chunks to force fresh translation
echo "=== Clearing Existing Chunks ===\n";
$deletedChunks = $db->query(
    'DELETE FROM chapter_chunks WHERE chapter_id = ?',
    [$chapter['id']]
);
echo "Cleared existing chunks\n";

// Clear translation cache if it exists
echo "=== Clearing Translation Cache ===\n";
try {
    $deletedCache = $db->query(
        'DELETE FROM translation_cache WHERE novel_id = ? AND chapter_id = ?',
        [$chapter['novel_id'], $chapter['id']]
    );
    echo "Cleared translation cache\n";
} catch (Exception $e) {
    echo "Translation cache table doesn't exist (normal)\n";
}

// Reset chapter translation status
echo "=== Resetting Chapter Status ===\n";
$db->query(
    'UPDATE chapters SET translation_status = ?, translated_content = ?, updated_at = NOW() WHERE id = ?',
    ['pending', '', $chapter['id']]
);
echo "Reset chapter translation status\n\n";

// Now force re-translate with the fixed system
echo "=== Starting Fresh Translation ===\n";

try {
    $translationService = new TranslationService();
    
    // Force use of specific provider to avoid fallbacks
    echo "Setting up translation with fixed system...\n";
    
    // Re-translate the chapter
    $result = $translationService->translateChapter(
        $chapter['novel_id'],
        $chapter['id']
    );
    
    if ($result['success']) {
        echo "✅ Fresh translation successful!\n";
        echo "Title translation time: " . $result['title_translation']['execution_time'] . "s\n";
        echo "Content translation time: " . $result['content_translation']['execution_time'] . "s\n";
        echo "New translation length: " . $result['content_translation']['character_count'] . " characters\n\n";
        
        // Get the new translation from database
        $updatedChapter = $db->fetchOne(
            'SELECT translated_content FROM chapters WHERE id = ?',
            [$chapter['id']]
        );
        
        $newTranslation = $updatedChapter['translated_content'];
        
        echo "=== Analyzing Fresh Translation ===\n";
        
        // Check for family term issues in new translation
        $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $newProblems = [];
        
        foreach ($problematicTerms as $term) {
            $count = 0;
            $offset = 0;
            while (($pos = stripos($newTranslation, $term, $offset)) !== false) {
                $beforeChar = $pos > 0 ? $newTranslation[$pos - 1] : ' ';
                $afterChar = $pos + strlen($term) < strlen($newTranslation) ? $newTranslation[$pos + strlen($term)] : ' ';
                
                // Check if it's a standalone word
                if (!ctype_alnum($beforeChar) && !ctype_alnum($afterChar)) {
                    $count++;
                }
                $offset = $pos + 1;
            }
            
            if ($count > 0) {
                $newProblems[] = "{$term}: {$count}";
            }
        }
        
        // Check for expected romanized terms
        $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san', 'Onee-san'];
        $foundExpected = [];
        
        foreach ($expectedTerms as $term) {
            $count = substr_count($newTranslation, $term);
            if ($count > 0) {
                $foundExpected[] = "{$term}: {$count}";
            }
        }
        
        // Final assessment
        if (empty($newProblems)) {
            echo "🎉 SUCCESS: No English family terms in fresh translation!\n";
        } else {
            echo "❌ STILL PROBLEMATIC: Fresh translation still has English family terms:\n";
            foreach ($newProblems as $problem) {
                echo "- {$problem}\n";
            }
        }
        
        if (!empty($foundExpected)) {
            echo "✅ EXCELLENT: Found romanized family terms:\n";
            foreach ($foundExpected as $term) {
                echo "- {$term}\n";
            }
        } else {
            echo "⚠️ No romanized family terms found\n";
        }
        
        // Show sample of new translation
        echo "\n=== Sample of Fresh Translation ===\n";
        echo substr($newTranslation, 0, 1000) . "...\n";
        
        // Check if chunked translation was used
        $chunks = $db->fetchAll(
            'SELECT id, chunk_number FROM chapter_chunks WHERE chapter_id = ? ORDER BY chunk_number',
            [$chapter['id']]
        );
        
        if (!empty($chunks)) {
            echo "\n=== Chunked Translation Used ===\n";
            echo "Number of chunks created: " . count($chunks) . "\n";
            echo "This explains why some parts might have different behavior\n";
        } else {
            echo "\n=== Regular Translation Used ===\n";
            echo "No chunks were created - used single translation\n";
        }
        
    } else {
        echo "❌ Fresh translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during fresh translation: " . $e->getMessage() . "\n";
}

echo "\n=== Force Re-translation Complete ===\n";
