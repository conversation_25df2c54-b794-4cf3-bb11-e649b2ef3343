<?php
/**
 * Migration: Add AI Provider Preferences
 * Adds support for user AI provider selection and configuration
 */

require_once __DIR__ . '/../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Starting AI Provider Preferences migration...\n";
    
    // Create ai_provider_preferences table
    $createTableSql = "
        CREATE TABLE IF NOT EXISTS ai_provider_preferences (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT DEFAULT 1,
            provider_type VARCHAR(50) NOT NULL DEFAULT 'deepseek',
            is_active BOOLEAN DEFAULT TRUE,
            configuration JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_provider (user_id, provider_type),
            INDEX idx_active (is_active)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";
    
    $db->query($createTableSql);
    echo "✓ Created ai_provider_preferences table\n";

    // Insert default provider preference
    $defaultPreferenceSql = "
        INSERT IGNORE INTO ai_provider_preferences (user_id, provider_type, is_active, configuration)
        VALUES (1, 'deepseek', TRUE, JSON_OBJECT(
            'api_key', '',
            'model', 'deepseek-chat',
            'temperature', 0.3,
            'max_tokens', 4096,
            'timeout', 300
        ))
    ";

    $db->query($defaultPreferenceSql);
    echo "✓ Inserted default DeepSeek provider preference\n";

    // Create ai_provider_usage_stats table for tracking usage
    $createUsageStatsSql = "
        CREATE TABLE IF NOT EXISTS ai_provider_usage_stats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            provider_type VARCHAR(50) NOT NULL,
            usage_date DATE NOT NULL,
            translation_count INT DEFAULT 0,
            character_count BIGINT DEFAULT 0,
            success_count INT DEFAULT 0,
            error_count INT DEFAULT 0,
            total_execution_time DECIMAL(10,2) DEFAULT 0.00,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            UNIQUE KEY unique_provider_date (provider_type, usage_date),
            INDEX idx_usage_date (usage_date),
            INDEX idx_provider (provider_type)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ";

    $db->query($createUsageStatsSql);
    echo "✓ Created ai_provider_usage_stats table\n";

    // Add provider_used column to chapters table for tracking
    $addProviderColumnSql = "
        ALTER TABLE chapters
        ADD COLUMN provider_used VARCHAR(50) DEFAULT NULL AFTER translation_status
    ";

    try {
        $db->query($addProviderColumnSql);
        echo "✓ Added provider_used column to chapters table\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "✓ provider_used column already exists in chapters table\n";
        } else {
            throw $e;
        }
    }

    // Add provider_used column to chapter_chunks table for tracking
    $addChunkProviderColumnSql = "
        ALTER TABLE chapter_chunks
        ADD COLUMN provider_used VARCHAR(50) DEFAULT NULL AFTER translation_status
    ";

    try {
        $db->query($addChunkProviderColumnSql);
        echo "✓ Added provider_used column to chapter_chunks table\n";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "✓ provider_used column already exists in chapter_chunks table\n";
        } else {
            throw $e;
        }
    }
    
    echo "\n✅ AI Provider Preferences migration completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Configure your AI provider preferences in the settings page\n";
    echo "2. Test different providers to find the best one for your needs\n";
    echo "3. Monitor usage statistics to optimize your provider selection\n";
    
} catch (Exception $e) {
    echo "\n❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
