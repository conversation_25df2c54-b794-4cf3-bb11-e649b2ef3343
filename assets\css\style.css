/* Novel Translation Application Styles */

:root {
    --primary-color: #0d6efd;
    --secondary-color: #6c757d;
    --success-color: #198754;
    --info-color: #0dcaf0;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #212529;
}

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 9999;
    display: flex;
    justify-content: center;
    align-items: center;
}

.loading-content {
    text-align: center;
}

/* Card Enhancements */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    border-radius: 10px 10px 0 0 !important;
    border-bottom: none;
}

/* Novel Info Display */
.novel-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.novel-title {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.novel-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 15px;
}

.novel-meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.novel-synopsis {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
}

/* Chapter List */
.chapter-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background: white;
}

.chapter-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s;
}

.chapter-item:hover {
    background-color: #f8f9fa;
}

.chapter-item:last-child {
    border-bottom: none;
}

.chapter-number {
    font-weight: bold;
    color: var(--primary-color);
    min-width: 60px;
}

.chapter-title {
    flex: 1;
    margin: 0 15px;
    font-size: 0.95rem;
}

.chapter-actions {
    display: flex;
    gap: 8px;
}

.chapter-status {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-pending {
    background-color: #e9ecef;
    color: #6c757d;
}

.status-saved {
    background-color: #d1ecf1;
    color: #0c5460;
}

.status-translating {
    background-color: #fff3cd;
    color: #856404;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-error {
    background-color: #f8d7da;
    color: #721c24;
}

/* Novel List */
.novel-card {
    border-left: 4px solid var(--primary-color);
    margin-bottom: 15px;
}

.novel-card-header {
    background: linear-gradient(90deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
}

.novel-progress {
    margin-top: 10px;
}

.progress-label {
    font-size: 0.85rem;
    color: #6c757d;
    margin-bottom: 5px;
}

/* Name Dictionary */
.name-dictionary {
    max-height: 600px;
    overflow-y: auto;
}

.name-item {
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    background: white;
    transition: background-color 0.2s ease;
}

.name-item:hover {
    background-color: #f8f9fa;
}

.name-item:last-child {
    border-bottom: none;
}

.name-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.name-original {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.name-original strong {
    color: var(--dark-color);
    font-size: 14px;
}

.name-romanization-row,
.name-translation-row {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-label-sm {
    font-size: 11px;
    font-weight: 600;
    color: var(--secondary-color);
    text-transform: uppercase;
    margin-bottom: 2px;
}

.name-type {
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    font-weight: 500;
    min-width: 70px;
    text-align: center;
}

.name-meta {
    margin-top: 4px;
    padding-top: 4px;
    border-top: 1px solid #f0f0f0;
}

.type-character { background-color: #e7f3ff; color: #0066cc; }
.type-location { background-color: #fff2e7; color: #cc6600; }
.type-organization { background-color: #f0e7ff; color: #6600cc; }
.type-skill { background-color: #e7ffe7; color: #00cc00; }
.type-monster { background-color: #ffe7e7; color: #cc0000; }
.type-other { background-color: #f0f0f0; color: #666666; }

/* Buttons */
.btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* Platform Badges */
.platform-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.platform-kakuyomu {
    background-color: #ff6b6b;
    color: white;
}

.platform-syosetu {
    background-color: #4ecdc4;
    color: white;
}

.platform-shuba69 {
    background-color: #45b7d1;
    color: white;
}

.platform-manual {
    background-color: #6c757d;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chapter-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .chapter-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .novel-meta {
        flex-direction: column;
        gap: 8px;
    }

    .name-item {
        padding: 8px;
    }

    .name-content {
        gap: 6px;
    }

    .section-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .section-controls .btn {
        width: 100%;
    }

    .name-dictionary {
        max-height: 400px;
    }

    .content-section {
        margin-bottom: 0.5rem;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .edit-controls {
        border-left: none;
        border-top: 1px solid #e9ecef;
        padding: 8px 12px;
        justify-content: center;
        flex-wrap: wrap;
    }

    .section-toggle {
        border-radius: 8px 8px 0 0;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Scrollbar Styling */
.chapter-list::-webkit-scrollbar,
.name-dictionary::-webkit-scrollbar {
    width: 6px;
}

.chapter-list::-webkit-scrollbar-track,
.name-dictionary::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chapter-list::-webkit-scrollbar-thumb,
.name-dictionary::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chapter-list::-webkit-scrollbar-thumb:hover,
.name-dictionary::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Toast Styling */
.toast {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Form Enhancements */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Page-specific Enhancements */
.novel-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: none;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.novel-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.chapter-preview-list {
    max-height: 300px;
    overflow-y: auto;
}

.chapter-preview-item {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    border-bottom: 1px solid #eee;
    gap: 10px;
}

.chapter-preview-item:last-child {
    border-bottom: none;
}

.chapter-text {
    font-family: 'Georgia', serif;
    line-height: 1.8;
    font-size: 16px;
}

.chapter-text p {
    margin-bottom: 1.2em;
    text-align: justify;
}

/* Furigana (Ruby Text) Styles */
ruby {
    ruby-align: center;
    ruby-position: over;
}

rt {
    font-size: 0.6em;
    line-height: 1;
    text-align: center;
    color: #666;
    font-weight: normal;
}

/* Ensure proper spacing for furigana */
.chapter-text ruby {
    margin: 0 0.1em;
}

/* Alternative furigana display modes */
.furigana-parentheses ruby rt {
    display: none;
}

.furigana-parentheses ruby::after {
    content: "(" attr(data-rt) ")";
    font-size: 0.8em;
    color: #666;
}

.furigana-hidden ruby rt {
    display: none;
}

/* Furigana controls */
.furigana-controls {
    margin-bottom: 1rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #e9ecef;
}

.furigana-controls label {
    font-weight: 500;
    margin-right: 1rem;
    font-size: 0.9rem;
}

.furigana-controls select {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
}

/* Chunk boundary marker styles */
.chunk-boundary-marker {
    margin: 1.5rem 0;
    text-align: center;
}

.chunk-divider {
    border: none;
    border-top: 1px dashed #dee2e6;
    margin: 0.5rem 0;
    opacity: 0.7;
}

.chunk-marker-text {
    color: #6c757d;
    font-size: 0.75rem;
    background: #f8f9fa;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    display: inline-block;
    border: 1px solid #dee2e6;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.chunk-marker-text i {
    margin-right: 0.25rem;
    opacity: 0.8;
}

/* Chapter text formatting improvements */
.chapter-text p {
    margin-bottom: 1.2rem;
    line-height: 1.8;
}

.chapter-text p:last-child {
    margin-bottom: 0;
}

/* Preserve formatting for Japanese text */
.original-text {
    font-family: 'Noto Sans JP', 'Hiragino Sans', 'Yu Gothic', sans-serif;
    line-height: 1.9;
}

.translated-text {
    font-family: 'Georgia', 'Times New Roman', serif;
    line-height: 1.8;
}

/* Debug controls */
.debug-controls {
    margin-bottom: 1rem;
    padding: 0.75rem;
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0.375rem;
}

.debug-controls label {
    font-weight: 500;
    margin-right: 1rem;
    font-size: 0.9rem;
    color: #856404;
}

.debug-controls input[type="checkbox"] {
    margin-right: 0.5rem;
}

/* Bulk Selection Styles */
.bulk-controls {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-top: 12px;
}

.bulk-selection-info {
    display: flex;
    gap: 8px;
    align-items: center;
}

.bulk-selection-controls {
    display: flex;
    gap: 8px;
}

.bulk-actions {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #dee2e6;
    flex-wrap: wrap;
}

.chapter-item.selected {
    background-color: #e3f2fd;
    border-left: 4px solid var(--primary-color);
}

.chapter-checkbox {
    display: flex;
    align-items: center;
    margin-right: 12px;
}

.chapter-checkbox input[type="checkbox"] {
    transform: scale(1.2);
    margin: 0;
}

.chapter-item {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Responsive bulk controls */
@media (max-width: 768px) {
    .bulk-selection-info,
    .bulk-selection-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .bulk-actions {
        flex-direction: column;
        gap: 8px;
    }

    .bulk-actions .btn {
        width: 100%;
    }

    .chapter-item {
        flex-wrap: wrap;
        padding: 8px 12px;
    }

    .chapter-checkbox {
        order: -1;
        margin-right: 8px;
    }
}

/* Collapsible Content Sections */
.content-section {
    margin-bottom: 1rem;
}

.section-header {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0;
}

.section-toggle {
    flex: 1;
    text-align: left;
    padding: 12px 16px;
    border: none;
    background: none;
    color: #495057;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
    border-radius: 8px 8px 0 0;
}

.section-toggle:hover {
    background-color: #e9ecef;
    color: #212529;
    text-decoration: none;
}

.section-toggle:focus {
    box-shadow: none;
    outline: 2px solid var(--primary-color);
    outline-offset: -2px;
}

.toggle-icon {
    transition: transform 0.2s ease;
    font-size: 12px;
    width: 12px;
}

.section-title {
    font-weight: 600;
    font-size: 14px;
}

.edit-controls {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    border-left: 1px solid #e9ecef;
}

.content-section .card {
    border-radius: 0 0 8px 8px;
    border-top: none;
}

.section-controls {
    display: flex;
    gap: 8px;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 16px;
    flex-wrap: wrap;
}

.original-text {
    background-color: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
}

.translated-text {
    padding: 20px;
}

.meta-item {
    margin-bottom: 8px;
}

.meta-item strong {
    color: #495057;
}

/* Progress Bar Enhancements */
.progress-container {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin: 15px 0;
}

.progress-container.show {
    animation: slideDown 0.3s ease-out;
}

.progress-header {
    margin-bottom: 15px;
}

.progress-title {
    font-weight: 600;
    color: var(--dark-color);
    font-size: 1rem;
}

.progress-percentage {
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.progress {
    height: 24px !important;
    border-radius: 12px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.progress-bar {
    border-radius: 12px;
    transition: width 0.4s ease, background-color 0.3s ease;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

.progress-status {
    font-size: 0.9rem;
    color: #6c757d;
    font-style: italic;
    margin-top: 10px;
    min-height: 20px;
}

/* Progress bar color variants */
.progress-bar.bg-success {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.progress-bar.bg-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.progress-bar.bg-danger {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
}

.progress-bar.bg-primary {
    background: linear-gradient(45deg, #007bff, #6610f2);
}

/* Animation for progress bar stripes */
@keyframes progress-bar-stripes {
    0% {
        background-position: 1rem 0;
    }
    100% {
        background-position: 0 0;
    }
}

/* Slide down animation for progress container */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
        max-height: 0;
    }
    to {
        opacity: 1;
        transform: translateY(0);
        max-height: 200px;
    }
}

/* Responsive adjustments for progress bars */
@media (max-width: 768px) {
    .progress-container {
        padding: 15px;
        margin: 10px 0;
    }

    .progress-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .progress-percentage {
        font-size: 1rem;
    }

    .progress {
        height: 20px !important;
    }
}

/* New Chapters Check Modal Styles */
.stat-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
    border-bottom: 1px solid #f0f0f0;
}

.stat-label {
    font-weight: 500;
    color: #6c757d;
}

.stat-value {
    font-weight: 600;
}

.new-chapters-list {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    background: #f8f9fa;
}

.new-chapter-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid #e9ecef;
    background: white;
    transition: background-color 0.2s ease;
}

.new-chapter-item:last-child {
    border-bottom: none;
}

.new-chapter-item:hover {
    background-color: #f8f9fa;
}

.new-chapter-item .chapter-number {
    background: #007bff;
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-right: 0.75rem;
    min-width: 60px;
    text-align: center;
}

.new-chapter-item .chapter-title {
    flex: 1;
    font-size: 0.9rem;
    color: #495057;
    line-height: 1.4;
}

/* Check for new chapters button styling */
.btn-outline-info {
    border-color: #17a2b8;
    color: #17a2b8;
}

.btn-outline-info:hover {
    background-color: #17a2b8;
    border-color: #17a2b8;
    color: white;
}

/* Modal enhancements for new chapters */
.modal-lg .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.modal-header .modal-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive adjustments for new chapters modal */
@media (max-width: 768px) {
    .stat-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }

    .new-chapter-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .new-chapter-item .chapter-number {
        margin-right: 0;
        margin-bottom: 0.25rem;
    }
}

/* Simple Database-style Table for Novels */
.novels-table {
    background: white;
    border: 1px solid #dee2e6;
    margin-bottom: 0;
}

.novels-table thead th {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: normal;
    color: #495057;
    padding: 8px 12px;
    font-size: 0.875rem;
}

.novels-table tbody tr {
    border-bottom: 1px solid #dee2e6;
}

.novels-table tbody tr:nth-child(even) {
    background-color: #f9f9f9;
}

.novels-table td {
    padding: 8px 12px;
    vertical-align: top;
    border-top: none;
    font-size: 0.875rem;
}

.novel-title-cell {
    max-width: 300px;
}

.novel-title {
    font-size: 0.875rem;
    font-weight: normal;
    color: #212529;
    line-height: 1.2;
    margin-bottom: 2px;
}

.novel-synopsis-preview {
    line-height: 1.3;
    color: #6c757d;
    font-size: 0.8rem;
}

.progress-cell {
    min-width: 120px;
}

.progress-info {
    margin-bottom: 2px;
}

.progress-info small {
    font-size: 0.75rem;
}

/* Simple platform badge */
.novels-table .platform-badge {
    font-size: 0.7rem;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: normal;
    display: inline-block;
}

/* Simple button styling */
.novels-table .btn-sm {
    padding: 0.2rem 0.5rem;
    font-size: 0.75rem;
}

.novels-table .btn-group {
    white-space: nowrap;
}

/* Basic responsive adjustments */
@media (max-width: 768px) {
    .novels-table td {
        padding: 6px 8px;
    }

    .progress-cell {
        min-width: 100px;
    }
}

@media (max-width: 576px) {
    .novels-table thead th:nth-child(3),
    .novels-table tbody td:nth-child(3),
    .novels-table thead th:nth-child(5),
    .novels-table tbody td:nth-child(5) {
        display: none;
    }

    .novel-title-cell {
        max-width: 200px;
    }
}

/* Chunk Interface Styles */
.chunk-item {
    transition: all 0.3s ease;
    border: 1px solid #dee2e6 !important;
}

.chunk-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-color: #007bff !important;
}

.chunk-header h6 {
    margin-bottom: 0.25rem;
    font-weight: 600;
}

.chunk-actions .btn {
    margin-left: 0.25rem;
}

.content-preview {
    font-size: 0.875rem;
    line-height: 1.4;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.stat-item {
    text-align: center;
    padding: 0.5rem;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    line-height: 1;
}

.stat-label {
    display: block;
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

.badge-success {
    background-color: #198754;
}

.badge-warning {
    background-color: #ffc107;
    color: #000;
}

.badge-danger {
    background-color: #dc3545;
}

.badge-secondary {
    background-color: #6c757d;
}

.bg-success-subtle {
    background-color: #d1e7dd !important;
}

.chunk-list {
    padding-right: 0.5rem;
}

.chunk-list::-webkit-scrollbar {
    width: 6px;
}

.chunk-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chunk-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chunk-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Chapter item enhancements for chunk support */
.chapter-item.has-chunks::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #007bff, #0056b3);
    border-radius: 0 2px 2px 0;
}

/* Modal enhancements for chunk interface */
.modal-xl .modal-dialog {
    max-width: 1200px;
}

.chunk-modal .modal-header {
    border-bottom: 2px solid #dee2e6;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.chunk-modal .modal-title {
    font-weight: 600;
    color: #495057;
}

/* Button group enhancements */
.btn-group .btn {
    border-radius: 0.375rem;
    margin-right: 0.25rem;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Chunk progress container */
#chunk-progress-container {
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    animation: fadeIn 0.3s ease-in;
}

#chunk-progress-container .alert {
    margin-bottom: 0;
}

#chunk-progress-bar {
    transition: width 0.3s ease;
}

#chunk-progress-status {
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Name Dictionary Management Styles */
.name-dictionary-page .name-type {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-left: 0.5rem;
}

.name-dictionary-page .name-type.type-character {
    background-color: #e3f2fd;
    color: #1976d2;
}

.name-dictionary-page .name-type.type-location {
    background-color: #f3e5f5;
    color: #7b1fa2;
}

.name-dictionary-page .name-type.type-organization {
    background-color: #fff3e0;
    color: #f57c00;
}

.name-dictionary-page .name-type.type-country {
    background-color: #e8f5e8;
    color: #388e3c;
}

.name-dictionary-page .name-type.type-skill {
    background-color: #fce4ec;
    color: #c2185b;
}

.name-dictionary-page .name-type.type-monster {
    background-color: #ffebee;
    color: #d32f2f;
}

.name-dictionary-page .name-type.type-other {
    background-color: #f5f5f5;
    color: #616161;
}

/* Enhanced name item styles for management page */
.name-dictionary-page .name-item {
    border: 1px solid #e0e0e0;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #fff;
}

.name-dictionary-page .name-item:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.name-dictionary-page .name-type-row,
.name-dictionary-page .name-romanization-row,
.name-dictionary-page .name-translation-row {
    margin-top: 0.75rem;
}

.name-dictionary-page .form-label-sm {
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 0.25rem;
    color: #495057;
}

.name-dictionary-page .name-meta {
    margin-top: 0.5rem;
    padding-top: 0.5rem;
    border-top: 1px solid #e9ecef;
}

/* Name Dictionary Table Styles */
#names-table .form-select-sm {
    font-size: 0.875rem;
}

#names-table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

#names-table tbody tr:hover {
    background-color: #f8f9fa;
}

#names-table tbody tr.table-active {
    background-color: #e3f2fd;
}

/* Bulk Actions Styles */
#bulk-actions {
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    padding: 1rem;
}

#bulk-actions .input-group {
    max-width: 300px;
}

/* Loading Overlay for name dictionary */
#loading-overlay {
    backdrop-filter: blur(2px);
}

/* Responsive adjustments for name dictionary */
@media (max-width: 768px) {
    .name-dictionary-page .name-item {
        padding: 0.75rem;
    }

    #bulk-actions .d-flex {
        flex-direction: column;
        gap: 0.5rem;
    }

    #bulk-actions .input-group {
        max-width: none;
    }
}

/* Chapter View Header Layout */
.chapter-header-title {
    margin-bottom: 1rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e9ecef;
}

.chapter-header-title h4 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #212529;
    line-height: 1.3;
}

.chapter-header-title small {
    color: #6c757d;
    font-size: 0.875rem;
}

.chapter-header-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    align-items: flex-start;
}

/* Ensure button groups maintain proper spacing */
.chapter-header-actions .btn-group {
    margin-right: 0;
}

.chapter-header-actions .btn-group + .btn,
.chapter-header-actions .btn + .btn-group {
    margin-left: 0;
}

/* Responsive adjustments for chapter header */
@media (max-width: 768px) {
    .chapter-header-title {
        margin-bottom: 0.75rem;
        padding-bottom: 0.5rem;
    }

    .chapter-header-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 0.5rem;
    }

    .chapter-header-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .chapter-header-actions .btn-group {
        width: 100%;
        display: flex;
    }

    .chapter-header-actions .btn-group .btn {
        flex: 1;
    }
}

@media (max-width: 576px) {
    .chapter-header-title h4 {
        font-size: 1.1rem;
        line-height: 1.4;
    }

    .chapter-header-actions {
        gap: 0.375rem;
    }
}

/* Back to Top Floating Action Button */
.back-to-top-fab {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 3.5rem;
    height: 3.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: none;
    transition: all 0.3s ease;
    opacity: 0;
    transform: translateY(20px);
    background-color: var(--primary-color);
    color: white;
}

.back-to-top-fab:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
    background-color: var(--primary-color);
    color: white;
}

.back-to-top-fab:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    background-color: var(--primary-color);
    color: white;
    outline: none;
}

.back-to-top-fab:active {
    transform: translateY(0) scale(0.95);
}

.back-to-top-fab.show {
    opacity: 1;
    transform: translateY(0);
    animation: backToTopPulse 2s ease-in-out;
}

/* Subtle pulse animation when button first appears */
@keyframes backToTopPulse {
    0% { transform: translateY(0) scale(1); }
    50% { transform: translateY(0) scale(1.05); }
    100% { transform: translateY(0) scale(1); }
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
    .back-to-top-fab {
        transition: opacity 0.3s ease;
        animation: none;
    }

    .back-to-top-fab.show {
        animation: none;
    }
}

/* Responsive adjustments for back to top button */
@media (max-width: 768px) {
    .back-to-top-fab {
        bottom: 1.5rem;
        right: 1.5rem;
        width: 3rem;
        height: 3rem;
        font-size: 1.1rem;
    }
}

@media (max-width: 576px) {
    .back-to-top-fab {
        bottom: 1rem;
        right: 1rem;
        width: 2.75rem;
        height: 2.75rem;
        font-size: 1rem;
    }
}

/* Chapter Splitting Styles */
.split-chapter-modal .modal-body {
    padding: 1.5rem;
}

.split-chapter-modal .form-text {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.split-chapter-modal .alert {
    border-radius: 0.375rem;
    padding: 0.75rem 1rem;
}

.split-chapter-modal .alert-warning {
    background-color: #fff3cd;
    border-color: #ffecb5;
    color: #664d03;
}

.split-chapter-modal .text-info {
    color: #0dcaf0 !important;
}

.split-chapter-modal .text-muted {
    color: #6c757d !important;
}

/* Chapter with chunks indicator */
.chapter-item.has-chunks {
    border-left: 4px solid #17a2b8;
    background-color: #f8f9fa;
}

.chapter-item.has-chunks .chapter-title::after {
    content: " (Split)";
    font-size: 0.75rem;
    color: #17a2b8;
    font-weight: normal;
}

/* Split button styling */
.btn-outline-warning.split-btn {
    border-color: #ffc107;
    color: #ffc107;
}

.btn-outline-warning.split-btn:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #000;
}

/* Split chapter modal responsive adjustments */
@media (max-width: 768px) {
    .split-chapter-modal .modal-dialog {
        margin: 1rem;
    }

    .split-chapter-modal .modal-body {
        padding: 1rem;
    }
}

/* POV Selection Modal Styles */
.pov-options {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.pov-option {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: #fff;
}

.pov-option:hover {
    border-color: #007bff;
    background-color: #f8f9fa;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.pov-option.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
    box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
}

.pov-option .pov-radio {
    color: #6c757d;
    font-size: 1.1em;
    margin-top: 2px;
}

.pov-option.selected .pov-radio {
    color: #007bff;
}

.pov-option .pov-title {
    color: #212529;
    font-weight: 600;
    margin-bottom: 4px;
}

.pov-option.selected .pov-title {
    color: #007bff;
}

.pov-option .pov-description {
    font-size: 0.9em;
    line-height: 1.4;
    color: #6c757d;
}

.pov-option.selected .pov-description {
    color: #495057;
}

/* POV Management Interface Styles */
.pov-management {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
}

.pov-status {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85em;
    font-weight: 500;
}

.pov-status.preserve-original {
    background-color: #e9ecef;
    color: #495057;
}

.pov-status.first-person {
    background-color: #d4edda;
    color: #155724;
}

.pov-status.second-person {
    background-color: #fff3cd;
    color: #856404;
}

.pov-status.third-person-limited {
    background-color: #d1ecf1;
    color: #0c5460;
}

.pov-status.third-person-omniscient {
    background-color: #e2e3f1;
    color: #383d41;
}

.pov-status-small {
    font-size: 0.75em;
    padding: 2px 6px;
}

/* POV Management Interface Additional Styles */
.pov-statistics {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
}

.chapter-pov-overrides .border {
    background-color: #f8f9fa;
}

.chapter-pov-overrides .border:hover {
    background-color: #e9ecef;
    transition: background-color 0.2s ease;
}
