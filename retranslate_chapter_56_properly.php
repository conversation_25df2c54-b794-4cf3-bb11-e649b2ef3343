<?php
/**
 * Re-translate Chapter 56 with the fixed translation system
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

echo "=== Re-translating Chapter 56 with Fixed System ===\n\n";

$db = Database::getInstance();

// Get chapter 56
$chapter = $db->fetchOne(
    'SELECT id, novel_id, chapter_number, original_content, translated_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

echo "Chapter 56 found:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Novel ID: {$chapter['novel_id']}\n";
echo "- Original content length: " . strlen($chapter['original_content']) . " characters\n";
echo "- Current translated content length: " . strlen($chapter['translated_content']) . " characters\n\n";

// Check current translation for family term issues
echo "=== Current Translation Issues ===\n";
$currentTranslation = $chapter['translated_content'];
$problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
$currentProblems = [];

foreach ($problematicTerms as $term) {
    $count = substr_count(strtolower($currentTranslation), strtolower($term));
    if ($count > 0) {
        $currentProblems[] = "{$term}: {$count}";
    }
}

if (!empty($currentProblems)) {
    echo "Current translation has English family terms:\n";
    foreach ($currentProblems as $problem) {
        echo "- {$problem}\n";
    }
} else {
    echo "Current translation is already correct\n";
}

echo "\n=== Starting Re-translation ===\n";

try {
    // Use the TranslationService which should use the fixed Gemini system
    $translationService = new TranslationService();
    
    echo "Re-translating chapter content...\n";
    
    // Re-translate the chapter
    $result = $translationService->translateChapter(
        $chapter['novel_id'],
        $chapter['id']
    );
    
    if ($result['success']) {
        echo "✅ Re-translation successful!\n";
        echo "Execution time: " . $result['content_translation']['execution_time'] . "s\n";
        echo "New translation length: " . $result['content_translation']['character_count'] . " characters\n\n";
        
        // Get the new translation from database
        $updatedChapter = $db->fetchOne(
            'SELECT translated_content FROM chapters WHERE id = ?',
            [$chapter['id']]
        );
        
        $newTranslation = $updatedChapter['translated_content'];
        
        echo "=== Checking New Translation ===\n";
        
        // Check for family term issues in new translation
        $newProblems = [];
        foreach ($problematicTerms as $term) {
            $count = substr_count(strtolower($newTranslation), strtolower($term));
            if ($count > 0) {
                $newProblems[] = "{$term}: {$count}";
            }
        }
        
        if (empty($newProblems)) {
            echo "🎉 SUCCESS: No English family terms in new translation!\n";
        } else {
            echo "❌ STILL PROBLEMATIC: New translation still has English family terms:\n";
            foreach ($newProblems as $problem) {
                echo "- {$problem}\n";
            }
        }
        
        // Check for expected romanized terms
        $expectedTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san', 'Onee-san'];
        $foundExpected = [];
        
        foreach ($expectedTerms as $term) {
            $count = substr_count($newTranslation, $term);
            if ($count > 0) {
                $foundExpected[] = "{$term}: {$count}";
            }
        }
        
        if (!empty($foundExpected)) {
            echo "✅ EXCELLENT: Found romanized family terms:\n";
            foreach ($foundExpected as $term) {
                echo "- {$term}\n";
            }
        } else {
            echo "⚠️ No romanized family terms found (might indicate an issue)\n";
        }
        
        // Show sample of new translation
        echo "\n=== Sample of New Translation ===\n";
        echo substr($newTranslation, 0, 800) . "...\n";
        
        // Compare with old translation
        echo "\n=== Comparison ===\n";
        echo "Old translation issues: " . count($currentProblems) . " types of English family terms\n";
        echo "New translation issues: " . count($newProblems) . " types of English family terms\n";
        
        if (count($newProblems) < count($currentProblems)) {
            echo "✅ IMPROVEMENT: Fewer family term issues in new translation\n";
        } elseif (count($newProblems) == 0 && count($currentProblems) > 0) {
            echo "🎉 PERFECT: All family term issues resolved!\n";
        } else {
            echo "⚠️ No improvement or issues persist\n";
        }
        
    } else {
        echo "❌ Re-translation failed: " . $result['error'] . "\n";
        
        // If full re-translation fails, try chunked approach
        echo "\nTrying chunked translation approach...\n";
        
        // Split content into smaller chunks
        $originalContent = $chapter['original_content'];
        $chunkSize = 2000;
        $chunks = [];
        
        for ($i = 0; $i < strlen($originalContent); $i += $chunkSize) {
            $chunks[] = substr($originalContent, $i, $chunkSize);
        }
        
        echo "Split into " . count($chunks) . " chunks\n";
        
        // This would require implementing chunked translation
        // For now, just report the issue
        echo "⚠️ Full chapter re-translation failed, chunked approach would be needed\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during re-translation: " . $e->getMessage() . "\n";
}

echo "\n=== Re-translation Complete ===\n";
