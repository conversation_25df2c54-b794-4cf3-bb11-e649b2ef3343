<?php
/**
 * Test flexible POV system with different user preferences
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/DeepSeekTranslationService.php';

echo "=== Testing Flexible POV System ===\n\n";

// Test text with first-person pronouns
$testText = "私は考えている。我々はこれをテストする必要がある。私の意見では、これは重要だ。";

echo "Original Japanese text:\n";
echo $testText . "\n\n";

// Test different POV preferences
$povTests = [
    'first_person' => 'First Person',
    'third_person_limited' => 'Third Person Limited', 
    'third_person_omniscient' => 'Third Person Omniscient',
    'preserve_original' => 'Preserve Original (should default to third person)',
    null => 'No POV preference (should default to third person)'
];

foreach ($povTests as $povPreference => $description) {
    echo "=== Testing: {$description} ===\n";
    
    // Create narrative context
    $narrativeContext = [
        'narrative_voice' => 'first_person',
        'narrative_confidence' => 0.8,
        'perspective_indicators' => [
            'first_person' => ['私は', '我々は', '私の'],
            'third_person' => []
        ]
    ];

    $fullContext = [
        'type' => 'chunk',
        'narrative_context' => $narrativeContext
    ];
    
    // Add POV preference if specified
    if ($povPreference !== null) {
        $fullContext['user_pov_preference'] = $povPreference;
    }

    try {
        $deepSeekService = new DeepSeekTranslationService();
        
        // Test the buildNarrativePerspectiveInstructions method
        $reflection = new ReflectionClass($deepSeekService);
        $method = $reflection->getMethod('buildNarrativePerspectiveInstructions');
        $method->setAccessible(true);
        
        $instructions = $method->invoke($deepSeekService, $narrativeContext, $fullContext);
        
        echo "Generated instructions preview:\n";
        $lines = explode("\n", $instructions);
        foreach (array_slice($lines, 0, 8) as $line) {
            echo "  " . $line . "\n";
        }
        echo "  ...\n\n";
        
        // Test actual translation
        $result = $deepSeekService->translateText($testText, 'en', 'auto', $fullContext);
        
        if ($result['success']) {
            echo "Translation result:\n";
            echo "  " . $result['translated_text'] . "\n\n";
            
            // Analyze POV
            $translatedText = $result['translated_text'];
            $firstPersonCount = substr_count($translatedText, 'I ') + substr_count($translatedText, ' I ') + substr_count($translatedText, 'my ') + substr_count($translatedText, 'we ');
            $thirdPersonCount = substr_count($translatedText, 'he ') + substr_count($translatedText, 'she ') + substr_count($translatedText, 'they ') + substr_count($translatedText, 'his ') + substr_count($translatedText, 'her ') + substr_count($translatedText, 'their ');
            
            echo "POV Analysis:\n";
            echo "  First-person pronouns: {$firstPersonCount}\n";
            echo "  Third-person pronouns: {$thirdPersonCount}\n";
            
            // Determine if result matches expectation
            $expectedFirstPerson = ($povPreference === 'first_person');
            $expectedThirdPerson = in_array($povPreference, ['third_person_limited', 'third_person_omniscient', 'preserve_original']) || $povPreference === null;
            
            if ($expectedFirstPerson && $firstPersonCount > 0 && $thirdPersonCount == 0) {
                echo "  ✅ CORRECT: First person POV applied successfully\n";
            } elseif ($expectedThirdPerson && $thirdPersonCount > 0 && $firstPersonCount == 0) {
                echo "  ✅ CORRECT: Third person POV applied successfully\n";
            } elseif ($firstPersonCount == 0 && $thirdPersonCount == 0) {
                echo "  ⚠️ NEUTRAL: No personal pronouns found\n";
            } else {
                echo "  ❌ ISSUE: POV doesn't match expectation\n";
                echo "    Expected: " . ($expectedFirstPerson ? "First person" : "Third person") . "\n";
                echo "    Got: Mixed or unexpected result\n";
            }
        } else {
            echo "❌ Translation failed: " . $result['error'] . "\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

echo "=== Summary ===\n";
echo "The flexible POV system now supports:\n";
echo "✅ First Person - for novels that should use 'I', 'me', 'my'\n";
echo "✅ Third Person Limited - focus on one character's perspective\n";
echo "✅ Third Person Omniscient - access to all characters' thoughts\n";
echo "✅ Preserve Original - defaults to third person for consistency\n";
echo "✅ No preference - defaults to third person omniscient\n\n";

echo "To use different POV for different novels:\n";
echo "1. Set POV preference in the POV modal for each novel\n";
echo "2. The system will apply the selected perspective consistently\n";
echo "3. First person novels will use 'I', third person novels will use 'he/she/they'\n";
echo "4. Dialogue is always preserved regardless of narrative POV\n";

echo "\nDone.\n";
