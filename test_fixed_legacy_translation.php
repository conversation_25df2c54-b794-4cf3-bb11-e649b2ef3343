<?php
/**
 * Test the fixed legacy translation system
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

echo "=== Testing Fixed Legacy Translation System ===\n\n";

try {
    $translationService = new TranslationService();
    $db = Database::getInstance();
    
    // Get family terms from name dictionary
    $familyNames = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['兄さん', '兄', '父', '母', 'お父さん', 'お母さん', 'お兄ちゃん', 'お兄さん']
    );
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $familyNames
    ];
    
    echo "Using " . count($familyNames) . " family terms from name dictionary\n\n";
    
    // Test sentences that previously caused problems
    $testSentences = [
        "兄さんは忙しいです。",
        "お父さんが帰ってきました。",
        "お母さんは料理をしています。",
        "私の兄は学生です。",
        "父と母が話しています。"
    ];
    
    foreach ($testSentences as $i => $sentence) {
        echo "Test " . ($i + 1) . ":\n";
        echo "Original: {$sentence}\n";
        
        // Force use of legacy translation method by using TranslationService directly
        $result = $translationService->translateText(
            $sentence,
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            $translation = $result['translated_text'];
            echo "Translation: {$translation}\n";
            echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
            
            // Check for forbidden English family terms
            $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad', 'papa', 'mama'];
            $foundForbidden = [];
            
            foreach ($forbiddenTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundForbidden[] = $term;
                }
            }
            
            // Check for required romanized terms
            $requiredTerms = ['Tou-san', 'Kaa-san', 'Nii-san', 'Otou-san', 'Okaa-san', 'Onii-san', 'Onii-chan'];
            $foundRequired = [];
            
            foreach ($requiredTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundRequired[] = $term;
                }
            }
            
            // Assessment
            if (empty($foundForbidden) && !empty($foundRequired)) {
                echo "🎉 PERFECT: No forbidden terms, found romanized: " . implode(', ', $foundRequired) . "\n";
            } elseif (empty($foundForbidden)) {
                echo "✅ GOOD: No forbidden terms found\n";
            } else {
                echo "❌ FAILED: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
            }
            
        } else {
            echo "❌ Translation failed: " . $result['error'] . "\n";
        }
        
        echo "\n";
    }
    
    // Test with a longer sample from Chapter 56
    echo "=== Testing with Chapter 56 Sample ===\n";
    
    $chapter = $db->fetchOne(
        'SELECT original_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
        []
    );
    
    // Get a paragraph with family terms
    $sampleText = "「もう、兄さんは……ちょっと元侯爵様に挨拶に行くだけだからって言っていたのに、まさかそのまま任務に行ってしまうなんて」と、ティルティの不満点は、ソルドに置いていかれたというその一点だ。";
    
    echo "Sample: {$sampleText}\n";
    
    $result = $translationService->translateText(
        $sampleText,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        $translation = $result['translated_text'];
        echo "Translation: {$translation}\n";
        echo "API used: " . ($result['api_used'] ?? 'unknown') . "\n";
        
        // Check for issues
        $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $foundForbidden = [];
        
        foreach ($forbiddenTerms as $term) {
            if (stripos($translation, $term) !== false) {
                $foundForbidden[] = $term;
            }
        }
        
        if (empty($foundForbidden)) {
            echo "✅ EXCELLENT: No forbidden family terms in longer sample\n";
        } else {
            echo "❌ PROBLEM PERSISTS: Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
        }
        
    } else {
        echo "❌ Longer sample failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
