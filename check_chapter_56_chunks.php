<?php
/**
 * Check if Chapter 56 uses chunked translation and examine the chunks
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/ChapterChunker.php';

echo "=== Checking Chapter 56 Chunking Status ===\n\n";

$db = Database::getInstance();

// Check if Chapter 56 has chunks
$chapter = $db->fetchOne(
    'SELECT id, chapter_number, original_content, translated_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

echo "Chapter 56 found:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Original content length: " . strlen($chapter['original_content']) . " characters\n";
echo "- Translated content length: " . strlen($chapter['translated_content']) . " characters\n\n";

// Check for existing chunks
$chunks = $db->fetchAll(
    'SELECT id, chunk_number, original_content, translated_content, 
            character_count, word_count, translation_status, translation_date
     FROM chapter_chunks 
     WHERE chapter_id = ? 
     ORDER BY chunk_number',
    [$chapter['id']]
);

echo "=== Chunk Analysis ===\n";
if (empty($chunks)) {
    echo "✅ Chapter 56 has NO chunks - uses regular translation\n";
    
    // Check if it should be chunked
    $chapterChunker = new ChapterChunker();
    $needsChunking = $chapterChunker->needsChunking($chapter['original_content']);
    
    echo "Should be chunked: " . ($needsChunking ? "Yes" : "No") . "\n";
    
    if ($needsChunking) {
        echo "⚠️ Chapter is large enough to need chunking but doesn't have chunks\n";
        echo "This means it uses regular translation which might have different behavior\n";
    }
    
} else {
    echo "❌ Chapter 56 HAS " . count($chunks) . " chunks - uses chunked translation\n\n";
    
    echo "Chunk details:\n";
    foreach ($chunks as $i => $chunk) {
        echo "Chunk " . ($i + 1) . ":\n";
        echo "- Chunk ID: {$chunk['id']}\n";
        echo "- Chunk Number: {$chunk['chunk_number']}\n";
        echo "- Original length: {$chunk['character_count']} characters\n";
        echo "- Translated length: " . strlen($chunk['translated_content']) . " characters\n";
        echo "- Status: {$chunk['translation_status']}\n";
        echo "- Translation date: {$chunk['translation_date']}\n";
        
        // Check for family term issues in this chunk
        if (!empty($chunk['translated_content'])) {
            $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
            $chunkProblems = [];
            
            foreach ($problematicTerms as $term) {
                $count = substr_count(strtolower($chunk['translated_content']), strtolower($term));
                if ($count > 0) {
                    $chunkProblems[] = "{$term}: {$count}";
                }
            }
            
            if (!empty($chunkProblems)) {
                echo "- ❌ Family term issues: " . implode(', ', $chunkProblems) . "\n";
            } else {
                echo "- ✅ No family term issues\n";
            }
            
            // Show sample
            echo "- Sample: " . substr($chunk['translated_content'], 0, 100) . "...\n";
        }
        
        echo "\n";
    }
    
    // Check if chunked translation uses different prompt system
    echo "=== Chunked Translation Analysis ===\n";
    echo "Chunked translation may use different translation paths that bypass the fixed instructions.\n";
    echo "Need to check if chunked translation uses the same buildTranslationPrompt method.\n";
}

// Check the ChapterChunker to see if it needs chunks
echo "\n=== ChapterChunker Analysis ===\n";
$chapterChunker = new ChapterChunker();
$hasChunks = $chapterChunker->hasChunks($chapter['id']);
echo "ChapterChunker says has chunks: " . ($hasChunks ? "Yes" : "No") . "\n";

$needsChunking = $chapterChunker->needsChunking($chapter['original_content']);
echo "ChapterChunker says needs chunking: " . ($needsChunking ? "Yes" : "No") . "\n";

// If it has chunks, that explains why the family term fixes aren't working
if ($hasChunks) {
    echo "\n🔍 DIAGNOSIS:\n";
    echo "Chapter 56 uses CHUNKED TRANSLATION which may have different translation logic.\n";
    echo "The family term fixes were applied to the main translation prompt,\n";
    echo "but chunked translation might use a different code path.\n";
    echo "Need to check if chunked translation uses the same fixed instructions.\n";
} else {
    echo "\n🔍 DIAGNOSIS:\n";
    echo "Chapter 56 uses REGULAR TRANSLATION.\n";
    echo "The family term issues might be from:\n";
    echo "1. Old cached translation that wasn't re-translated\n";
    echo "2. Different translation service being used\n";
    echo "3. Instructions not being applied correctly\n";
}

echo "\n=== Analysis Complete ===\n";
