<?php
/**
 * API Endpoint: WordPress Categories Management
 * GET /api/wordpress-categories.php?profile_id=<id> - Get categories for a WordPress profile
 */

// Disable error display
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Clear output buffers
while (ob_get_level()) {
    ob_end_clean();
}

// Set headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // Include config
    require_once '../config/config.php';

    // Get profile ID
    $profileId = isset($_GET['profile_id']) ? (int)$_GET['profile_id'] : 0;

    if ($profileId <= 0) {
        echo json_encode([
            'success' => false,
            'error' => 'Valid profile_id parameter is required'
        ]);
        exit;
    }

    // Get database
    $db = Database::getInstance();

    // Get profile
    $profile = $db->fetchOne(
        "SELECT * FROM wordpress_profiles WHERE id = ? AND is_active = 1",
        [$profileId]
    );

    if (!$profile) {
        echo json_encode([
            'success' => false,
            'error' => 'WordPress profile not found or inactive'
        ]);
        exit;
    }

    // Create WordPress service
    $wordpressService = new WordPressService($profileId);

    // Fetch categories
    $result = $wordpressService->fetchAvailableCategories();

    if ($result['success']) {
        echo json_encode([
            'success' => true,
            'categories' => $result['categories'],
            'profile_id' => $profileId,
            'profile_name' => $profile['profile_name']
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'error' => $result['error']
        ]);
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Exception: ' . $e->getMessage()
    ]);
} catch (Error $e) {
    echo json_encode([
        'success' => false,
        'error' => 'Fatal Error: ' . $e->getMessage()
    ]);
}


