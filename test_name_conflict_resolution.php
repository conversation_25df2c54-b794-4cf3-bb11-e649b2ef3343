<?php
/**
 * Test Name Conflict Resolution
 * Specific tests for conflict detection and resolution in name mapping
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/NameSubstitutionService.php';

echo "=== Name Conflict Resolution Test Suite ===\n\n";

try {
    $nameService = new NameSubstitutionService();
    
    // Test 1: Multiple Character Names with Similar Variations
    echo "Test 1: Multiple Character Names with Similar Variations\n";
    echo "Testing conflict resolution when multiple names could match the same text\n";
    
    $conflictNames = [
        [
            'original_name' => 'アレックス',
            'translation' => 'Alex',
            'name_type' => 'character',
            'frequency' => 20,
            'is_verified' => true
        ],
        [
            'original_name' => 'アレクサンダー',
            'translation' => 'Alexander',
            'name_type' => 'character',
            'frequency' => 15,
            'is_verified' => true
        ],
        [
            'original_name' => 'アレクサ',
            'translation' => 'Alexa',
            'name_type' => 'character',
            'frequency' => 8,
            'is_verified' => false
        ]
    ];
    
    $testText = "<PERSON> spoke to <PERSON> about <PERSON>'s plan. <PERSON> was also there.";
    $originalText = "アレクサンダーはアレクサについてアレックスの計画について話した。アレクサンダーもそこにいた。";
    
    echo "Original: {$testText}\n";
    
    $result = $nameService->applyNameSubstitutions($testText, $conflictNames, $originalText);
    
    echo "Result: {$result['processed_text']}\n";
    echo "Substitutions made: " . count($result['substitutions']) . "\n";
    
    foreach ($result['substitutions'] as $sub) {
        echo "  - {$sub['original_name']} → {$sub['target_name']} (variations: " . implode(', ', $sub['found_variations']) . ")\n";
    }
    
    // Verify that conflicts were resolved appropriately
    $alexPreserved = strpos($result['processed_text'], 'Alex') !== false;
    $alexanderPreserved = strpos($result['processed_text'], 'Alexander') !== false;
    $noIncorrectAlexa = substr_count($result['processed_text'], 'Alexa') <= substr_count($testText, 'Alexandra');
    
    echo $alexPreserved ? "✅ Alex preserved correctly\n" : "❌ Alex not preserved\n";
    echo $alexanderPreserved ? "✅ Alexander preserved correctly\n" : "❌ Alexander not preserved\n";
    echo $noIncorrectAlexa ? "✅ No incorrect Alexa substitutions\n" : "❌ Incorrect Alexa substitutions detected\n";
    echo "\n";
    
    // Test 2: Nested Name Conflicts (Names within Names)
    echo "Test 2: Nested Name Conflicts (Names within Names)\n";
    echo "Testing handling of names that are contained within other names\n";
    
    $nestedNames = [
        [
            'original_name' => '山田',
            'translation' => 'Yamada',
            'name_type' => 'character',
            'frequency' => 30
        ],
        [
            'original_name' => '山田太郎',
            'translation' => 'Yamada Taro',
            'name_type' => 'character',
            'frequency' => 25
        ],
        [
            'original_name' => '山田花子',
            'translation' => 'Yamada Hanako',
            'name_type' => 'character',
            'frequency' => 20
        ]
    ];
    
    $testText2 = "Yamada Taro met Yamada Hanako. Yamada was happy to see them both.";
    $originalText2 = "山田太郎は山田花子に会った。山田は二人に会えて嬉しかった。";
    
    echo "Original: {$testText2}\n";
    
    $result2 = $nameService->applyNameSubstitutions($testText2, $nestedNames, $originalText2);
    
    echo "Result: {$result2['processed_text']}\n";
    echo "Substitutions made: " . count($result2['substitutions']) . "\n";
    
    // Verify that full names are preserved and not broken down
    $fullNamesPreserved = strpos($result2['processed_text'], 'Yamada Taro') !== false && 
                         strpos($result2['processed_text'], 'Yamada Hanako') !== false;
    $standaloneYamadaHandled = preg_match('/\bYamada\b(?!\s+(Taro|Hanako))/', $result2['processed_text']);
    
    echo $fullNamesPreserved ? "✅ Full names preserved correctly\n" : "❌ Full names not preserved\n";
    echo $standaloneYamadaHandled ? "✅ Standalone Yamada handled correctly\n" : "❌ Standalone Yamada not handled correctly\n";
    echo "\n";
    
    // Test 3: Priority-based Conflict Resolution
    echo "Test 3: Priority-based Conflict Resolution\n";
    echo "Testing that higher priority names win conflicts\n";
    
    $priorityNames = [
        [
            'original_name' => 'ケン',
            'translation' => 'Ken',
            'name_type' => 'character',
            'frequency' => 50,
            'is_verified' => true
        ],
        [
            'original_name' => 'ケント',
            'translation' => 'Kent',
            'name_type' => 'location',
            'frequency' => 5,
            'is_verified' => false
        ]
    ];
    
    $testText3 = "Kenneth spoke about Kent's situation.";
    $originalText3 = "ケンはケントの状況について話した。";
    
    echo "Original: {$testText3}\n";
    
    $result3 = $nameService->applyNameSubstitutions($testText3, $priorityNames, $originalText3);
    
    echo "Result: {$result3['processed_text']}\n";
    echo "Substitutions made: " . count($result3['substitutions']) . "\n";
    
    // Verify that character name (higher priority) takes precedence
    $characterNamePrioritized = true;
    foreach ($result3['substitutions'] as $sub) {
        if ($sub['original_name'] === 'ケン' && $sub['target_name'] === 'Ken') {
            $characterNamePrioritized = true;
            break;
        }
    }
    
    echo $characterNamePrioritized ? "✅ Character name prioritized correctly\n" : "❌ Character name not prioritized\n";
    echo "\n";
    
    // Test 4: Generic Term Protection
    echo "Test 4: Generic Term Protection\n";
    echo "Testing that generic terms are not incorrectly replaced\n";
    
    $genericTestNames = [
        [
            'original_name' => '彼',
            'translation' => 'He',
            'name_type' => 'other',
            'frequency' => 100
        ],
        [
            'original_name' => '彼女',
            'translation' => 'She',
            'name_type' => 'other',
            'frequency' => 80
        ]
    ];
    
    $testText4 = "He said that she would come. The he and she in the story are important.";
    $originalText4 = "彼は彼女が来ると言った。物語の彼と彼女は重要です。";
    
    echo "Original: {$testText4}\n";
    
    $result4 = $nameService->applyNameSubstitutions($testText4, $genericTestNames, $originalText4);
    
    echo "Result: {$result4['processed_text']}\n";
    echo "Substitutions made: " . count($result4['substitutions']) . "\n";
    
    // Verify that generic pronouns are not replaced inappropriately
    $genericTermsProtected = strpos($result4['processed_text'], 'He said') !== false &&
                            strpos($result4['processed_text'], 'she would') !== false;
    
    echo $genericTermsProtected ? "✅ Generic terms protected correctly\n" : "❌ Generic terms incorrectly replaced\n";
    echo "\n";
    
    // Test 5: Mixed Language Boundary Detection
    echo "Test 5: Mixed Language Boundary Detection\n";
    echo "Testing word boundary detection in mixed language content\n";
    
    $mixedNames = [
        [
            'original_name' => 'マリア',
            'translation' => 'Maria',
            'name_type' => 'character',
            'frequency' => 15
        ]
    ];
    
    $testText5 = "Maria-san spoke to マリア about the plan. The Maria in the story is important.";
    $originalText5 = "マリアさんはマリアに計画について話した。物語のマリアは重要です。";
    
    echo "Original: {$testText5}\n";
    
    $result5 = $nameService->applyNameSubstitutions($testText5, $mixedNames, $originalText5);
    
    echo "Result: {$result5['processed_text']}\n";
    echo "Substitutions made: " . count($result5['substitutions']) . "\n";
    
    // Verify that mixed language boundaries are handled correctly
    $boundariesCorrect = strpos($result5['processed_text'], 'Maria-san') !== false;
    
    echo $boundariesCorrect ? "✅ Mixed language boundaries handled correctly\n" : "❌ Mixed language boundary issues\n";
    echo "\n";
    
    echo "=== Conflict Resolution Test Suite Complete ===\n";
    echo "Check debug.log for detailed conflict resolution decisions.\n";
    
} catch (Exception $e) {
    echo "❌ Error during testing: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
