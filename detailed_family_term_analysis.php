<?php
/**
 * Detailed analysis of family term translation issues in Chapter 56
 */

require_once 'config/config.php';
require_once 'config/database.php';

$db = Database::getInstance();

echo "=== Detailed Family Term Analysis - Novel 7, Chapter 56 ===\n\n";

// Get chapter data
$chapter = $db->fetchOne(
    'SELECT id, original_content, translated_content
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter not found\n";
    exit;
}

$originalContent = $chapter['original_content'];
$translatedContent = $chapter['translated_content'];

echo "=== Finding Specific Translation Issues ===\n\n";

// Function to find context around a term
function findContext($text, $searchTerm, $contextLength = 100) {
    $positions = [];
    $offset = 0;
    
    while (($pos = stripos($text, $searchTerm, $offset)) !== false) {
        $start = max(0, $pos - $contextLength);
        $end = min(strlen($text), $pos + strlen($searchTerm) + $contextLength);
        $context = substr($text, $start, $end - $start);
        
        $positions[] = [
            'position' => $pos,
            'context' => $context,
            'before' => substr($text, $start, $pos - $start),
            'term' => substr($text, $pos, strlen($searchTerm)),
            'after' => substr($text, $pos + strlen($searchTerm), $end - $pos - strlen($searchTerm))
        ];
        
        $offset = $pos + 1;
    }
    
    return $positions;
}

// Check for "father" instances
echo "1. Checking 'father' translations:\n";
$fatherInstances = findContext($translatedContent, 'father');
foreach ($fatherInstances as $i => $instance) {
    echo "   Instance " . ($i + 1) . ":\n";
    echo "   Context: \"...{$instance['before']}[{$instance['term']}]{$instance['after']}...\"\n";
    echo "   → Should be: 'Otou-san' or 'Tou-san'\n\n";
}

// Check for "mother" instances  
echo "2. Checking 'mother' translations:\n";
$motherInstances = findContext($translatedContent, 'mother');
foreach ($motherInstances as $i => $instance) {
    echo "   Instance " . ($i + 1) . ":\n";
    echo "   Context: \"...{$instance['before']}[{$instance['term']}]{$instance['after']}...\"\n";
    echo "   → Should be: 'Okaa-san' or 'Kaa-san'\n\n";
}

// Check for "mom" instances
echo "3. Checking 'mom' translations:\n";
$momInstances = findContext($translatedContent, 'mom');
foreach ($momInstances as $i => $instance) {
    echo "   Instance " . ($i + 1) . ":\n";
    echo "   Context: \"...{$instance['before']}[{$instance['term']}]{$instance['after']}...\"\n";
    echo "   → Should be: 'Okaa-san' or 'Mama'\n\n";
}

// Now let's find the corresponding Japanese terms in the original
echo "=== Finding Corresponding Japanese Terms ===\n\n";

// Look for Japanese family terms in original content
$japaneseFamilyTerms = [
    '父さん' => 'Tou-san',
    'お父さん' => 'Otou-san', 
    '父' => 'Chichi/Otou-san',
    '母さん' => 'Kaa-san',
    'お母さん' => 'Okaa-san',
    '母' => 'Haha/Okaa-san',
    '兄さん' => 'Nii-san',
    'お兄さん' => 'Onii-san',
    'お兄ちゃん' => 'Onii-chan',
    '兄' => 'Ani/Onii-san'
];

echo "Japanese family terms found in original content:\n";
foreach ($japaneseFamilyTerms as $japanese => $romanized) {
    $count = substr_count($originalContent, $japanese);
    if ($count > 0) {
        echo "- {$japanese} ({$romanized}): {$count} occurrence(s)\n";
        
        // Show context for each occurrence
        $instances = findContext($originalContent, $japanese, 50);
        foreach ($instances as $i => $instance) {
            echo "  Context " . ($i + 1) . ": \"...{$instance['context']}...\"\n";
        }
        echo "\n";
    }
}

// Check name dictionary entries for these terms
echo "=== Name Dictionary Status ===\n";
$familyTermsInDict = $db->fetchAll(
    'SELECT original_name, romanization, translation 
     FROM name_dictionary 
     WHERE novel_id = 7 
     AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?, ?)
     ORDER BY original_name',
    ['父さん', 'お父さん', '父', '母さん', 'お母さん', '母', '兄さん', 'お兄さん', 'お兄ちゃん']
);

foreach ($familyTermsInDict as $entry) {
    echo "- {$entry['original_name']} → {$entry['romanization']} → {$entry['translation']}\n";
}

echo "\n=== Recommendations ===\n";
echo "1. The Gemini AI is translating family terms to English instead of preserving romanized forms\n";
echo "2. According to user preferences, family terms should remain as complete romanized units\n";
echo "3. The name dictionary contains correct romanizations, but they're not being used consistently\n";
echo "4. Translation instructions for Gemini need to be updated to enforce family term preservation\n\n";

echo "=== Suggested Corrections ===\n";
echo "- 'father' → 'Otou-san' or 'Tou-san'\n";
echo "- 'mother' → 'Okaa-san' or 'Kaa-san'\n"; 
echo "- 'mom' → 'Okaa-san'\n";
echo "- Ensure honorifics remain attached to family terms as complete units\n\n";

echo "=== Analysis Complete ===\n";
