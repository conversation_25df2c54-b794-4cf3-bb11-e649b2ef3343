<?php
/**
 * Check family terms in name dictionary
 */

require_once 'config/config.php';
require_once 'config/database.php';

$db = Database::getInstance();

echo "=== Family Terms in Name Dictionary for Novel 7 ===\n\n";

$familyTerms = $db->fetchAll(
    'SELECT original_name, romanization, translation, name_type, frequency 
     FROM name_dictionary 
     WHERE novel_id = 7 
     AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
     ORDER BY original_name',
    ['父さん', 'お父さん', '母さん', 'お母さん', 'お兄ちゃん', 'お姉ちゃん', 'おじさん', 'おばさん']
);

if (empty($familyTerms)) {
    echo "No family terms found in name dictionary.\n";
} else {
    foreach ($familyTerms as $term) {
        echo "- {$term['original_name']} → ";
        echo "romanization: '{$term['romanization']}', ";
        echo "translation: '{$term['translation']}', ";
        echo "type: {$term['name_type']}, ";
        echo "frequency: {$term['frequency']}\n";
    }
}

echo "\n=== All Family-Related Terms ===\n\n";

// Check for any terms that might be family-related
$allFamilyLike = $db->fetchAll(
    'SELECT original_name, romanization, translation, name_type, frequency 
     FROM name_dictionary 
     WHERE novel_id = 7 
     AND (original_name LIKE "%さん%" OR original_name LIKE "%ちゃん%" OR original_name LIKE "%父%" OR original_name LIKE "%母%" OR original_name LIKE "%兄%" OR original_name LIKE "%姉%")
     ORDER BY frequency DESC, original_name',
    []
);

foreach ($allFamilyLike as $term) {
    echo "- {$term['original_name']} → ";
    echo "romanization: '{$term['romanization']}', ";
    echo "translation: '{$term['translation']}', ";
    echo "type: {$term['name_type']}, ";
    echo "frequency: {$term['frequency']}\n";
}

echo "\n=== Analysis Complete ===\n";
