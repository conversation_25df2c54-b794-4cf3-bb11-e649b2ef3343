<?php
/**
 * Fix remaining "mom" term in Chapter 56
 */

require_once 'config/config.php';
require_once 'config/database.php';

$db = Database::getInstance();

echo "=== Fixing Remaining 'mom' Term in Chapter 56 ===\n\n";

// Get chapter 56
$chapter = $db->fetchOne(
    'SELECT id, translated_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

$content = $chapter['translated_content'];

// Find the "mom" instance and show context
$momPos = stripos($content, 'mom');
if ($momPos !== false) {
    $start = max(0, $momPos - 100);
    $end = min(strlen($content), $momPos + 100);
    $context = substr($content, $start, $end - $start);
    
    echo "Found 'mom' at position {$momPos}:\n";
    echo "Context: ...{$context}...\n\n";
    
    // Check if it's actually part of another word
    $beforeChar = $momPos > 0 ? $content[$momPos - 1] : ' ';
    $afterChar = $momPos + 3 < strlen($content) ? $content[$momPos + 3] : ' ';
    
    echo "Character before: '{$beforeChar}'\n";
    echo "Character after: '{$afterChar}'\n\n";
    
    // If it's part of "moment", don't replace it
    if (substr($content, $momPos, 6) === 'moment') {
        echo "✅ This is part of the word 'moment' - no replacement needed\n";
    } else {
        // Replace standalone "mom"
        $fixedContent = preg_replace('/\bmom\b/i', 'Okaa-san', $content, -1, $count);
        
        if ($count > 0) {
            echo "Replacing {$count} instance(s) of 'mom' with 'Okaa-san'\n";
            
            // Update the database
            $updateResult = $db->query(
                'UPDATE chapters SET translated_content = ?, updated_at = NOW() WHERE id = ?',
                [$fixedContent, $chapter['id']]
            );
            
            if ($updateResult) {
                echo "✅ Chapter 56 updated successfully!\n";
            } else {
                echo "❌ Failed to update chapter in database\n";
            }
        } else {
            echo "No replacements made\n";
        }
    }
} else {
    echo "✅ No 'mom' found in the content\n";
}

echo "\n=== Final Verification ===\n";

// Get updated content
$updatedChapter = $db->fetchOne(
    'SELECT translated_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
    []
);

$finalContent = $updatedChapter['translated_content'];

// Check for any remaining English family terms
$problematicTerms = ['father', 'mother', 'mom', 'dad', 'brother', 'sister'];
$remainingIssues = [];

foreach ($problematicTerms as $term) {
    $count = substr_count(strtolower($finalContent), strtolower($term));
    if ($count > 0) {
        // Check if they're part of other words
        $actualCount = 0;
        $offset = 0;
        while (($pos = stripos($finalContent, $term, $offset)) !== false) {
            $beforeChar = $pos > 0 ? $finalContent[$pos - 1] : ' ';
            $afterChar = $pos + strlen($term) < strlen($finalContent) ? $finalContent[$pos + strlen($term)] : ' ';
            
            // Check if it's a standalone word
            if (!ctype_alnum($beforeChar) && !ctype_alnum($afterChar)) {
                $actualCount++;
            }
            $offset = $pos + 1;
        }
        
        if ($actualCount > 0) {
            $remainingIssues[] = "{$term}: {$actualCount} standalone occurrences";
        }
    }
}

if (empty($remainingIssues)) {
    echo "✅ SUCCESS: No English family terms found - all fixes complete!\n";
} else {
    echo "⚠️ Remaining issues:\n";
    foreach ($remainingIssues as $issue) {
        echo "- {$issue}\n";
    }
}

echo "\n=== Fix Complete ===\n";
