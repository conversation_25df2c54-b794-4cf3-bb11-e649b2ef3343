<?php
/**
 * Base Crawler Class
 * Novel Translation Application
 */

abstract class BaseCrawler {
    protected $userAgent;
    protected $timeout;
    protected $retries;
    protected $delay;
    
    public function __construct() {
        $this->userAgent = USER_AGENT;
        $this->timeout = 30;
        $this->retries = MAX_RETRIES;
        $this->delay = CRAWL_DELAY;
    }
    
    /**
     * Abstract methods that must be implemented by specific crawlers
     */
    abstract public function getNovelInfo(string $url): array;
    abstract public function getChapterList(string $url): array;
    abstract public function getChapterContent(string $chapterUrl): array;
    abstract protected function validateUrl(string $url): bool;
    
    /**
     * Make HTTP request with retry logic
     */
    protected function makeRequest(string $url, array $options = []): string {
        $attempt = 0;
        $lastError = '';

        while ($attempt < $this->retries) {
            try {
                $ch = curl_init();

                $defaultOptions = [
                    CURLOPT_URL => $url,
                    CURLOPT_RETURNTRANSFER => true,
                    CURLOPT_FOLLOWLOCATION => true,
                    CURLOPT_TIMEOUT => $this->timeout,
                    CURLOPT_USERAGENT => $this->userAgent,
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => false,
                    CURLOPT_ENCODING => '', // Let curl handle all supported encodings automatically
                    CURLOPT_HTTPHEADER => [
                        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                        'Accept-Language: zh-CN,zh;q=0.9,en-US,en;q=0.5',
                        'Accept-Encoding: gzip, deflate',
                        'Connection: keep-alive',
                        'Upgrade-Insecure-Requests: 1',
                    ]
                ];

                // Merge options carefully to avoid conflicts with CURLOPT_HTTPHEADER
                $mergedOptions = $defaultOptions;
                foreach ($options as $key => $value) {
                    if ($key === CURLOPT_HTTPHEADER && isset($defaultOptions[CURLOPT_HTTPHEADER])) {
                        // Merge HTTP headers arrays
                        $mergedOptions[CURLOPT_HTTPHEADER] = array_merge($defaultOptions[CURLOPT_HTTPHEADER], $value);
                    } else {
                        $mergedOptions[$key] = $value;
                    }
                }

                curl_setopt_array($ch, $mergedOptions);
                
                $response = curl_exec($ch);
                $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
                $error = curl_error($ch);
                curl_close($ch);
                
                if ($error) {
                    throw new Exception("cURL error: {$error}");
                }
                
                if ($httpCode >= 400) {
                    throw new Exception("HTTP error: {$httpCode}");
                }
                
                if ($response === false) {
                    throw new Exception("Empty response");
                }
                
                // Add delay between requests
                if ($this->delay > 0) {
                    sleep($this->delay);
                }
                
                return $response;
                
            } catch (Exception $e) {
                $attempt++;
                $lastError = $e->getMessage();
                
                if ($attempt < $this->retries) {
                    // Exponential backoff
                    sleep(pow(2, $attempt));
                }
            }
        }
        
        throw new Exception("Failed to fetch URL after {$this->retries} attempts. Last error: {$lastError}");
    }
    
    /**
     * Parse HTML content using DOMDocument with proper Chinese character encoding
     */
    protected function parseHtml(string $html): DOMDocument {
        $dom = new DOMDocument();

        // Suppress warnings for malformed HTML
        libxml_use_internal_errors(true);

        // Detect encoding from meta tag first (more reliable for Chinese sites)
        $detectedEncoding = $this->detectHtmlEncoding($html);

        // Convert to UTF-8 if needed
        if ($detectedEncoding && strtoupper($detectedEncoding) !== 'UTF-8') {
            $this->log("Converting from $detectedEncoding to UTF-8");
            $html = mb_convert_encoding($html, 'UTF-8', $detectedEncoding);

            // Update the meta charset tag to UTF-8
            $html = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $html);
        }

        // Ensure the HTML has proper UTF-8 meta tag if none exists
        if (!preg_match('/<meta[^>]*charset[^>]*>/i', $html)) {
            $html = preg_replace('/(<head[^>]*>)/i', '$1<meta charset="UTF-8">', $html);
        }

        // Load HTML with proper UTF-8 handling
        $dom->encoding = 'UTF-8';

        // Try direct loading first
        $success = $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);

        // Fallback method if the above fails
        if (!$success) {
            $dom = new DOMDocument();
            $dom->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        }

        // Clear libxml errors
        libxml_clear_errors();

        return $dom;
    }

    /**
     * Detect HTML encoding from meta tags and content
     */
    protected function detectHtmlEncoding(string $html): ?string {
        // First, try to find charset in meta tags
        if (preg_match('/<meta[^>]*charset[^>]*=[\'"]*([^\'"\s>]+)/i', $html, $matches)) {
            $encoding = trim($matches[1]);
            $this->log("Found charset in meta tag: $encoding");
            return $encoding;
        }

        // Try Content-Type meta tag
        if (preg_match('/<meta[^>]*http-equiv[^>]*content-type[^>]*content[^>]*charset=([^\'"\s>]+)/i', $html, $matches)) {
            $encoding = trim($matches[1]);
            $this->log("Found charset in Content-Type meta tag: $encoding");
            return $encoding;
        }

        // Fallback to mb_detect_encoding with Chinese encodings prioritized
        $encoding = mb_detect_encoding($html, ['GBK', 'GB2312', 'UTF-8', 'BIG5'], true);
        if ($encoding) {
            $this->log("Detected encoding via mb_detect_encoding: $encoding");
            return $encoding;
        }

        $this->log("Could not detect encoding, assuming UTF-8");
        return 'UTF-8';
    }
    
    /**
     * Extract text content from DOM element
     */
    protected function getTextContent(DOMElement $element): string {
        return trim($element->textContent);
    }

    /**
     * Extract text content preserving furigana (ruby) markup
     */
    protected function getTextContentWithFurigana(DOMElement $element): string {
        return $this->processNodeForFurigana($element);
    }

    /**
     * Process DOM node to preserve furigana information
     */
    protected function processNodeForFurigana(DOMNode $node): string {
        $result = '';

        foreach ($node->childNodes as $child) {
            if ($child->nodeType === XML_TEXT_NODE) {
                $result .= $child->textContent;
            } elseif ($child->nodeType === XML_ELEMENT_NODE) {
                switch (strtolower($child->nodeName)) {
                    case 'ruby':
                        $result .= $this->processRubyElement($child);
                        break;
                    case 'rt':
                        // Skip rt elements when not inside ruby processing
                        break;
                    case 'rp':
                        // Skip rp elements (ruby parentheses)
                        break;
                    case 'br':
                        $result .= "\n";
                        break;
                    case 'p':
                    case 'div':
                        $result .= $this->processNodeForFurigana($child);
                        if ($child->nodeName === 'p') {
                            $result .= "\n\n";
                        } else {
                            $result .= "\n";
                        }
                        break;
                    default:
                        $result .= $this->processNodeForFurigana($child);
                        break;
                }
            }
        }

        return $result;
    }

    /**
     * Process ruby element to create furigana markup
     */
    protected function processRubyElement(DOMElement $rubyElement): string {
        $baseText = '';
        $furiganaText = '';

        foreach ($rubyElement->childNodes as $child) {
            if ($child->nodeType === XML_TEXT_NODE) {
                $baseText .= $child->textContent;
            } elseif ($child->nodeType === XML_ELEMENT_NODE) {
                switch (strtolower($child->nodeName)) {
                    case 'rt':
                        $furiganaText .= $child->textContent;
                        break;
                    case 'rp':
                        // Skip ruby parentheses
                        break;
                    default:
                        $baseText .= $child->textContent;
                        break;
                }
            }
        }

        // Return furigana in a custom markup format: {base|furigana}
        if (!empty($furiganaText)) {
            return "{" . trim($baseText) . "|" . trim($furiganaText) . "}";
        }

        return $baseText;
    }
    
    /**
     * Get element by CSS selector (simplified)
     */
    protected function querySelector(DOMDocument $dom, string $selector): ?DOMElement {
        $xpath = new DOMXPath($dom);
        
        // Convert simple CSS selectors to XPath
        $xpathQuery = $this->cssToXpath($selector);
        $nodes = $xpath->query($xpathQuery);
        
        return $nodes->length > 0 ? $nodes->item(0) : null;
    }
    
    /**
     * Get elements by CSS selector (simplified)
     */
    protected function querySelectorAll(DOMDocument $dom, string $selector): DOMNodeList {
        $xpath = new DOMXPath($dom);
        $xpathQuery = $this->cssToXpath($selector);
        $result = $xpath->query($xpathQuery);

        // Return empty DOMNodeList if query failed
        if ($result === false) {
            return new DOMNodeList();
        }

        return $result;
    }
    
    /**
     * Convert simple CSS selectors to XPath (basic implementation)
     */
    private function cssToXpath(string $selector): string {
        // Handle basic selectors
        $selector = trim($selector);

        // Handle complex selectors with spaces (descendant combinator)
        if (strpos($selector, ' ') !== false) {
            $parts = explode(' ', $selector);
            $xpath = '';
            foreach ($parts as $part) {
                $part = trim($part);
                if ($part) {
                    $xpath .= $this->singleSelectorToXpath($part);
                }
            }
            return $xpath;
        }

        return $this->singleSelectorToXpath($selector);
    }

    /**
     * Convert a single CSS selector to XPath
     */
    private function singleSelectorToXpath(string $selector): string {
        $selector = trim($selector);

        // Class selector
        if (strpos($selector, '.') === 0) {
            $class = substr($selector, 1);
            return "//*[contains(concat(' ', normalize-space(@class), ' '), ' {$class} ')]";
        }

        // ID selector
        if (strpos($selector, '#') === 0) {
            $id = substr($selector, 1);
            return "//*[@id='{$id}']";
        }

        // Attribute selector with contains (*=)
        if (preg_match('/([a-zA-Z0-9]*)\[([^*=]+)\*="([^"]+)"\]/', $selector, $matches)) {
            $tag = $matches[1] ?: '*';
            $attr = $matches[2];
            $value = $matches[3];
            return "//{$tag}[contains(@{$attr}, '{$value}')]";
        }

        // Attribute selector with equals (=)
        if (preg_match('/([a-zA-Z0-9]*)\[([^=]+)="([^"]+)"\]/', $selector, $matches)) {
            $tag = $matches[1] ?: '*';
            $attr = $matches[2];
            $value = $matches[3];
            return "//{$tag}[@{$attr}='{$value}']";
        }

        // Tag with class
        if (preg_match('/^([a-zA-Z][a-zA-Z0-9]*)\.([a-zA-Z0-9_-]+)$/', $selector, $matches)) {
            $tag = $matches[1];
            $class = $matches[2];
            return "//{$tag}[contains(concat(' ', normalize-space(@class), ' '), ' {$class} ')]";
        }

        // Tag selector
        if (preg_match('/^[a-zA-Z][a-zA-Z0-9]*$/', $selector)) {
            return "//{$selector}";
        }

        // Default to any element
        return "//*";
    }
    
    /**
     * Clean and normalize text with proper Chinese character handling
     */
    protected function cleanText(string $text): string {
        // Ensure UTF-8 encoding
        if (!mb_check_encoding($text, 'UTF-8')) {
            $encoding = mb_detect_encoding($text, ['UTF-8', 'GB2312', 'GBK', 'BIG5'], true);
            if ($encoding) {
                $text = mb_convert_encoding($text, 'UTF-8', $encoding);
            }
        }

        // Remove HTML entities while preserving Chinese characters
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Clean up whitespace but preserve Chinese text structure
        // Replace multiple spaces/tabs with single space
        $text = preg_replace('/[ \t]+/', ' ', $text);

        // Clean up excessive line breaks but preserve paragraph structure
        $text = preg_replace('/\n{3,}/', "\n\n", $text);

        // Remove any remaining HTML tags
        $text = strip_tags($text);

        // Remove control characters but preserve Chinese characters
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Trim
        $text = trim($text);

        return $text;
    }
    
    /**
     * Extract date from text
     */
    protected function extractDate(string $text): ?string {
        // Common date patterns - prioritize Syosetu format
        $patterns = [
            '/(\d{4})\/(\d{1,2})\/(\d{1,2})\s+\d{1,2}:\d{2}/',  // YYYY/MM/DD HH:MM (Syosetu format)
            '/(\d{4})[\/\-年](\d{1,2})[\/\-月](\d{1,2})[日]?/',  // YYYY/MM/DD or YYYY年MM月DD日
            '/(\d{1,2})[\/\-](\d{1,2})[\/\-](\d{4})/',          // MM/DD/YYYY
            '/(\d{4})年(\d{1,2})月(\d{1,2})日/',                // YYYY年MM月DD日
            '/(\d{4})-(\d{1,2})-(\d{1,2})/',                   // YYYY-MM-DD
            '/(\d{4})\.(\d{1,2})\.(\d{1,2})/',                 // YYYY.MM.DD
            '/(\d{1,2})\.(\d{1,2})\.(\d{4})/',                 // DD.MM.YYYY
            '/(\d{4})\/(\d{1,2})\/(\d{1,2})/',                 // YYYY/MM/DD
            '/(\d{1,2})\/(\d{1,2})\/(\d{4})/',                 // MM/DD/YYYY
            '/(\d{4})年(\d{1,2})月/',                          // YYYY年MM月 (without day)
            '/(\d{4})[年\-\/](\d{1,2})[月\-\/]/',              // YYYY年MM月 or YYYY-MM- or YYYY/MM/
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $text, $matches)) {
                if (count($matches) >= 3) {
                    // Determine date format and convert to YYYY-MM-DD
                    if (strlen($matches[1]) === 4) {
                        // YYYY-MM-DD format
                        $year = $matches[1];
                        $month = $matches[2];
                        $day = isset($matches[3]) ? $matches[3] : '01'; // Default to 1st if no day

                        // Validate the date values
                        if ($year >= 1900 && $year <= 2100 && $month >= 1 && $month <= 12 && $day >= 1 && $day <= 31) {
                            return sprintf('%04d-%02d-%02d', $year, $month, $day);
                        }
                    } else if (count($matches) >= 4 && strlen($matches[3]) === 4) {
                        // MM/DD/YYYY or DD.MM.YYYY format
                        $year = $matches[3];
                        $month = $matches[1];
                        $day = $matches[2];

                        // Validate the date values
                        if ($year >= 1900 && $year <= 2100 && $month >= 1 && $month <= 12 && $day >= 1 && $day <= 31) {
                            return sprintf('%04d-%02d-%02d', $year, $month, $day);
                        }
                    }
                }
            }
        }

        // Try to parse ISO date format
        if (preg_match('/(\d{4}-\d{2}-\d{2})T/', $text, $matches)) {
            return $matches[1];
        }

        // Try to parse timestamp
        if (preg_match('/(\d{10})/', $text, $matches)) {
            $timestamp = (int)$matches[1];
            if ($timestamp > 946684800 && $timestamp < 2147483647) { // Valid range 2000-2038
                return date('Y-m-d', $timestamp);
            }
        }

        return null;
    }
    
    /**
     * Extract numbers from text
     */
    protected function extractNumber(string $text): int {
        if (preg_match('/(\d+)/', $text, $matches)) {
            return (int) $matches[1];
        }
        return 0;
    }
    
    /**
     * Validate and normalize URL
     */
    protected function normalizeUrl(string $url, string $baseUrl = ''): string {
        // If URL is relative, make it absolute
        if (strpos($url, 'http') !== 0 && $baseUrl) {
            if (strpos($url, '/') === 0) {
                // Absolute path
                $parsedBase = parse_url($baseUrl);
                $url = $parsedBase['scheme'] . '://' . $parsedBase['host'] . $url;
            } else {
                // Relative path
                $url = rtrim($baseUrl, '/') . '/' . ltrim($url, '/');
            }
        }
        
        return $url;
    }
    
    /**
     * Log crawler activity
     */
    protected function log(string $message, string $level = 'info'): void {
        $timestamp = date('Y-m-d H:i:s');
        $logMessage = "[{$timestamp}] [{$level}] " . get_class($this) . ": {$message}";
        
        // Log to file
        $logFile = APP_ROOT . '/logs/crawler.log';
        $logDir = dirname($logFile);
        
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND | LOCK_EX);
    }
}
