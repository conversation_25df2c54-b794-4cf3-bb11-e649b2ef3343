<?php
/**
 * Gemini 2.5 Translation Service
 * Google Gemini 2.5 Flash AI Translation Implementation (Experimental Preview)
 * Novel Translation Application
 */

require_once 'Gemini20TranslationService.php';

class Gemini25TranslationService extends Gemini20TranslationService {
    
    protected function initializeVersionConfig(): void {
        $this->version = '2.5';
        $this->apiUrl = GEMINI_25_API_URL;
        $this->model = GEMINI_25_MODEL;
    }

    /**
     * Calculate adaptive token limit based on content size and type (optimized for Gemini 2.5)
     */
    protected function calculateAdaptiveTokenLimit(string $prompt, array $context): int {
        $promptLength = mb_strlen($prompt);

        // For titles, use very small limit to prevent truncation
        if (isset($context['type']) && $context['type'] === 'title') {
            $baseLimit = 80; // Higher for Gemini 2.5 experimental

            // Reduce for retry attempts
            if (isset($context['truncation_retry'])) {
                $retryAttempt = $context['truncation_retry'];
                $baseLimit = max(30, $baseLimit - ($retryAttempt * 15));
                file_put_contents('debug.log', "Gemini25TranslationService: Title retry attempt {$retryAttempt}, using token limit: {$baseLimit}\n", FILE_APPEND);
            }

            return $baseLimit;
        }

        // For content, calculate based on input size (highest limits for Gemini 2.5)
        $baseLimit = 4096; // Higher default for Gemini 2.5

        if ($promptLength < 2000) {
            $baseLimit = 4096; // Small content
        } elseif ($promptLength < 6000) {
            $baseLimit = 6144; // Medium content
        } elseif ($promptLength < 12000) {
            $baseLimit = 8192; // Large content
        } else {
            $baseLimit = 10240; // Very large content (experimental)
        }

        // Reduce token limit for retry attempts
        if (isset($context['truncation_retry'])) {
            $retryAttempt = $context['truncation_retry'];
            $reductionFactor = 1 - ($retryAttempt * 0.2); // Reduce by 20% per retry (less aggressive)
            $baseLimit = max(2048, floor($baseLimit * $reductionFactor));

            file_put_contents('debug.log', "Gemini25TranslationService: Retry attempt {$retryAttempt}, reduced token limit to {$baseLimit}\n", FILE_APPEND);
        }

        return $baseLimit;
    }

    /**
     * Execute single API request to Gemini 2.5 (with experimental configuration)
     */
    protected function executeSingleApiRequest(string $prompt, array $context, int $attempt): array {
        error_log("Gemini25TranslationService: Making API request (attempt {$attempt})");
        error_log("Gemini25TranslationService: API Key length: " . strlen($this->apiKey));
        error_log("Gemini25TranslationService: Prompt length: " . strlen($prompt));

        // Calculate adaptive token limit
        $adaptiveTokenLimit = $this->calculateAdaptiveTokenLimit($prompt, $context);

        // Experimental generation config for Gemini 2.5
        $generationConfig = [
            'temperature' => 0.2, // Lower for better consistency in experimental model
            'topK' => 60, // Higher for experimental model
            'topP' => 0.85, // Optimized for Gemini 2.5
            'maxOutputTokens' => $adaptiveTokenLimit
        ];

        // Adjust parameters for title translations
        if (isset($context['type']) && $context['type'] === 'title') {
            $generationConfig['temperature'] = 0.02; // Very low for titles in experimental model
            $generationConfig['topK'] = 10;
            $generationConfig['topP'] = 0.7;
            $generationConfig['maxOutputTokens'] = 80; // Higher for experimental model

            file_put_contents('debug.log', "Gemini25TranslationService: Using experimental maxOutputTokens: 80 for title translation\n", FILE_APPEND);
        }

        // Build request data in Gemini format
        $requestData = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt
                        ]
                    ]
                ]
            ],
            'generationConfig' => $generationConfig,
            'safetySettings' => [
                [
                    'category' => 'HARM_CATEGORY_HARASSMENT',
                    'threshold' => 'BLOCK_NONE'
                ],
                [
                    'category' => 'HARM_CATEGORY_HATE_SPEECH',
                    'threshold' => 'BLOCK_NONE'
                ],
                [
                    'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                    'threshold' => 'BLOCK_NONE'
                ],
                [
                    'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                    'threshold' => 'BLOCK_NONE'
                ]
            ]
        ];

        // Log token limit for debugging
        file_put_contents('debug.log', "Gemini25TranslationService: Using maxOutputTokens: {$generationConfig['maxOutputTokens']}\n", FILE_APPEND);

        $jsonData = json_encode($requestData);
        error_log("Gemini25TranslationService: Request data size: " . strlen($jsonData) . " bytes");

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl . '?key=' . $this->apiKey,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
            ],
            CURLOPT_TIMEOUT => TRANSLATION_TIMEOUT + 60, // Extra timeout for experimental model
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log response details
        error_log("Gemini25TranslationService: HTTP Code: " . $httpCode);
        error_log("Gemini25TranslationService: Response length: " . strlen($response));

        if ($error) {
            error_log("Gemini25TranslationService: cURL error: " . $error);
            return ['success' => false, 'error' => "cURL error: {$error}", 'retryable' => true];
        }

        if ($httpCode !== 200) {
            error_log("Gemini25TranslationService: HTTP error response: " . substr($response, 0, 500));

            // Parse error response for better error handling
            $errorDetails = $this->parseErrorResponse($response, $httpCode);

            return [
                'success' => false,
                'error' => "HTTP error: {$httpCode}",
                'details' => $errorDetails,
                'retryable' => $this->isHttpCodeRetryable($httpCode),
                'response' => $response
            ];
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Gemini25TranslationService: JSON decode error: " . json_last_error_msg());
            error_log("Gemini25TranslationService: Raw response: " . substr($response, 0, 500));
            return ['success' => false, 'error' => 'Invalid JSON response: ' . json_last_error_msg(), 'retryable' => false];
        }

        error_log("Gemini25TranslationService: API request successful");
        return ['success' => true, 'data' => $decodedResponse];
    }

    /**
     * Analyze content for timeout risk (optimized for Gemini 2.5 experimental)
     */
    protected function analyzeContentForTimeout(string $text, array $context): array {
        $length = mb_strlen($text);
        $analysis = [
            'high_timeout_risk' => false,
            'reason' => '',
            'recommended_action' => '',
            'estimated_time' => 0
        ];

        // Base timeout risk on content length (higher thresholds for experimental Gemini 2.5)
        if ($length > 20000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 20,000 characters';
            $analysis['recommended_action'] = 'chunk_content';
        } elseif ($length > 15000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 15,000 characters';
            $analysis['recommended_action'] = 'reduce_timeout';
        }

        // Check for complexity factors that increase timeout risk
        $complexityFactors = [];

        // High dialogue density (higher threshold for experimental model)
        $dialogueMatches = preg_match_all('/「[^」]*」|"[^"]*"/', $text);
        if ($dialogueMatches > 40) {
            $complexityFactors[] = 'high_dialogue_density';
        }

        // Technical terms or complex vocabulary
        $kanjiDensity = preg_match_all('/[\x{4e00}-\x{9faf}]/u', $text) / max(1, $length);
        if ($kanjiDensity > 0.35) {
            $complexityFactors[] = 'high_kanji_density';
        }

        // Check for furigana content (more processing intensive)
        if (preg_match('/[\x{3040}-\x{309F}]/u', $text)) {
            $complexityFactors[] = 'furigana_content';
        }

        // Check for long continuous text blocks (harder to process)
        $longBlocks = preg_match_all('/[^\n]{250,}/', $text);
        if ($longBlocks > 8) {
            $complexityFactors[] = 'long_text_blocks';
        }

        // If we have complexity factors and moderate length, increase risk
        if (!empty($complexityFactors) && $length > 10000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Complex content (' . implode(', ', $complexityFactors) . ') with length ' . $length . ' characters';
            $analysis['recommended_action'] = 'chunk_content';
        }

        // Estimate translation time (optimized for experimental Gemini 2.5)
        $analysis['estimated_time'] = max(12, ($length / 120) * 1.5); // ~1.5 seconds per 120 characters

        return $analysis;
    }

    /**
     * Split oversized content into smaller pieces (optimized for Gemini 2.5)
     */
    protected function splitOversizedContent(string $text): array {
        $maxPieceSize = 10000; // Larger pieces for experimental Gemini 2.5
        $pieces = [];

        // Try to split by paragraphs first
        $paragraphs = preg_split('/\n\s*\n/', $text, -1, PREG_SPLIT_NO_EMPTY);

        $currentPiece = '';
        foreach ($paragraphs as $paragraph) {
            if (mb_strlen($currentPiece . $paragraph) > $maxPieceSize && !empty($currentPiece)) {
                $pieces[] = trim($currentPiece);
                $currentPiece = $paragraph;
            } else {
                $currentPiece .= ($currentPiece ? "\n\n" : '') . $paragraph;
            }
        }

        if (!empty($currentPiece)) {
            $pieces[] = trim($currentPiece);
        }

        return $pieces;
    }

    /**
     * Make API request to Gemini 2.5 (with experimental retry logic)
     */
    protected function makeApiRequest(string $prompt, array $context = [], array $contentAnalysis = []): array {
        $maxRetries = 4; // Extra retry for experimental model
        $retryDelay = 2; // Longer delay for experimental model

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            file_put_contents('debug.log', "Gemini25TranslationService: Experimental attempt {$attempt} of {$maxRetries}\n", FILE_APPEND);

            $result = $this->executeSingleApiRequest($prompt, $context, $attempt);

            if ($result['success']) {
                return $result;
            }

            // Check if error is retryable
            if (!$this->isRetryableError($result)) {
                file_put_contents('debug.log', "Gemini25TranslationService: Non-retryable error, stopping retries\n", FILE_APPEND);
                return $result;
            }

            // Wait before retry (exponential backoff with longer delays for experimental model)
            if ($attempt < $maxRetries) {
                $delay = $retryDelay * pow(2, $attempt - 1);
                file_put_contents('debug.log', "Gemini25TranslationService: Waiting {$delay}s before experimental retry\n", FILE_APPEND);
                sleep($delay);
            }
        }

        return $result;
    }
}
