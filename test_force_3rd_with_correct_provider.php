<?php
/**
 * Test force 3rd person with the correct active provider
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';
require_once 'classes/TranslationService.php';

echo "=== Testing Force 3rd Person with Correct Provider ===\n\n";

// Check current active provider
$aiProviderManager = new AIProviderManager();
$activeProvider = $aiProviderManager->getActiveProvider();

echo "Current active provider: {$activeProvider}\n\n";

// Test text with first-person pronouns that should be converted
$testText = "私は考えている。我々はこれをテストする必要がある。私の意見では、これは重要だ。";

echo "Original Japanese text:\n";
echo $testText . "\n\n";

// Create narrative context that should trigger force 3rd person
$narrativeContext = [
    'narrative_voice' => 'first_person',
    'narrative_confidence' => 0.8,
    'perspective_indicators' => [
        'first_person' => ['私は', '我々は', '私の'],
        'third_person' => []
    ]
];

$fullContext = [
    'type' => 'chunk',
    'user_pov_preference' => 'third_person_omniscient',
    'narrative_context' => $narrativeContext
];

echo "=== Testing Translation with Active Provider ===\n";

try {
    $translationService = new TranslationService();
    
    echo "Using TranslationService with active provider: {$activeProvider}\n";
    
    $result = $translationService->translateText($testText, 'en', 'auto', $fullContext);
    
    if ($result['success']) {
        echo "✅ Translation successful!\n\n";
        echo "Translated text:\n";
        echo "================\n";
        echo $result['translated_text'];
        echo "\n================\n\n";
        
        // Analyze the result for POV
        $translatedText = $result['translated_text'];
        
        // Count first-person pronouns
        $firstPersonPronouns = ['I ', ' I ', 'I\'', 'me ', ' me', 'my ', ' my', 'we ', ' we', 'us ', ' us', 'our ', ' our'];
        $firstPersonCount = 0;
        $firstPersonFound = [];
        
        foreach ($firstPersonPronouns as $pronoun) {
            $count = substr_count($translatedText, $pronoun);
            if ($count > 0) {
                $firstPersonCount += $count;
                $firstPersonFound[] = trim($pronoun) . ": {$count}";
            }
        }
        
        // Count third-person pronouns
        $thirdPersonPronouns = ['he ', ' he', 'she ', ' she', 'they ', ' they', 'him ', ' him', 'her ', ' her', 'them ', ' them', 'his ', ' his', 'their ', ' their'];
        $thirdPersonCount = 0;
        $thirdPersonFound = [];
        
        foreach ($thirdPersonPronouns as $pronoun) {
            $count = substr_count($translatedText, $pronoun);
            if ($count > 0) {
                $thirdPersonCount += $count;
                $thirdPersonFound[] = trim($pronoun) . ": {$count}";
            }
        }
        
        echo "=== POV Analysis ===\n";
        echo "First-person pronouns: {$firstPersonCount}\n";
        if (!empty($firstPersonFound)) {
            echo "- " . implode(', ', $firstPersonFound) . "\n";
        }
        
        echo "Third-person pronouns: {$thirdPersonCount}\n";
        if (!empty($thirdPersonFound)) {
            echo "- " . implode(', ', $thirdPersonFound) . "\n";
        }
        
        $totalPronouns = $firstPersonCount + $thirdPersonCount;
        if ($totalPronouns > 0) {
            $thirdPersonRatio = ($thirdPersonCount / $totalPronouns) * 100;
            echo "Third-person ratio: " . round($thirdPersonRatio, 1) . "%\n";
            
            if ($firstPersonCount == 0 && $thirdPersonCount > 0) {
                echo "✅ PERFECT: Force 3rd person working perfectly!\n";
            } elseif ($thirdPersonRatio >= 80) {
                echo "✅ GOOD: Mostly third-person perspective\n";
            } elseif ($thirdPersonRatio >= 50) {
                echo "⚠️ MIXED: Mixed perspective (may include dialogue)\n";
            } else {
                echo "❌ ISSUE: Still primarily first-person\n";
                echo "This indicates force 3rd person is not working with {$activeProvider}\n";
            }
        } else {
            echo "⚠️ No personal pronouns found\n";
        }
        
    } else {
        echo "❌ Translation failed: " . $result['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during translation: " . $e->getMessage() . "\n";
}

// Test switching to DeepSeek if it's not the active provider
if ($activeProvider !== 'deepseek') {
    echo "\n=== Testing with DeepSeek Provider ===\n";
    
    try {
        // Switch to DeepSeek temporarily
        $aiProviderManager->setActiveProvider('deepseek');
        echo "Switched to DeepSeek provider\n";
        
        $result = $translationService->translateText($testText, 'en', 'auto', $fullContext);
        
        if ($result['success']) {
            echo "✅ DeepSeek translation successful!\n";
            echo "DeepSeek result: " . $result['translated_text'] . "\n";
            
            // Quick POV check
            $translatedText = $result['translated_text'];
            $firstPersonCount = substr_count($translatedText, 'I ') + substr_count($translatedText, ' I ') + substr_count($translatedText, 'my ') + substr_count($translatedText, 'we ');
            $thirdPersonCount = substr_count($translatedText, 'he ') + substr_count($translatedText, 'she ') + substr_count($translatedText, 'they ') + substr_count($translatedText, 'his ') + substr_count($translatedText, 'her ') + substr_count($translatedText, 'their ');
            
            if ($firstPersonCount == 0 && $thirdPersonCount > 0) {
                echo "✅ PERFECT: DeepSeek force 3rd person working!\n";
            } else {
                echo "❌ DeepSeek force 3rd person not working (1st: {$firstPersonCount}, 3rd: {$thirdPersonCount})\n";
            }
        } else {
            echo "❌ DeepSeek translation failed: " . $result['error'] . "\n";
        }
        
        // Switch back to original provider
        $aiProviderManager->setActiveProvider($activeProvider);
        echo "Switched back to {$activeProvider} provider\n";
        
    } catch (Exception $e) {
        echo "❌ Error testing DeepSeek: " . $e->getMessage() . "\n";
        // Make sure to switch back
        $aiProviderManager->setActiveProvider($activeProvider);
    }
}

echo "\n=== Summary ===\n";
echo "The force 3rd person implementation is ready in DeepSeek service.\n";
echo "Current active provider: {$activeProvider}\n";
echo "For force 3rd person to work, you need to:\n";
echo "1. Switch to DeepSeek provider in the UI\n";
echo "2. Clear chapter 171 translation\n";
echo "3. Translate each part/chunk again\n";
echo "4. The result should use third-person perspective automatically\n";

echo "\nDone.\n";
