<?php
/**
 * Check translation quality for Novel ID 7, Chapter 56
 * Specifically looking for family term translation errors
 */

require_once 'config/config.php';
require_once 'config/database.php';

$db = Database::getInstance();

echo "=== Checking Novel ID 7, Chapter 56 Translation ===\n\n";

// Get chapter data
$chapter = $db->fetchOne(
    'SELECT id, chapter_number, original_title, translated_title, 
            original_content, translated_content, translation_status
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found for Novel ID 7\n";
    exit;
}

echo "Chapter found:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Original Title: {$chapter['original_title']}\n";
echo "- Translated Title: {$chapter['translated_title']}\n";
echo "- Translation Status: {$chapter['translation_status']}\n";
echo "- Has Original Content: " . (!empty($chapter['original_content']) ? 'Yes' : 'No') . "\n";
echo "- Has Translated Content: " . (!empty($chapter['translated_content']) ? 'Yes' : 'No') . "\n\n";

if (empty($chapter['translated_content'])) {
    echo "❌ No translated content found for this chapter\n";
    exit;
}

// Get name dictionary for Novel 7
$nameDictionary = $db->fetchAll(
    'SELECT original_name, romanization, translation, name_type, frequency 
     FROM name_dictionary 
     WHERE novel_id = 7 
     ORDER BY frequency DESC',
    []
);

echo "Name dictionary entries: " . count($nameDictionary) . "\n\n";

// Look for family terms in the name dictionary
$familyTerms = [];
foreach ($nameDictionary as $entry) {
    $original = $entry['original_name'];
    if (preg_match('/[父母兄姉弟妹]|お[父母兄姉]|おじ|おば/', $original)) {
        $familyTerms[] = $entry;
    }
}

echo "=== Family Terms in Name Dictionary ===\n";
if (empty($familyTerms)) {
    echo "No family terms found in name dictionary\n";
} else {
    foreach ($familyTerms as $term) {
        echo "- {$term['original_name']} → {$term['romanization']} → {$term['translation']}\n";
    }
}
echo "\n";

// Analyze translated content for family term issues
echo "=== Analyzing Translated Content ===\n";

$translatedContent = $chapter['translated_content'];
$originalContent = $chapter['original_content'];

// Look for problematic English family terms that should be romanized
$problematicTerms = [
    'big brother' => 'onii-chan/onii-san',
    'big sister' => 'onee-chan/onee-san', 
    'father' => 'otou-san/tou-san',
    'mother' => 'okaa-san/kaa-san',
    'dad' => 'otou-san/papa',
    'mom' => 'okaa-san/mama',
    'uncle' => 'ojisan',
    'aunt' => 'obasan',
    'grandfather' => 'ojiisan',
    'grandmother' => 'obaasan'
];

$foundIssues = [];

foreach ($problematicTerms as $englishTerm => $shouldBe) {
    if (stripos($translatedContent, $englishTerm) !== false) {
        // Count occurrences
        $count = substr_count(strtolower($translatedContent), strtolower($englishTerm));
        $foundIssues[] = [
            'term' => $englishTerm,
            'count' => $count,
            'should_be' => $shouldBe
        ];
    }
}

if (empty($foundIssues)) {
    echo "✅ No problematic family term translations found\n";
} else {
    echo "❌ Found problematic family term translations:\n";
    foreach ($foundIssues as $issue) {
        echo "- '{$issue['term']}' appears {$issue['count']} time(s) - should be '{$issue['should_be']}'\n";
    }
}

// Look for isolated honorifics (like "-chan" without the family term)
echo "\n=== Checking for Isolated Honorifics ===\n";

$isolatedHonorifics = [];
$honorificPatterns = [
    '/-chan\b/',
    '/-san\b/', 
    '/-sama\b/',
    '/-kun\b/'
];

foreach ($honorificPatterns as $pattern) {
    if (preg_match_all($pattern, $translatedContent, $matches)) {
        $isolatedHonorifics[] = [
            'honorific' => str_replace(['/', '\b'], '', $pattern),
            'count' => count($matches[0])
        ];
    }
}

if (empty($isolatedHonorifics)) {
    echo "✅ No isolated honorifics found\n";
} else {
    echo "❌ Found isolated honorifics:\n";
    foreach ($isolatedHonorifics as $honorific) {
        echo "- '{$honorific['honorific']}' appears {$honorific['count']} time(s)\n";
    }
}

// Show sample of translated content for manual review
echo "\n=== Sample of Translated Content (first 1000 characters) ===\n";
echo substr($translatedContent, 0, 1000) . "...\n\n";

// Show sample of original content for comparison
echo "=== Sample of Original Content (first 1000 characters) ===\n";
echo substr($originalContent, 0, 1000) . "...\n\n";

echo "=== Analysis Complete ===\n";
