<?php
/**
 * Debug DeepSeek POV prompt generation
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/POVPreferenceManager.php';
require_once 'classes/PerspectiveService.php';
require_once 'classes/DeepSeekTranslationService.php';

echo "=== Debugging DeepSeek POV Prompt Generation ===\n\n";

// Create test context with narrative_context
$testContext = [
    'type' => 'chunk',
    'user_pov_preference' => 'third_person_omniscient',
    'narrative_context' => [
        'content_type' => 'narrative',
        'optimal_perspective' => 'third_person_omniscient',
        'perspective_reasoning' => 'User selected third person omniscient',
        'perspective_instructions' => 'Test POV instructions',
        'user_selected' => true
    ]
];

echo "Test context:\n";
print_r($testContext);

$deepSeekService = new DeepSeekTranslationService();
$testText = "I think this is a test. We should check if it works.";

echo "\n=== Testing buildTranslationPrompt ===\n";

// Use reflection to access protected method
$reflection = new ReflectionClass($deepSeekService);
$method = $reflection->getMethod('buildTranslationPrompt');
$method->setAccessible(true);

try {
    $prompt = $method->invoke($deepSeekService, $testText, 'en', 'auto', $testContext, []);
    
    echo "Generated prompt:\n";
    echo "================\n";
    echo $prompt;
    echo "\n================\n\n";
    
    // Check for POV-related content
    echo "=== POV Content Analysis ===\n";
    
    if (strpos($prompt, 'narrative_context') !== false) {
        echo "✅ 'narrative_context' found in prompt\n";
    } else {
        echo "❌ 'narrative_context' NOT found in prompt\n";
    }
    
    if (strpos($prompt, 'PERSPECTIVE') !== false) {
        echo "✅ 'PERSPECTIVE' found in prompt\n";
    } else {
        echo "❌ 'PERSPECTIVE' NOT found in prompt\n";
    }
    
    if (strpos($prompt, 'third_person_omniscient') !== false) {
        echo "✅ 'third_person_omniscient' found in prompt\n";
    } else {
        echo "❌ 'third_person_omniscient' NOT found in prompt\n";
    }
    
    if (strpos($prompt, 'THIRD PERSON OMNISCIENT') !== false) {
        echo "✅ 'THIRD PERSON OMNISCIENT' found in prompt\n";
    } else {
        echo "❌ 'THIRD PERSON OMNISCIENT' NOT found in prompt\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error calling buildTranslationPrompt: " . $e->getMessage() . "\n";
}

// Test buildNarrativePerspectiveInstructions directly
echo "\n=== Testing buildNarrativePerspectiveInstructions ===\n";

try {
    $method2 = $reflection->getMethod('buildNarrativePerspectiveInstructions');
    $method2->setAccessible(true);
    
    $narrativeInstructions = $method2->invoke($deepSeekService, $testContext['narrative_context'], $testContext);
    
    echo "Narrative perspective instructions:\n";
    echo "================\n";
    echo $narrativeInstructions;
    echo "\n================\n\n";
    
} catch (Exception $e) {
    echo "❌ Error calling buildNarrativePerspectiveInstructions: " . $e->getMessage() . "\n";
}

// Test buildIntelligentPerspectiveInstructions directly
echo "\n=== Testing buildIntelligentPerspectiveInstructions ===\n";

try {
    $method3 = $reflection->getMethod('buildIntelligentPerspectiveInstructions');
    $method3->setAccessible(true);
    
    $intelligentInstructions = $method3->invoke($deepSeekService, $testContext['narrative_context'], $testContext);
    
    echo "Intelligent perspective instructions:\n";
    echo "================\n";
    echo $intelligentInstructions;
    echo "\n================\n\n";
    
} catch (Exception $e) {
    echo "❌ Error calling buildIntelligentPerspectiveInstructions: " . $e->getMessage() . "\n";
}

// Test getThirdPersonOmniscientInstructions directly
echo "\n=== Testing getThirdPersonOmniscientInstructions ===\n";

try {
    $method4 = $reflection->getMethod('getThirdPersonOmniscientInstructions');
    $method4->setAccessible(true);
    
    $omniscientInstructions = $method4->invoke($deepSeekService);
    
    echo "Third person omniscient instructions:\n";
    echo "================\n";
    echo $omniscientInstructions;
    echo "\n================\n\n";
    
} catch (Exception $e) {
    echo "❌ Error calling getThirdPersonOmniscientInstructions: " . $e->getMessage() . "\n";
}

echo "Done.\n";
