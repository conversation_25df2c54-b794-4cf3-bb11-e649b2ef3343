<?php
/**
 * Name Substitution Service
 * Handles post-translation name substitution to ensure exact dictionary matches
 * Novel Translation Application
 */

class NameSubstitutionService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Apply exact name substitutions to translated text using the name dictionary
     */
    public function applyNameSubstitutions(string $translatedText, array $nameDictionary, string $originalText = ''): array {
        $startTime = microtime(true);
        $substitutions = [];
        $processedText = $translatedText;
        
        try {
            // Sort names by length and priority to handle conflicts correctly
            $sortedNames = $this->sortNamesByLength($nameDictionary);

            // Pre-process to detect potential conflicts
            $conflictMap = $this->buildConflictMap($sortedNames, $translatedText);

            foreach ($sortedNames as $nameEntry) {
                $originalName = $nameEntry['original_name'] ?? '';
                $targetName = $this->getTargetName($nameEntry);

                if (empty($originalName) || empty($targetName)) {
                    continue;
                }

                // Skip if original name is not in the source text
                if (!empty($originalText) && mb_strpos($originalText, $originalName) === false) {
                    $this->logNameProcessing($nameEntry, 'SKIPPED', 'Original name not found in source text');
                    continue;
                }

                // Check for conflicts before processing
                if ($this->hasActiveConflicts($originalName, $conflictMap, $processedText)) {
                    // Use conflict resolution to determine if we should proceed
                    if (!$this->shouldProcessWithConflicts($nameEntry, $conflictMap, $processedText)) {
                        $conflicts = $this->getConflictsForName($originalName, $conflictMap);
                        $this->logConflictResolution($originalName, $conflicts, false, 'Lower priority than conflicting names');
                        continue;
                    } else {
                        $conflicts = $this->getConflictsForName($originalName, $conflictMap);
                        $this->logConflictResolution($originalName, $conflicts, true, 'Highest priority among conflicts');
                    }
                }

                // Find and replace AI-interpreted versions of this name
                $replacementResult = $this->findAndReplaceNameVariations(
                    $processedText,
                    $originalName,
                    $targetName
                );

                if ($replacementResult['replaced']) {
                    $processedText = $replacementResult['text'];
                    $substitutions[] = [
                        'original_name' => $originalName,
                        'target_name' => $targetName,
                        'found_variations' => $replacementResult['variations'],
                        'replacement_count' => $replacementResult['count']
                    ];

                    $this->logNameProcessing($nameEntry, 'PROCESSED',
                        "Replaced {$replacementResult['count']} occurrences of variations: " .
                        implode(', ', $replacementResult['variations']));

                    // Update conflict map to reflect changes
                    $this->updateConflictMap($conflictMap, $originalName, $processedText);
                } else {
                    $this->logNameProcessing($nameEntry, 'NO_MATCHES', 'No variations found in translated text');
                }
            }
            
            $executionTime = microtime(true) - $startTime;
            
            return [
                'success' => true,
                'original_text' => $translatedText,
                'processed_text' => $processedText,
                'substitutions' => $substitutions,
                'substitution_count' => count($substitutions),
                'execution_time' => round($executionTime, 4)
            ];
            
        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'original_text' => $translatedText,
                'processed_text' => $translatedText, // Return original on error
                'substitutions' => $substitutions,
                'execution_time' => round($executionTime, 4)
            ];
        }
    }
    
    /**
     * Sort names by length and specificity to handle names that contain other names
     * and prevent incorrect substitutions
     */
    private function sortNamesByLength(array $nameDictionary): array {
        usort($nameDictionary, function($a, $b) {
            $nameA = $a['original_name'] ?? '';
            $nameB = $b['original_name'] ?? '';
            $lengthA = mb_strlen($nameA);
            $lengthB = mb_strlen($nameB);

            // First priority: Length (longest first to prevent substring issues)
            if ($lengthA !== $lengthB) {
                return $lengthB - $lengthA;
            }

            // Second priority: Specificity (character names before general terms)
            $typeA = $a['name_type'] ?? 'other';
            $typeB = $b['name_type'] ?? 'other';
            $priorityA = $this->getNameTypePriority($typeA);
            $priorityB = $this->getNameTypePriority($typeB);

            if ($priorityA !== $priorityB) {
                return $priorityA - $priorityB; // Lower number = higher priority
            }

            // Third priority: Frequency (higher frequency first)
            $freqA = $a['frequency'] ?? 1;
            $freqB = $b['frequency'] ?? 1;

            if ($freqA !== $freqB) {
                return $freqB - $freqA;
            }

            // Fourth priority: Has specific translation (translated names first)
            $hasTranslationA = !empty($a['translation']) ? 1 : 0;
            $hasTranslationB = !empty($b['translation']) ? 1 : 0;

            return $hasTranslationB - $hasTranslationA;
        });

        return $nameDictionary;
    }

    /**
     * Get priority value for name types (lower = higher priority)
     */
    private function getNameTypePriority(string $nameType): int {
        $priorities = [
            'character' => 1,    // Highest priority
            'location' => 2,
            'organization' => 3,
            'skill' => 4,
            'monster' => 5,
            'country' => 6,
            'other' => 7         // Lowest priority
        ];

        return $priorities[$nameType] ?? 7;
    }
    
    /**
     * Get the target name (translation > romanization > original)
     */
    private function getTargetName(array $nameEntry): string {
        $translation = isset($nameEntry['translation']) ? trim($nameEntry['translation']) : '';
        $romanization = isset($nameEntry['romanization']) ? trim($nameEntry['romanization']) : '';
        $original = isset($nameEntry['original_name']) ? trim($nameEntry['original_name']) : '';
        
        if (!empty($translation)) {
            return $translation;
        } elseif (!empty($romanization)) {
            return $romanization;
        } else {
            return $original;
        }
    }
    
    /**
     * Find and replace variations of a name in the translated text
     * Enhanced with conflict detection and validation
     */
    private function findAndReplaceNameVariations(string $text, string $originalName, string $targetName): array {
        $variations = [];
        $replacementCount = 0;
        $processedText = $text;

        // Generate possible variations of how the AI might have translated this name
        $possibleVariations = $this->generateNameVariations($originalName, $targetName, $text);

        // Filter variations to prevent conflicts
        $safeVariations = $this->filterSafeVariations($possibleVariations, $targetName, $text);

        foreach ($safeVariations as $variation) {
            if (empty($variation) || $variation === $targetName) {
                continue; // Skip empty or already correct names
            }

            // Validate that this variation should be replaced
            if (!$this->shouldReplaceVariation($variation, $targetName, $text, $originalName)) {
                continue;
            }

            // Use word boundary matching to avoid partial replacements
            $pattern = $this->createWordBoundaryPattern($variation);

            if (preg_match($pattern, $processedText)) {
                $processedText = preg_replace($pattern, $targetName, $processedText, -1, $count);
                if ($count > 0) {
                    $variations[] = $variation;
                    $replacementCount += $count;
                }
            }
        }

        return [
            'text' => $processedText,
            'replaced' => $replacementCount > 0,
            'variations' => $variations,
            'count' => $replacementCount
        ];
    }

    /**
     * Filter variations to only include safe ones that won't cause conflicts
     */
    private function filterSafeVariations(array $variations, string $targetName, string $context): array {
        $safeVariations = [];

        foreach ($variations as $variation) {
            if (empty($variation) || $variation === $targetName) {
                continue;
            }

            // Skip variations that are too generic or common
            if ($this->isGenericTerm($variation)) {
                continue;
            }

            // Skip variations that might conflict with other names
            if ($this->hasVariationConflict($variation, $context)) {
                continue;
            }

            $safeVariations[] = $variation;
        }

        return $safeVariations;
    }

    /**
     * Check if a variation should be replaced
     */
    private function shouldReplaceVariation(string $variation, string $targetName, string $text, string $originalName): bool {
        // Don't replace if the variation is already the target
        if (strcasecmp($variation, $targetName) === 0) {
            return false;
        }

        // Don't replace if the variation appears to be part of a larger word
        $pattern = $this->createWordBoundaryPattern($variation);
        if (!preg_match($pattern, $text)) {
            return false;
        }

        // Don't replace very short variations unless they're exact matches
        if (mb_strlen($variation) <= 2 && strcasecmp($variation, $originalName) !== 0) {
            return false;
        }

        return true;
    }

    /**
     * Check if a term is too generic to safely replace
     */
    private function isGenericTerm(string $term): bool {
        $genericTerms = [
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'he', 'she', 'it', 'they', 'we', 'you', 'i', 'me', 'him', 'her', 'us', 'them',
            'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
            'will', 'would', 'could', 'should', 'may', 'might', 'can', 'must',
            'this', 'that', 'these', 'those', 'here', 'there', 'where', 'when', 'why', 'how',
            'all', 'any', 'some', 'many', 'much', 'few', 'little', 'more', 'most', 'less', 'least',
            'one', 'two', 'three', 'first', 'second', 'third', 'last', 'next', 'other', 'another'
        ];

        return in_array(strtolower($term), $genericTerms) || mb_strlen($term) <= 1;
    }

    /**
     * Check if a variation might conflict with other content
     */
    private function hasVariationConflict(string $variation, string $context): bool {
        // Check if the variation appears in contexts that suggest it's not a name
        $conflictPatterns = [
            '/\b' . preg_quote($variation, '/') . '\s+(is|are|was|were|will|would|could|should)\b/i',
            '/\b(the|a|an)\s+' . preg_quote($variation, '/') . '\s+(of|in|on|at|for|with)\b/i',
            '/\b' . preg_quote($variation, '/') . '\s+(and|or|but|so|yet|nor)\b/i',
        ];

        foreach ($conflictPatterns as $pattern) {
            if (preg_match($pattern, $context)) {
                return true;
            }
        }

        return false;
    }
    
    /**
     * Generate possible variations of how AI might translate a name
     */
    private function generateNameVariations(string $originalName, string $targetName, string $context): array {
        $variations = [];
        
        // Add the exact target name for direct matching
        $variations[] = $targetName;
        
        // Generate linguistic variations based on the original name structure
        $variations = array_merge($variations, $this->generateLinguisticVariations($originalName, $context));
        
        // Generate capitalization variations
        $variations = array_merge($variations, $this->generateCapitalizationVariations($targetName));
        
        // Generate punctuation variations
        $variations = array_merge($variations, $this->generatePunctuationVariations($targetName));
        
        // Remove duplicates and empty values
        $variations = array_unique(array_filter($variations));
        
        return $variations;
    }
    
    /**
     * Generate linguistic variations based on original name structure
     */
    private function generateLinguisticVariations(string $originalName, string $context): array {
        $variations = [];

        // Handle names with particles like "の" (no/of)
        if (strpos($originalName, 'の') !== false) {
            $parts = explode('の', $originalName);
            if (count($parts) === 2) {
                $part1 = trim($parts[0]);
                $part2 = trim($parts[1]);

                // Generate common English patterns for "X の Y"
                $variations[] = $part2 . " of " . $part1;
                $variations[] = $part1 . "'s " . $part2;
                $variations[] = "the " . $part2 . " of " . $part1;
                $variations[] = "the " . $part1 . "'s " . $part2;
                $variations[] = $part2 . " of the " . $part1;
                $variations[] = $part1 . " " . $part2; // Sometimes particles are omitted

                // Handle pluralization
                $variations[] = $part2 . "s of " . $part1;
                $variations[] = $part2 . "s of the " . $part1;
                $variations[] = "the " . $part2 . "s of " . $part1;

                // Handle specific patterns like "Insect(s)" vs "Insects"
                if (stripos($part2, '(s)') !== false) {
                    $singularPart2 = str_ireplace('(s)', '', $part2);
                    $pluralPart2 = str_ireplace('(s)', 's', $part2);

                    // Add both singular and plural variations
                    $variations[] = $singularPart2 . " of " . $part1;
                    $variations[] = $pluralPart2 . " of " . $part1;
                    $variations[] = "the " . $singularPart2 . " of " . $part1;
                    $variations[] = "the " . $pluralPart2 . " of " . $part1;
                    $variations[] = $singularPart2 . " of the " . $part1;
                    $variations[] = $pluralPart2 . " of the " . $part1;
                }
            }
        }

        // Handle names with quotes or special characters
        if (preg_match('/["""『』「」【】]/u', $originalName)) {
            $cleanName = preg_replace('/["""『』「」【】]/u', '', $originalName);
            $variations[] = $cleanName;
            $variations[] = '"' . $cleanName . '"';
            $variations[] = "'" . $cleanName . "'";
        }

        // Handle compound names (names with multiple parts)
        if (preg_match('/[\s・]/u', $originalName)) {
            $parts = preg_split('/[\s・]/u', $originalName);
            if (count($parts) > 1) {
                // Try different combinations
                $variations[] = implode(' ', $parts);
                $variations[] = implode('', $parts);
                $variations[] = implode('-', $parts);

                // Try with "the" prefix
                $variations[] = "the " . implode(' ', $parts);
            }
        }

        // Handle names with numbers or special suffixes
        if (preg_match('/(\d+|世|級|型|号)$/', $originalName)) {
            // Handle numbered names like "パーラハーラ3世"
            $variations[] = preg_replace('/(\d+)/', ' $1', $originalName);
            $variations[] = preg_replace('/世$/', ' III', $originalName);
            $variations[] = preg_replace('/(\d+)世$/', ' $1', $originalName);
        }

        return $variations;
    }
    
    /**
     * Generate capitalization variations
     */
    private function generateCapitalizationVariations(string $name): array {
        $variations = [];
        
        $variations[] = $name; // Original
        $variations[] = strtolower($name); // All lowercase
        $variations[] = strtoupper($name); // All uppercase
        $variations[] = ucfirst(strtolower($name)); // First letter capitalized
        $variations[] = ucwords(strtolower($name)); // Title case
        
        return $variations;
    }
    
    /**
     * Generate punctuation variations
     */
    private function generatePunctuationVariations(string $name): array {
        $variations = [];
        
        // Handle special (s) patterns for pluralization
        if (stripos($name, '(s)') !== false) {
            $variations[] = str_ireplace('(s)', '', $name); // Singular form
            $variations[] = str_ireplace('(s)', 's', $name); // Plural form
        }

        // Remove parentheses and their contents
        $variations[] = preg_replace('/\s*\([^)]*\)/', '', $name);

        // Remove quotes
        $variations[] = str_replace(['"', "'", '"', '"', "'", "'"], '', $name);

        // Replace different types of quotes
        $variations[] = str_replace(['"', '"'], '"', $name);
        $variations[] = str_replace(["'", "'"], "'", $name);
        
        return $variations;
    }
    
    /**
     * Create a regex pattern with word boundaries for safe replacement
     * Enhanced to handle mixed language content and prevent partial matches
     */
    private function createWordBoundaryPattern(string $variation): string {
        // Escape special regex characters
        $escaped = preg_quote($variation, '/');

        // Enhanced boundary detection for mixed language content
        if ($this->isLatinText($variation)) {
            // For Latin text, use standard word boundaries
            return '/\b' . $escaped . '\b/ui';
        } elseif ($this->isJapaneseText($variation)) {
            // For Japanese text, use custom boundaries
            return '/(?<![ぁ-んァ-ヶ一-龯])' . $escaped . '(?![ぁ-んァ-ヶ一-龯])/u';
        } else {
            // For mixed or other text, use comprehensive boundaries
            return '/(?<![A-Za-z0-9ぁ-んァ-ヶ一-龯])' . $escaped . '(?![A-Za-z0-9ぁ-んァ-ヶ一-龯])/u';
        }
    }

    /**
     * Check if text is primarily Latin characters
     */
    private function isLatinText(string $text): bool {
        return preg_match('/^[A-Za-z\s\-\'\.]+$/', $text);
    }

    /**
     * Check if text contains Japanese characters
     */
    private function isJapaneseText(string $text): bool {
        return preg_match('/[ぁ-んァ-ヶ一-龯]/u', $text);
    }
    
    /**
     * Log substitution details for debugging
     * Enhanced with detailed conflict and decision information
     */
    public function logSubstitutions(array $substitutions, string $context = ''): void {
        if (empty($substitutions)) {
            $this->logDebug("No name substitutions applied" . (!empty($context) ? " in {$context}" : ""));
            return;
        }

        $logMessage = "NameSubstitutionService: Applied " . count($substitutions) . " name substitutions";
        if (!empty($context)) {
            $logMessage .= " in {$context}";
        }
        $logMessage .= "\n";

        foreach ($substitutions as $sub) {
            $logMessage .= "  - {$sub['original_name']} → {$sub['target_name']} ";
            $logMessage .= "(found variations: " . implode(', ', $sub['found_variations']) . ", ";
            $logMessage .= "replacements: {$sub['replacement_count']})\n";
        }

        $this->logDebug($logMessage);
    }

    /**
     * Log detailed debugging information
     */
    private function logDebug(string $message, array $context = []): void {
        $timestamp = date('Y-m-d H:i:s');
        $logEntry = "[{$timestamp}] NameSubstitutionService: {$message}";

        if (!empty($context)) {
            $logEntry .= " | Context: " . json_encode($context, JSON_UNESCAPED_UNICODE);
        }

        $logEntry .= "\n";
        file_put_contents('debug.log', $logEntry, FILE_APPEND);
    }

    /**
     * Log conflict detection and resolution decisions
     */
    private function logConflictResolution(string $originalName, array $conflicts, bool $processed, string $reason = ''): void {
        $conflictNames = array_map(function($c) { return $c['original_name']; }, $conflicts);
        $action = $processed ? 'PROCESSED' : 'SKIPPED';

        $message = "Conflict resolution for '{$originalName}': {$action}";
        if (!empty($conflicts)) {
            $message .= " | Conflicts with: " . implode(', ', array_unique($conflictNames));
        }
        if (!empty($reason)) {
            $message .= " | Reason: {$reason}";
        }

        $this->logDebug($message);
    }

    /**
     * Log similarity calculation details
     */
    private function logSimilarityCalculation(string $candidate, string $target, float $similarity, float $threshold, bool $accepted): void {
        $result = $accepted ? 'ACCEPTED' : 'REJECTED';
        $message = "Similarity check: '{$candidate}' vs '{$target}' = " . number_format($similarity, 3) . " (threshold: " . number_format($threshold, 3) . ") → {$result}";
        $this->logDebug($message);
    }

    /**
     * Log pattern matching attempts
     */
    private function logPatternMatch(string $pattern, string $text, int $matches, string $variation): void {
        $message = "Pattern match: '{$variation}' found {$matches} times in text";
        $this->logDebug($message, ['pattern' => $pattern]);
    }

    /**
     * Log name processing decisions
     */
    private function logNameProcessing(array $nameEntry, string $decision, string $reason = ''): void {
        $originalName = $nameEntry['original_name'] ?? 'unknown';
        $targetName = $this->getTargetName($nameEntry);
        $nameType = $nameEntry['name_type'] ?? 'unknown';
        $frequency = $nameEntry['frequency'] ?? 0;

        $message = "Name processing: '{$originalName}' → '{$targetName}' ({$nameType}, freq: {$frequency}) → {$decision}";
        if (!empty($reason)) {
            $message .= " | Reason: {$reason}";
        }

        $this->logDebug($message);
    }

    /**
     * Advanced fuzzy matching for names that might have been significantly altered by AI
     */
    public function findFuzzyMatches(string $translatedText, array $nameDictionary): array {
        $fuzzyMatches = [];

        foreach ($nameDictionary as $nameEntry) {
            $originalName = $nameEntry['original_name'] ?? '';
            $targetName = $this->getTargetName($nameEntry);

            if (empty($originalName) || empty($targetName)) {
                continue;
            }

            // Extract potential name candidates from the translated text
            $candidates = $this->extractNameCandidates($translatedText, $targetName);

            foreach ($candidates as $candidate) {
                $similarity = $this->calculateNameSimilarity($candidate, $targetName);

                // Use dynamic threshold based on name characteristics
                $threshold = $this->getDynamicSimilarityThreshold($candidate, $targetName, $originalName);

                if ($similarity > $threshold) {
                    $confidence = $this->calculateConfidence($candidate, $targetName, $originalName);

                    // Only include matches with sufficient confidence
                    if ($confidence > 0.6) {
                        $fuzzyMatches[] = [
                            'original_name' => $originalName,
                            'target_name' => $targetName,
                            'found_text' => $candidate,
                            'similarity' => $similarity,
                            'confidence' => $confidence
                        ];
                    }
                }
            }
        }

        // Sort by confidence (highest first)
        usort($fuzzyMatches, function($a, $b) {
            return $b['confidence'] <=> $a['confidence'];
        });

        return $fuzzyMatches;
    }

    /**
     * Extract potential name candidates from translated text
     */
    private function extractNameCandidates(string $text, string $targetName): array {
        $candidates = [];

        // Extract capitalized words and phrases (likely names)
        preg_match_all('/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/', $text, $matches);
        $candidates = array_merge($candidates, $matches[0]);

        // Extract quoted text (often names)
        preg_match_all('/["""\'\'](.*?)["""\'\']/', $text, $matches);
        $candidates = array_merge($candidates, $matches[1]);

        // Extract words similar in length to target name
        $targetLength = mb_strlen($targetName);
        $words = preg_split('/\s+/', $text);
        foreach ($words as $word) {
            $wordLength = mb_strlen(trim($word, '.,!?;:'));
            if (abs($wordLength - $targetLength) <= 3) {
                $candidates[] = trim($word, '.,!?;:');
            }
        }

        return array_unique(array_filter($candidates));
    }

    /**
     * Calculate similarity between two name strings
     * Enhanced to prevent false positives and improve accuracy
     */
    private function calculateNameSimilarity(string $candidate, string $target): float {
        // Normalize both strings
        $candidate = strtolower(trim($candidate));
        $target = strtolower(trim($target));

        if ($candidate === $target) {
            return 1.0;
        }

        // Early exit for very different lengths
        $candidateLength = mb_strlen($candidate);
        $targetLength = mb_strlen($target);
        $lengthDiff = abs($candidateLength - $targetLength);

        // If length difference is too large, similarity is low
        if ($lengthDiff > max($candidateLength, $targetLength) * 0.5) {
            return 0.0;
        }

        // Use multiple similarity metrics
        $levenshteinSimilarity = $this->calculateLevenshteinSimilarity($candidate, $target);
        $jaccardSimilarity = $this->calculateJaccardSimilarity($candidate, $target);
        $substringBonus = $this->calculateSubstringBonus($candidate, $target);

        // Weighted combination of similarities
        $similarity = ($levenshteinSimilarity * 0.5) + ($jaccardSimilarity * 0.3) + ($substringBonus * 0.2);

        // Apply penalties for potential false positives
        $similarity = $this->applyFalsePositivePenalties($candidate, $target, $similarity);

        return max(0.0, min(1.0, $similarity));
    }

    /**
     * Calculate Levenshtein-based similarity
     */
    private function calculateLevenshteinSimilarity(string $candidate, string $target): float {
        $maxLength = max(mb_strlen($candidate), mb_strlen($target));
        if ($maxLength === 0) {
            return 0.0;
        }

        $distance = levenshtein($candidate, $target);
        return 1 - ($distance / $maxLength);
    }

    /**
     * Calculate Jaccard similarity based on character n-grams
     */
    private function calculateJaccardSimilarity(string $candidate, string $target): float {
        $candidateGrams = $this->getNGrams($candidate, 2);
        $targetGrams = $this->getNGrams($target, 2);

        if (empty($candidateGrams) && empty($targetGrams)) {
            return 1.0;
        }

        if (empty($candidateGrams) || empty($targetGrams)) {
            return 0.0;
        }

        $intersection = array_intersect($candidateGrams, $targetGrams);
        $union = array_unique(array_merge($candidateGrams, $targetGrams));

        return count($intersection) / count($union);
    }

    /**
     * Get n-grams from a string
     */
    private function getNGrams(string $text, int $n): array {
        $grams = [];
        $length = mb_strlen($text);

        for ($i = 0; $i <= $length - $n; $i++) {
            $grams[] = mb_substr($text, $i, $n);
        }

        return $grams;
    }

    /**
     * Calculate bonus for substring relationships
     */
    private function calculateSubstringBonus(string $candidate, string $target): float {
        $candidateInTarget = mb_strpos($target, $candidate) !== false;
        $targetInCandidate = mb_strpos($candidate, $target) !== false;

        if ($candidateInTarget || $targetInCandidate) {
            // Bonus based on the ratio of the substring to the full string
            $shorter = min(mb_strlen($candidate), mb_strlen($target));
            $longer = max(mb_strlen($candidate), mb_strlen($target));
            return $shorter / $longer;
        }

        return 0.0;
    }

    /**
     * Apply penalties for potential false positives
     */
    private function applyFalsePositivePenalties(string $candidate, string $target, float $similarity): float {
        // Penalty for very short strings (likely to be false positives)
        if (mb_strlen($candidate) <= 2 || mb_strlen($target) <= 2) {
            $similarity *= 0.5;
        }

        // Penalty for common words that might be confused
        if ($this->isGenericTerm($candidate) || $this->isGenericTerm($target)) {
            $similarity *= 0.3;
        }

        // Penalty for very different character types
        if ($this->hasDifferentCharacterTypes($candidate, $target)) {
            $similarity *= 0.7;
        }

        return $similarity;
    }

    /**
     * Check if strings have very different character types
     */
    private function hasDifferentCharacterTypes(string $candidate, string $target): bool {
        $candidateHasLatin = preg_match('/[A-Za-z]/', $candidate);
        $candidateHasJapanese = preg_match('/[ぁ-んァ-ヶ一-龯]/', $candidate);
        $targetHasLatin = preg_match('/[A-Za-z]/', $target);
        $targetHasJapanese = preg_match('/[ぁ-んァ-ヶ一-龯]/', $target);

        // If one is purely Latin and the other is purely Japanese, they're very different
        return ($candidateHasLatin && !$candidateHasJapanese && $targetHasJapanese && !$targetHasLatin) ||
               ($targetHasLatin && !$targetHasJapanese && $candidateHasJapanese && !$candidateHasLatin);
    }

    /**
     * Get dynamic similarity threshold based on name characteristics
     */
    private function getDynamicSimilarityThreshold(string $candidate, string $target, string $original): float {
        $baseThreshold = 0.75; // Increased from 0.7 for better precision

        // Lower threshold for longer names (they're more unique)
        $avgLength = (mb_strlen($candidate) + mb_strlen($target)) / 2;
        if ($avgLength >= 8) {
            $baseThreshold -= 0.1;
        } elseif ($avgLength <= 3) {
            $baseThreshold += 0.15; // Much higher threshold for short names
        }

        // Lower threshold for character names (they should be more precisely matched)
        if (preg_match('/^[A-Z][a-z]+$/', $candidate) && preg_match('/^[A-Z][a-z]+$/', $target)) {
            $baseThreshold -= 0.05;
        }

        // Higher threshold for generic-looking terms
        if ($this->isGenericTerm($candidate) || $this->isGenericTerm($target)) {
            $baseThreshold += 0.2;
        }

        return min(0.95, max(0.6, $baseThreshold));
    }

    /**
     * Calculate confidence score for a potential match
     * Enhanced with better validation and scoring
     */
    private function calculateConfidence(string $candidate, string $target, string $original): float {
        $similarity = $this->calculateNameSimilarity($candidate, $target);

        // Boost confidence for exact matches
        if (strtolower($candidate) === strtolower($target)) {
            return 1.0;
        }

        $confidence = $similarity;

        // Boost confidence for names that appear to be proper nouns
        if (preg_match('/^[A-Z]/', $candidate)) {
            $confidence += 0.1;
        }

        // Boost confidence for names with similar structure to original
        $structureSimilarity = $this->calculateStructureSimilarity($candidate, $original);
        $confidence += $structureSimilarity * 0.15;

        // Boost confidence for character-like names
        if ($this->looksLikeCharacterName($candidate) && $this->looksLikeCharacterName($target)) {
            $confidence += 0.1;
        }

        // Apply penalties for risky matches
        $confidence = $this->applyConfidencePenalties($candidate, $target, $confidence);

        return min(1.0, max(0.0, $confidence));
    }

    /**
     * Check if a string looks like a character name
     */
    private function looksLikeCharacterName(string $name): bool {
        // Capitalized words, reasonable length, not generic
        return preg_match('/^[A-Z][a-z]{2,}(?:\s+[A-Z][a-z]{2,})*$/', $name) &&
               !$this->isGenericTerm($name) &&
               mb_strlen($name) >= 3 && mb_strlen($name) <= 30;
    }

    /**
     * Apply penalties to confidence for risky matches
     */
    private function applyConfidencePenalties(string $candidate, string $target, float $confidence): float {
        // Penalty for very short names
        if (mb_strlen($candidate) <= 2 || mb_strlen($target) <= 2) {
            $confidence *= 0.6;
        }

        // Penalty for generic terms
        if ($this->isGenericTerm($candidate) || $this->isGenericTerm($target)) {
            $confidence *= 0.4;
        }

        // Penalty for very different lengths
        $lengthRatio = min(mb_strlen($candidate), mb_strlen($target)) /
                      max(mb_strlen($candidate), mb_strlen($target));
        if ($lengthRatio < 0.5) {
            $confidence *= 0.7;
        }

        return $confidence;
    }

    /**
     * Calculate structural similarity between candidate and original name
     */
    private function calculateStructureSimilarity(string $candidate, string $original): float {
        // Check for similar patterns (spaces, hyphens, etc.)
        $candidateSpaces = substr_count($candidate, ' ');
        $originalSpaces = substr_count($original, ' ');

        $candidateHyphens = substr_count($candidate, '-');
        $originalHyphens = substr_count($original, '-');

        $spaceSimilarity = 1 - abs($candidateSpaces - $originalSpaces) / max(1, max($candidateSpaces, $originalSpaces));
        $hyphenSimilarity = 1 - abs($candidateHyphens - $originalHyphens) / max(1, max($candidateHyphens, $originalHyphens));

        return ($spaceSimilarity + $hyphenSimilarity) / 2;
    }

    /**
     * Apply exact substitutions with context preservation
     */
    public function applyExactSubstitutions(string $translatedText, array $nameDictionary, array $options = []): array {
        $preserveGrammar = $options['preserve_grammar'] ?? true;
        $preserveCapitalization = $options['preserve_capitalization'] ?? true;
        $preserveArticles = $options['preserve_articles'] ?? true;

        $substitutions = [];
        $processedText = $translatedText;

        // Sort names by priority (frequency, length, etc.)
        $prioritizedNames = $this->prioritizeNames($nameDictionary);

        foreach ($prioritizedNames as $nameEntry) {
            $originalName = $nameEntry['original_name'] ?? '';
            $targetName = $this->getTargetName($nameEntry);

            if (empty($originalName) || empty($targetName)) {
                continue;
            }

            // Find all occurrences with context
            $occurrences = $this->findNameOccurrencesWithContext($processedText, $originalName, $targetName);

            foreach ($occurrences as $occurrence) {
                $replacement = $this->createContextualReplacement(
                    $occurrence,
                    $targetName,
                    $preserveGrammar,
                    $preserveCapitalization,
                    $preserveArticles
                );

                if ($replacement['text'] !== $occurrence['found_text']) {
                    $processedText = str_replace(
                        $occurrence['full_match'],
                        $replacement['full_text'],
                        $processedText
                    );

                    $substitutions[] = [
                        'original_name' => $originalName,
                        'target_name' => $targetName,
                        'found_text' => $occurrence['found_text'],
                        'replacement_text' => $replacement['text'],
                        'context_before' => $occurrence['context_before'],
                        'context_after' => $occurrence['context_after'],
                        'grammar_preserved' => $replacement['grammar_preserved'],
                        'position' => $occurrence['position']
                    ];
                }
            }
        }

        return [
            'success' => true,
            'original_text' => $translatedText,
            'processed_text' => $processedText,
            'substitutions' => $substitutions,
            'substitution_count' => count($substitutions)
        ];
    }

    /**
     * Prioritize names for substitution (most important first)
     * Enhanced to prevent conflicts between similar names
     */
    private function prioritizeNames(array $nameDictionary): array {
        // First, detect potential conflicts between names
        $conflictGroups = $this->detectNameConflicts($nameDictionary);

        usort($nameDictionary, function($a, $b) use ($conflictGroups) {
            $nameA = $a['original_name'] ?? '';
            $nameB = $b['original_name'] ?? '';

            // Check if these names are in conflict with each other
            $conflictPriorityA = $this->getConflictPriority($nameA, $conflictGroups);
            $conflictPriorityB = $this->getConflictPriority($nameB, $conflictGroups);

            if ($conflictPriorityA !== $conflictPriorityB) {
                return $conflictPriorityA - $conflictPriorityB; // Lower = higher priority
            }

            // Enhanced scoring system
            $scoreA = $this->calculateNameScore($a);
            $scoreB = $this->calculateNameScore($b);

            return $scoreB - $scoreA; // Descending order (higher score = higher priority)
        });

        return $nameDictionary;
    }

    /**
     * Calculate comprehensive score for name prioritization
     */
    private function calculateNameScore(array $nameEntry): int {
        $score = 0;

        // Base frequency score (higher frequency = higher priority)
        $frequency = $nameEntry['frequency'] ?? 1;
        $score += $frequency * 20;

        // Length score (longer names = higher priority to prevent substring conflicts)
        $length = mb_strlen($nameEntry['original_name'] ?? '');
        $score += $length * 5;

        // Translation availability score
        if (!empty($nameEntry['translation'])) {
            $score += 100; // High bonus for having translation
        } elseif (!empty($nameEntry['romanization'])) {
            $score += 50; // Medium bonus for having romanization
        }

        // Name type priority score
        $nameType = $nameEntry['name_type'] ?? 'other';
        $typeScore = [
            'character' => 80,    // Highest priority for character names
            'location' => 60,
            'organization' => 40,
            'skill' => 30,
            'monster' => 20,
            'country' => 10,
            'other' => 0
        ];
        $score += $typeScore[$nameType] ?? 0;

        // Verification bonus
        if (!empty($nameEntry['is_verified'])) {
            $score += 30;
        }

        return $score;
    }

    /**
     * Find name occurrences with surrounding context
     */
    private function findNameOccurrencesWithContext(string $text, string $originalName, string $targetName): array {
        $occurrences = [];
        $variations = $this->generateNameVariations($originalName, $targetName, $text);

        foreach ($variations as $variation) {
            if (empty($variation)) continue;

            $pattern = $this->createContextPattern($variation);
            preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE);

            foreach ($matches[0] as $match) {
                $fullMatch = $match[0];
                $position = $match[1];

                // Extract context
                $contextBefore = mb_substr($text, max(0, $position - 20), 20);
                $contextAfter = mb_substr($text, $position + mb_strlen($fullMatch), 20);

                $occurrences[] = [
                    'found_text' => $variation,
                    'full_match' => $fullMatch,
                    'position' => $position,
                    'context_before' => $contextBefore,
                    'context_after' => $contextAfter
                ];
            }
        }

        // Remove duplicates and sort by position
        $occurrences = $this->removeDuplicateOccurrences($occurrences);
        usort($occurrences, function($a, $b) {
            return $a['position'] - $b['position'];
        });

        return $occurrences;
    }

    /**
     * Create contextual replacement preserving grammar and style
     */
    private function createContextualReplacement(array $occurrence, string $targetName, bool $preserveGrammar, bool $preserveCapitalization, bool $preserveArticles): array {
        $foundText = $occurrence['found_text'];
        $contextBefore = $occurrence['context_before'];
        $contextAfter = $occurrence['context_after'];

        $replacement = $targetName;
        $grammarPreserved = false;

        // Preserve capitalization patterns
        if ($preserveCapitalization) {
            if (ctype_upper($foundText)) {
                $replacement = strtoupper($replacement);
            } elseif (ucfirst(strtolower($foundText)) === $foundText) {
                $replacement = ucfirst(strtolower($replacement));
            }
        }

        // Handle articles and determiners
        if ($preserveArticles) {
            $beforeWords = preg_split('/\s+/', trim($contextBefore));
            $lastWord = strtolower(end($beforeWords));

            if (in_array($lastWord, ['the', 'a', 'an'])) {
                // Adjust article if needed
                if ($lastWord === 'a' && preg_match('/^[aeiou]/i', $replacement)) {
                    $replacement = 'an ' . $replacement;
                    $grammarPreserved = true;
                } elseif ($lastWord === 'an' && !preg_match('/^[aeiou]/i', $replacement)) {
                    $replacement = 'a ' . $replacement;
                    $grammarPreserved = true;
                }
            }
        }

        return [
            'text' => $replacement,
            'full_text' => $occurrence['full_match'], // Will be replaced with context
            'grammar_preserved' => $grammarPreserved
        ];
    }

    /**
     * Create pattern for finding names with context
     * Enhanced to handle mixed language content and prevent false matches
     */
    private function createContextPattern(string $variation): string {
        $escaped = preg_quote($variation, '/');

        // Use the same boundary logic as word boundary pattern
        if ($this->isLatinText($variation)) {
            return '/(?:\b|^)' . $escaped . '(?:\b|$)/ui';
        } elseif ($this->isJapaneseText($variation)) {
            return '/(?:^|(?<![ぁ-んァ-ヶ一-龯]))' . $escaped . '(?:$|(?![ぁ-んァ-ヶ一-龯]))/u';
        } else {
            return '/(?:^|(?<![A-Za-z0-9ぁ-んァ-ヶ一-龯]))' . $escaped . '(?:$|(?![A-Za-z0-9ぁ-んァ-ヶ一-龯]))/u';
        }
    }

    /**
     * Remove duplicate occurrences
     */
    private function removeDuplicateOccurrences(array $occurrences): array {
        $unique = [];
        $seen = [];

        foreach ($occurrences as $occurrence) {
            $key = $occurrence['position'] . '_' . $occurrence['found_text'];
            if (!isset($seen[$key])) {
                $unique[] = $occurrence;
                $seen[$key] = true;
            }
        }

        return $unique;
    }

    /**
     * Detect potential conflicts between names that could interfere with each other
     */
    private function detectNameConflicts(array $nameDictionary): array {
        $conflictGroups = [];
        $processedNames = [];

        foreach ($nameDictionary as $i => $nameEntry) {
            $nameA = $nameEntry['original_name'] ?? '';
            if (empty($nameA) || in_array($nameA, $processedNames)) {
                continue;
            }

            $conflicts = [$nameA];

            // Check for conflicts with other names
            foreach ($nameDictionary as $j => $otherEntry) {
                if ($i === $j) continue;

                $nameB = $otherEntry['original_name'] ?? '';
                if (empty($nameB) || in_array($nameB, $processedNames)) {
                    continue;
                }

                // Check for various types of conflicts
                if ($this->namesHaveConflict($nameA, $nameB)) {
                    $conflicts[] = $nameB;
                }
            }

            if (count($conflicts) > 1) {
                $conflictGroups[] = $conflicts;
                $processedNames = array_merge($processedNames, $conflicts);
            }
        }

        return $conflictGroups;
    }

    /**
     * Check if two names have potential conflicts
     */
    private function namesHaveConflict(string $nameA, string $nameB): bool {
        // Substring conflicts (one name contains another)
        if (mb_strpos($nameA, $nameB) !== false || mb_strpos($nameB, $nameA) !== false) {
            return true;
        }

        // Similar length and high similarity
        $lengthA = mb_strlen($nameA);
        $lengthB = mb_strlen($nameB);
        if (abs($lengthA - $lengthB) <= 2) {
            $similarity = $this->calculateNameSimilarity($nameA, $nameB);
            if ($similarity > 0.8) {
                return true;
            }
        }

        // Common prefix/suffix conflicts
        if ($lengthA >= 3 && $lengthB >= 3) {
            $prefixA = mb_substr($nameA, 0, 3);
            $prefixB = mb_substr($nameB, 0, 3);
            $suffixA = mb_substr($nameA, -3);
            $suffixB = mb_substr($nameB, -3);

            if (($prefixA === $prefixB && $suffixA === $suffixB) ||
                (mb_strlen($nameA) >= 4 && mb_strlen($nameB) >= 4 &&
                 mb_substr($nameA, 0, 4) === mb_substr($nameB, 0, 4))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Get conflict priority for a name (lower = higher priority)
     */
    private function getConflictPriority(string $name, array $conflictGroups): int {
        foreach ($conflictGroups as $groupIndex => $group) {
            if (in_array($name, $group)) {
                // Within a conflict group, longer names get higher priority
                $position = array_search($name, $group);
                $lengths = array_map('mb_strlen', $group);
                arsort($lengths); // Sort by length descending
                $priorityOrder = array_keys($lengths);

                return $groupIndex * 100 + array_search($position, $priorityOrder);
            }
        }

        return 999; // No conflicts, lowest priority number (highest actual priority)
    }

    /**
     * Build a conflict map to track potential naming conflicts
     */
    private function buildConflictMap(array $nameDictionary, string $text): array {
        $conflictMap = [];

        foreach ($nameDictionary as $nameEntry) {
            $originalName = $nameEntry['original_name'] ?? '';
            $targetName = $this->getTargetName($nameEntry);

            if (empty($originalName) || empty($targetName)) {
                continue;
            }

            // Find all potential variations that could conflict
            $variations = $this->generateNameVariations($originalName, $targetName, $text);

            foreach ($variations as $variation) {
                if (empty($variation)) continue;

                $key = strtolower($variation);
                if (!isset($conflictMap[$key])) {
                    $conflictMap[$key] = [];
                }

                $conflictMap[$key][] = [
                    'original_name' => $originalName,
                    'target_name' => $targetName,
                    'variation' => $variation,
                    'priority_score' => $this->calculateNameScore($nameEntry),
                    'name_entry' => $nameEntry
                ];
            }
        }

        // Sort conflicts by priority
        foreach ($conflictMap as $key => $conflicts) {
            if (count($conflicts) > 1) {
                usort($conflictMap[$key], function($a, $b) {
                    return $b['priority_score'] - $a['priority_score'];
                });
            }
        }

        return $conflictMap;
    }

    /**
     * Check if a name has active conflicts in the current text
     */
    private function hasActiveConflicts(string $originalName, array $conflictMap, string $text): bool {
        foreach ($conflictMap as $variation => $conflicts) {
            if (count($conflicts) <= 1) continue;

            // Check if any of the conflicts involve this name
            foreach ($conflicts as $conflict) {
                if ($conflict['original_name'] === $originalName) {
                    // Check if the variation actually appears in the text
                    $pattern = $this->createWordBoundaryPattern($conflict['variation']);
                    if (preg_match($pattern, $text)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * Determine if we should process a name despite conflicts
     */
    private function shouldProcessWithConflicts(array $nameEntry, array $conflictMap, string $text): bool {
        $originalName = $nameEntry['original_name'] ?? '';
        $targetName = $this->getTargetName($nameEntry);
        $nameScore = $this->calculateNameScore($nameEntry);

        foreach ($conflictMap as $variation => $conflicts) {
            if (count($conflicts) <= 1) continue;

            // Find this name in the conflicts
            $thisNameIndex = -1;
            foreach ($conflicts as $index => $conflict) {
                if ($conflict['original_name'] === $originalName) {
                    $thisNameIndex = $index;
                    break;
                }
            }

            if ($thisNameIndex === -1) continue;

            // Check if the variation appears in text
            $pattern = $this->createWordBoundaryPattern($conflicts[$thisNameIndex]['variation']);
            if (!preg_match($pattern, $text)) continue;

            // Only process if this name has the highest priority among conflicts
            if ($thisNameIndex !== 0) {
                return false; // Higher priority conflict exists
            }

            // Additional check: ensure significant priority difference
            if (count($conflicts) > 1) {
                $scoreDifference = $conflicts[0]['priority_score'] - $conflicts[1]['priority_score'];
                if ($scoreDifference < 20) { // Minimum score difference required
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Update conflict map after processing a name
     */
    private function updateConflictMap(array &$conflictMap, string $processedName, string $updatedText): void {
        // Remove conflicts for the processed name since they've been resolved
        foreach ($conflictMap as $variation => &$conflicts) {
            $conflicts = array_filter($conflicts, function($conflict) use ($processedName) {
                return $conflict['original_name'] !== $processedName;
            });

            // Re-index array
            $conflicts = array_values($conflicts);
        }
    }

    /**
     * Get all conflicts for a specific name
     */
    private function getConflictsForName(string $originalName, array $conflictMap): array {
        $nameConflicts = [];

        foreach ($conflictMap as $variation => $conflicts) {
            foreach ($conflicts as $conflict) {
                if ($conflict['original_name'] === $originalName) {
                    $nameConflicts = array_merge($nameConflicts, $conflicts);
                    break;
                }
            }
        }

        // Remove duplicates and the name itself
        $uniqueConflicts = [];
        $seen = [];

        foreach ($nameConflicts as $conflict) {
            $key = $conflict['original_name'];
            if ($key !== $originalName && !isset($seen[$key])) {
                $uniqueConflicts[] = $conflict;
                $seen[$key] = true;
            }
        }

        return $uniqueConflicts;
    }
}
?>
