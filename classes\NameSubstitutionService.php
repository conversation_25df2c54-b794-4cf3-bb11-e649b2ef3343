<?php
/**
 * Name Substitution Service
 * Handles post-translation name substitution to ensure exact dictionary matches
 * Novel Translation Application
 */

class NameSubstitutionService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Apply exact name substitutions to translated text using the name dictionary
     */
    public function applyNameSubstitutions(string $translatedText, array $nameDictionary, string $originalText = ''): array {
        $startTime = microtime(true);
        $substitutions = [];
        $processedText = $translatedText;
        
        try {
            // Sort names by length (longest first) to handle names that contain other names
            $sortedNames = $this->sortNamesByLength($nameDictionary);
            
            foreach ($sortedNames as $nameEntry) {
                $originalName = $nameEntry['original_name'] ?? '';
                $targetName = $this->getTargetName($nameEntry);
                
                if (empty($originalName) || empty($targetName)) {
                    continue;
                }
                
                // Skip if original name is not in the source text
                if (!empty($originalText) && strpos($originalText, $originalName) === false) {
                    continue;
                }
                
                // Find and replace AI-interpreted versions of this name
                $replacementResult = $this->findAndReplaceNameVariations(
                    $processedText, 
                    $originalName, 
                    $targetName
                );
                
                if ($replacementResult['replaced']) {
                    $processedText = $replacementResult['text'];
                    $substitutions[] = [
                        'original_name' => $originalName,
                        'target_name' => $targetName,
                        'found_variations' => $replacementResult['variations'],
                        'replacement_count' => $replacementResult['count']
                    ];
                }
            }
            
            $executionTime = microtime(true) - $startTime;
            
            return [
                'success' => true,
                'original_text' => $translatedText,
                'processed_text' => $processedText,
                'substitutions' => $substitutions,
                'substitution_count' => count($substitutions),
                'execution_time' => round($executionTime, 4)
            ];
            
        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;
            
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'original_text' => $translatedText,
                'processed_text' => $translatedText, // Return original on error
                'substitutions' => $substitutions,
                'execution_time' => round($executionTime, 4)
            ];
        }
    }
    
    /**
     * Sort names by length (longest first) to handle nested names correctly
     */
    private function sortNamesByLength(array $nameDictionary): array {
        usort($nameDictionary, function($a, $b) {
            $lengthA = mb_strlen($a['original_name'] ?? '');
            $lengthB = mb_strlen($b['original_name'] ?? '');
            return $lengthB - $lengthA; // Descending order
        });
        
        return $nameDictionary;
    }
    
    /**
     * Get the target name (translation > romanization > original)
     */
    private function getTargetName(array $nameEntry): string {
        $translation = isset($nameEntry['translation']) ? trim($nameEntry['translation']) : '';
        $romanization = isset($nameEntry['romanization']) ? trim($nameEntry['romanization']) : '';
        $original = isset($nameEntry['original_name']) ? trim($nameEntry['original_name']) : '';
        
        if (!empty($translation)) {
            return $translation;
        } elseif (!empty($romanization)) {
            return $romanization;
        } else {
            return $original;
        }
    }
    
    /**
     * Find and replace variations of a name in the translated text
     */
    private function findAndReplaceNameVariations(string $text, string $originalName, string $targetName): array {
        $variations = [];
        $replacementCount = 0;
        $processedText = $text;
        
        // Generate possible variations of how the AI might have translated this name
        $possibleVariations = $this->generateNameVariations($originalName, $targetName, $text);
        
        foreach ($possibleVariations as $variation) {
            if (empty($variation) || $variation === $targetName) {
                continue; // Skip empty or already correct names
            }
            
            // Use word boundary matching to avoid partial replacements
            $pattern = $this->createWordBoundaryPattern($variation);
            
            if (preg_match($pattern, $processedText)) {
                $processedText = preg_replace($pattern, $targetName, $processedText, -1, $count);
                if ($count > 0) {
                    $variations[] = $variation;
                    $replacementCount += $count;
                }
            }
        }
        
        return [
            'text' => $processedText,
            'replaced' => $replacementCount > 0,
            'variations' => $variations,
            'count' => $replacementCount
        ];
    }
    
    /**
     * Generate possible variations of how AI might translate a name
     */
    private function generateNameVariations(string $originalName, string $targetName, string $context): array {
        $variations = [];
        
        // Add the exact target name for direct matching
        $variations[] = $targetName;
        
        // Generate linguistic variations based on the original name structure
        $variations = array_merge($variations, $this->generateLinguisticVariations($originalName, $context));
        
        // Generate capitalization variations
        $variations = array_merge($variations, $this->generateCapitalizationVariations($targetName));
        
        // Generate punctuation variations
        $variations = array_merge($variations, $this->generatePunctuationVariations($targetName));
        
        // Remove duplicates and empty values
        $variations = array_unique(array_filter($variations));
        
        return $variations;
    }
    
    /**
     * Generate linguistic variations based on original name structure
     */
    private function generateLinguisticVariations(string $originalName, string $context): array {
        $variations = [];

        // Handle names with particles like "の" (no/of)
        if (strpos($originalName, 'の') !== false) {
            $parts = explode('の', $originalName);
            if (count($parts) === 2) {
                $part1 = trim($parts[0]);
                $part2 = trim($parts[1]);

                // Generate common English patterns for "X の Y"
                $variations[] = $part2 . " of " . $part1;
                $variations[] = $part1 . "'s " . $part2;
                $variations[] = "the " . $part2 . " of " . $part1;
                $variations[] = "the " . $part1 . "'s " . $part2;
                $variations[] = $part2 . " of the " . $part1;
                $variations[] = $part1 . " " . $part2; // Sometimes particles are omitted

                // Handle pluralization
                $variations[] = $part2 . "s of " . $part1;
                $variations[] = $part2 . "s of the " . $part1;
                $variations[] = "the " . $part2 . "s of " . $part1;

                // Handle specific patterns like "Insect(s)" vs "Insects"
                if (stripos($part2, '(s)') !== false) {
                    $singularPart2 = str_ireplace('(s)', '', $part2);
                    $pluralPart2 = str_ireplace('(s)', 's', $part2);

                    // Add both singular and plural variations
                    $variations[] = $singularPart2 . " of " . $part1;
                    $variations[] = $pluralPart2 . " of " . $part1;
                    $variations[] = "the " . $singularPart2 . " of " . $part1;
                    $variations[] = "the " . $pluralPart2 . " of " . $part1;
                    $variations[] = $singularPart2 . " of the " . $part1;
                    $variations[] = $pluralPart2 . " of the " . $part1;
                }
            }
        }

        // Handle names with quotes or special characters
        if (preg_match('/["""『』「」【】]/u', $originalName)) {
            $cleanName = preg_replace('/["""『』「」【】]/u', '', $originalName);
            $variations[] = $cleanName;
            $variations[] = '"' . $cleanName . '"';
            $variations[] = "'" . $cleanName . "'";
        }

        // Handle compound names (names with multiple parts)
        if (preg_match('/[\s・]/u', $originalName)) {
            $parts = preg_split('/[\s・]/u', $originalName);
            if (count($parts) > 1) {
                // Try different combinations
                $variations[] = implode(' ', $parts);
                $variations[] = implode('', $parts);
                $variations[] = implode('-', $parts);

                // Try with "the" prefix
                $variations[] = "the " . implode(' ', $parts);
            }
        }

        // Handle names with numbers or special suffixes
        if (preg_match('/(\d+|世|級|型|号)$/', $originalName)) {
            // Handle numbered names like "パーラハーラ3世"
            $variations[] = preg_replace('/(\d+)/', ' $1', $originalName);
            $variations[] = preg_replace('/世$/', ' III', $originalName);
            $variations[] = preg_replace('/(\d+)世$/', ' $1', $originalName);
        }

        return $variations;
    }
    
    /**
     * Generate capitalization variations
     */
    private function generateCapitalizationVariations(string $name): array {
        $variations = [];
        
        $variations[] = $name; // Original
        $variations[] = strtolower($name); // All lowercase
        $variations[] = strtoupper($name); // All uppercase
        $variations[] = ucfirst(strtolower($name)); // First letter capitalized
        $variations[] = ucwords(strtolower($name)); // Title case
        
        return $variations;
    }
    
    /**
     * Generate punctuation variations
     */
    private function generatePunctuationVariations(string $name): array {
        $variations = [];
        
        // Handle special (s) patterns for pluralization
        if (stripos($name, '(s)') !== false) {
            $variations[] = str_ireplace('(s)', '', $name); // Singular form
            $variations[] = str_ireplace('(s)', 's', $name); // Plural form
        }

        // Remove parentheses and their contents
        $variations[] = preg_replace('/\s*\([^)]*\)/', '', $name);

        // Remove quotes
        $variations[] = str_replace(['"', "'", '"', '"', "'", "'"], '', $name);

        // Replace different types of quotes
        $variations[] = str_replace(['"', '"'], '"', $name);
        $variations[] = str_replace(["'", "'"], "'", $name);
        
        return $variations;
    }
    
    /**
     * Create a regex pattern with word boundaries for safe replacement
     */
    private function createWordBoundaryPattern(string $variation): string {
        // Escape special regex characters
        $escaped = preg_quote($variation, '/');
        
        // Use word boundaries, but handle special cases
        if (preg_match('/^[A-Za-z]/', $variation) && preg_match('/[A-Za-z]$/', $variation)) {
            // Standard word boundaries for names that start and end with letters
            return '/\b' . $escaped . '\b/u';
        } else {
            // Lookahead/lookbehind for names with special characters
            return '/(?<!\w)' . $escaped . '(?!\w)/u';
        }
    }
    
    /**
     * Log substitution details for debugging
     */
    public function logSubstitutions(array $substitutions, string $context = ''): void {
        if (empty($substitutions)) {
            return;
        }
        
        $logMessage = "NameSubstitutionService: Applied " . count($substitutions) . " name substitutions";
        if (!empty($context)) {
            $logMessage .= " in {$context}";
        }
        $logMessage .= "\n";
        
        foreach ($substitutions as $sub) {
            $logMessage .= "  - {$sub['original_name']} → {$sub['target_name']} ";
            $logMessage .= "(found variations: " . implode(', ', $sub['found_variations']) . ", ";
            $logMessage .= "replacements: {$sub['replacement_count']})\n";
        }
        
        file_put_contents('debug.log', $logMessage, FILE_APPEND);
    }

    /**
     * Advanced fuzzy matching for names that might have been significantly altered by AI
     */
    public function findFuzzyMatches(string $translatedText, array $nameDictionary): array {
        $fuzzyMatches = [];

        foreach ($nameDictionary as $nameEntry) {
            $originalName = $nameEntry['original_name'] ?? '';
            $targetName = $this->getTargetName($nameEntry);

            if (empty($originalName) || empty($targetName)) {
                continue;
            }

            // Extract potential name candidates from the translated text
            $candidates = $this->extractNameCandidates($translatedText, $targetName);

            foreach ($candidates as $candidate) {
                $similarity = $this->calculateNameSimilarity($candidate, $targetName);

                if ($similarity > 0.7) { // 70% similarity threshold
                    $fuzzyMatches[] = [
                        'original_name' => $originalName,
                        'target_name' => $targetName,
                        'found_text' => $candidate,
                        'similarity' => $similarity,
                        'confidence' => $this->calculateConfidence($candidate, $targetName, $originalName)
                    ];
                }
            }
        }

        // Sort by confidence (highest first)
        usort($fuzzyMatches, function($a, $b) {
            return $b['confidence'] <=> $a['confidence'];
        });

        return $fuzzyMatches;
    }

    /**
     * Extract potential name candidates from translated text
     */
    private function extractNameCandidates(string $text, string $targetName): array {
        $candidates = [];

        // Extract capitalized words and phrases (likely names)
        preg_match_all('/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/', $text, $matches);
        $candidates = array_merge($candidates, $matches[0]);

        // Extract quoted text (often names)
        preg_match_all('/["""\'\'](.*?)["""\'\']/', $text, $matches);
        $candidates = array_merge($candidates, $matches[1]);

        // Extract words similar in length to target name
        $targetLength = mb_strlen($targetName);
        $words = preg_split('/\s+/', $text);
        foreach ($words as $word) {
            $wordLength = mb_strlen(trim($word, '.,!?;:'));
            if (abs($wordLength - $targetLength) <= 3) {
                $candidates[] = trim($word, '.,!?;:');
            }
        }

        return array_unique(array_filter($candidates));
    }

    /**
     * Calculate similarity between two name strings
     */
    private function calculateNameSimilarity(string $candidate, string $target): float {
        // Normalize both strings
        $candidate = strtolower(trim($candidate));
        $target = strtolower(trim($target));

        if ($candidate === $target) {
            return 1.0;
        }

        // Use Levenshtein distance for similarity
        $maxLength = max(strlen($candidate), strlen($target));
        if ($maxLength === 0) {
            return 0.0;
        }

        $distance = levenshtein($candidate, $target);
        $similarity = 1 - ($distance / $maxLength);

        // Boost similarity for partial matches
        if (strpos($candidate, $target) !== false || strpos($target, $candidate) !== false) {
            $similarity = max($similarity, 0.8);
        }

        return max(0.0, $similarity);
    }

    /**
     * Calculate confidence score for a potential match
     */
    private function calculateConfidence(string $candidate, string $target, string $original): float {
        $similarity = $this->calculateNameSimilarity($candidate, $target);

        // Boost confidence for exact matches
        if (strtolower($candidate) === strtolower($target)) {
            return 1.0;
        }

        // Boost confidence for names that appear to be proper nouns
        $isProperNoun = preg_match('/^[A-Z]/', $candidate) ? 0.1 : 0.0;

        // Boost confidence for names with similar structure to original
        $structureSimilarity = $this->calculateStructureSimilarity($candidate, $original);

        return min(1.0, $similarity + $isProperNoun + ($structureSimilarity * 0.2));
    }

    /**
     * Calculate structural similarity between candidate and original name
     */
    private function calculateStructureSimilarity(string $candidate, string $original): float {
        // Check for similar patterns (spaces, hyphens, etc.)
        $candidateSpaces = substr_count($candidate, ' ');
        $originalSpaces = substr_count($original, ' ');

        $candidateHyphens = substr_count($candidate, '-');
        $originalHyphens = substr_count($original, '-');

        $spaceSimilarity = 1 - abs($candidateSpaces - $originalSpaces) / max(1, max($candidateSpaces, $originalSpaces));
        $hyphenSimilarity = 1 - abs($candidateHyphens - $originalHyphens) / max(1, max($candidateHyphens, $originalHyphens));

        return ($spaceSimilarity + $hyphenSimilarity) / 2;
    }

    /**
     * Apply exact substitutions with context preservation
     */
    public function applyExactSubstitutions(string $translatedText, array $nameDictionary, array $options = []): array {
        $preserveGrammar = $options['preserve_grammar'] ?? true;
        $preserveCapitalization = $options['preserve_capitalization'] ?? true;
        $preserveArticles = $options['preserve_articles'] ?? true;

        $substitutions = [];
        $processedText = $translatedText;

        // Sort names by priority (frequency, length, etc.)
        $prioritizedNames = $this->prioritizeNames($nameDictionary);

        foreach ($prioritizedNames as $nameEntry) {
            $originalName = $nameEntry['original_name'] ?? '';
            $targetName = $this->getTargetName($nameEntry);

            if (empty($originalName) || empty($targetName)) {
                continue;
            }

            // Find all occurrences with context
            $occurrences = $this->findNameOccurrencesWithContext($processedText, $originalName, $targetName);

            foreach ($occurrences as $occurrence) {
                $replacement = $this->createContextualReplacement(
                    $occurrence,
                    $targetName,
                    $preserveGrammar,
                    $preserveCapitalization,
                    $preserveArticles
                );

                if ($replacement['text'] !== $occurrence['found_text']) {
                    $processedText = str_replace(
                        $occurrence['full_match'],
                        $replacement['full_text'],
                        $processedText
                    );

                    $substitutions[] = [
                        'original_name' => $originalName,
                        'target_name' => $targetName,
                        'found_text' => $occurrence['found_text'],
                        'replacement_text' => $replacement['text'],
                        'context_before' => $occurrence['context_before'],
                        'context_after' => $occurrence['context_after'],
                        'grammar_preserved' => $replacement['grammar_preserved'],
                        'position' => $occurrence['position']
                    ];
                }
            }
        }

        return [
            'success' => true,
            'original_text' => $translatedText,
            'processed_text' => $processedText,
            'substitutions' => $substitutions,
            'substitution_count' => count($substitutions)
        ];
    }

    /**
     * Prioritize names for substitution (most important first)
     */
    private function prioritizeNames(array $nameDictionary): array {
        usort($nameDictionary, function($a, $b) {
            // Priority factors: frequency, length, has translation
            $scoreA = ($a['frequency'] ?? 1) * 10;
            $scoreA += mb_strlen($a['original_name'] ?? '') * 2;
            $scoreA += !empty($a['translation']) ? 50 : 0;

            $scoreB = ($b['frequency'] ?? 1) * 10;
            $scoreB += mb_strlen($b['original_name'] ?? '') * 2;
            $scoreB += !empty($b['translation']) ? 50 : 0;

            return $scoreB - $scoreA; // Descending order
        });

        return $nameDictionary;
    }

    /**
     * Find name occurrences with surrounding context
     */
    private function findNameOccurrencesWithContext(string $text, string $originalName, string $targetName): array {
        $occurrences = [];
        $variations = $this->generateNameVariations($originalName, $targetName, $text);

        foreach ($variations as $variation) {
            if (empty($variation)) continue;

            $pattern = $this->createContextPattern($variation);
            preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE);

            foreach ($matches[0] as $match) {
                $fullMatch = $match[0];
                $position = $match[1];

                // Extract context
                $contextBefore = mb_substr($text, max(0, $position - 20), 20);
                $contextAfter = mb_substr($text, $position + mb_strlen($fullMatch), 20);

                $occurrences[] = [
                    'found_text' => $variation,
                    'full_match' => $fullMatch,
                    'position' => $position,
                    'context_before' => $contextBefore,
                    'context_after' => $contextAfter
                ];
            }
        }

        // Remove duplicates and sort by position
        $occurrences = $this->removeDuplicateOccurrences($occurrences);
        usort($occurrences, function($a, $b) {
            return $a['position'] - $b['position'];
        });

        return $occurrences;
    }

    /**
     * Create contextual replacement preserving grammar and style
     */
    private function createContextualReplacement(array $occurrence, string $targetName, bool $preserveGrammar, bool $preserveCapitalization, bool $preserveArticles): array {
        $foundText = $occurrence['found_text'];
        $contextBefore = $occurrence['context_before'];
        $contextAfter = $occurrence['context_after'];

        $replacement = $targetName;
        $grammarPreserved = false;

        // Preserve capitalization patterns
        if ($preserveCapitalization) {
            if (ctype_upper($foundText)) {
                $replacement = strtoupper($replacement);
            } elseif (ucfirst(strtolower($foundText)) === $foundText) {
                $replacement = ucfirst(strtolower($replacement));
            }
        }

        // Handle articles and determiners
        if ($preserveArticles) {
            $beforeWords = preg_split('/\s+/', trim($contextBefore));
            $lastWord = strtolower(end($beforeWords));

            if (in_array($lastWord, ['the', 'a', 'an'])) {
                // Adjust article if needed
                if ($lastWord === 'a' && preg_match('/^[aeiou]/i', $replacement)) {
                    $replacement = 'an ' . $replacement;
                    $grammarPreserved = true;
                } elseif ($lastWord === 'an' && !preg_match('/^[aeiou]/i', $replacement)) {
                    $replacement = 'a ' . $replacement;
                    $grammarPreserved = true;
                }
            }
        }

        return [
            'text' => $replacement,
            'full_text' => $occurrence['full_match'], // Will be replaced with context
            'grammar_preserved' => $grammarPreserved
        ];
    }

    /**
     * Create pattern for finding names with context
     */
    private function createContextPattern(string $variation): string {
        $escaped = preg_quote($variation, '/');
        return '/(?:\b|^)' . $escaped . '(?:\b|$)/ui';
    }

    /**
     * Remove duplicate occurrences
     */
    private function removeDuplicateOccurrences(array $occurrences): array {
        $unique = [];
        $seen = [];

        foreach ($occurrences as $occurrence) {
            $key = $occurrence['position'] . '_' . $occurrence['found_text'];
            if (!isset($seen[$key])) {
                $unique[] = $occurrence;
                $seen[$key] = true;
            }
        }

        return $unique;
    }
}
?>
