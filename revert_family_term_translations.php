<?php
/**
 * Revert Family Term Translations Back to Romanized Forms
 * Restores the original intent of using romanization to preserve complete honorific+relationship units
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Reverting Family Term Translations to Romanized Forms ===\n\n";

$db = Database::getInstance();

// Correct romanized translations for family terms (as originally intended)
$correctRomanizations = [
    // Original romanized forms that preserve the complete honorific+relationship unit
    '父さん' => 'Tou-san',
    'お父さん' => 'Otou-san',
    '母さん' => 'Kaa-san', 
    'お母さん' => 'Okaa-san',
    '兄さん' => 'Nii-san',
    'お兄さん' => 'Onii-san',
    'お兄ちゃん' => 'Onii-chan',
    'お姉ちゃん' => 'Onee-chan',
    'お姉さん' => 'Onee-san',
    '姉さん' => 'Nee-chan',
    'おじさん' => '<PERSON>ji<PERSON>',
    'おばさん' => 'Obasan',
    'おじいさん' => 'Ojiisan',
    'おばあさん' => 'Obaasan',
    'おじいちゃん' => 'Ojiichan',
    'おばあちゃん' => 'Obaachan',
    'お父様' => 'Otou-sama',
    'お母様' => 'Okaa-sama',
    'お兄様' => 'Onii-sama',
    'お姉様' => 'Onee-sama'
];

echo "Reverting family terms to romanized forms to preserve complete honorific+relationship units...\n\n";

$revertedCount = 0;
$totalChecked = 0;
$removedCount = 0;

foreach ($correctRomanizations as $originalName => $correctRomanization) {
    // Check if this term exists in the name dictionary
    $existing = $db->fetchOne(
        'SELECT id, original_name, romanization, translation, name_type, frequency 
         FROM name_dictionary 
         WHERE novel_id = 7 AND original_name = ?',
        [$originalName]
    );
    
    if ($existing) {
        $totalChecked++;
        $currentTranslation = $existing['translation'];
        
        echo "Found: {$originalName}\n";
        echo "  Current translation: '{$currentTranslation}'\n";
        echo "  Correct romanization: '{$correctRomanization}'\n";
        
        if ($currentTranslation !== $correctRomanization) {
            // Update the translation back to romanized form
            $updated = $db->update(
                'name_dictionary',
                [
                    'translation' => $correctRomanization,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                'id = ?',
                [$existing['id']]
            );
            
            if ($updated > 0) {
                echo "  ✓ Reverted translation from '{$currentTranslation}' to '{$correctRomanization}'\n";
                $revertedCount++;
            } else {
                echo "  ✗ Failed to revert translation\n";
            }
        } else {
            echo "  ✓ Translation already correct (romanized)\n";
        }
        echo "\n";
    }
}

// Also remove any family terms that were incorrectly added with English translations
$englishFamilyTermsToRemove = [
    '母' // This was added as 'mother' which is not needed
];

echo "=== Removing Incorrectly Added English Family Terms ===\n\n";

foreach ($englishFamilyTermsToRemove as $termToRemove) {
    $existing = $db->fetchOne(
        'SELECT id, original_name, translation 
         FROM name_dictionary 
         WHERE novel_id = 7 AND original_name = ?',
        [$termToRemove]
    );
    
    if ($existing) {
        echo "Removing incorrectly added term: {$termToRemove} → '{$existing['translation']}'\n";
        
        $deleted = $db->delete(
            'name_dictionary',
            'id = ?',
            [$existing['id']]
        );
        
        if ($deleted > 0) {
            echo "  ✓ Removed\n";
            $removedCount++;
        } else {
            echo "  ✗ Failed to remove\n";
        }
    }
}

echo "\n=== Summary ===\n";
echo "Total family terms checked: {$totalChecked}\n";
echo "Translations reverted to romanization: {$revertedCount}\n";
echo "Incorrect entries removed: {$removedCount}\n";

if ($revertedCount > 0 || $removedCount > 0) {
    echo "\n✓ Family term translations have been reverted to romanized forms!\n";
    echo "Family terms now use complete romanized units like:\n";
    echo "- 父さん → 'Tou-san' (preserves complete honorific+relationship)\n";
    echo "- 母さん → 'Kaa-san' (prevents isolated '-san' fragments)\n";
    echo "- お兄ちゃん → 'Onii-chan' (maintains cultural context)\n";
    echo "\nThis preserves the original intent of preventing broken honorific fragments.\n";
} else {
    echo "\n✓ All family term translations were already in correct romanized form.\n";
}

echo "\n=== Revert Complete ===\n";
