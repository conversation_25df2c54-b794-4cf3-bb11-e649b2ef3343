<?php
/**
 * Chapter Splitter Class
 * Handles intelligent splitting of chapters into user-defined parts
 */

class ChapterSplitter {
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }

    /**
     * Split chapter content into user-defined number of parts
     */
    public function splitChapterIntoUserDefinedParts(int $chapterId, string $content, int $targetParts): array {
        try {
            // Validate input
            if ($targetParts < 2 || $targetParts > 10) {
                return [
                    'success' => false,
                    'error' => 'Target parts must be between 2 and 10'
                ];
            }

            if (empty(trim($content))) {
                return [
                    'success' => false,
                    'error' => 'Content is empty'
                ];
            }

            // Clear any existing chunks
            $this->clearExistingChunks($chapterId);

            // Calculate target size per chunk
            $totalLength = mb_strlen($content);
            $targetChunkSize = intval($totalLength / $targetParts);

            // Split content intelligently
            $chunks = $this->intelligentSplit($content, $targetParts, $targetChunkSize);

            if (empty($chunks)) {
                return [
                    'success' => false,
                    'error' => 'Failed to split content into chunks'
                ];
            }

            // Save chunks to database
            $chunksCreated = 0;
            foreach ($chunks as $index => $chunkContent) {
                $chunkNumber = $index + 1;
                $this->saveChunk($chapterId, $chunkNumber, [
                    'content' => $chunkContent,
                    'character_count' => mb_strlen($chunkContent),
                    'word_count' => str_word_count($chunkContent),
                    'type' => $this->determineChunkType($chunkContent)
                ]);
                $chunksCreated++;
            }

            $averageChunkSize = intval($totalLength / $chunksCreated);

            return [
                'success' => true,
                'chunks_created' => $chunksCreated,
                'average_chunk_size' => $averageChunkSize,
                'split_method' => 'user_defined_intelligent'
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Splitting failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Intelligently split content respecting natural boundaries
     */
    private function intelligentSplit(string $content, int $targetParts, int $targetChunkSize): array {
        // First, try paragraph-based splitting
        $paragraphs = $this->splitIntoParagraphs($content);
        
        if (count($paragraphs) >= $targetParts) {
            return $this->distributeContentByParagraphs($paragraphs, $targetParts, $targetChunkSize);
        }

        // If not enough paragraphs, try sentence-based splitting
        $sentences = $this->splitIntoSentences($content);
        
        if (count($sentences) >= $targetParts) {
            return $this->distributeContentBySentences($sentences, $targetParts, $targetChunkSize);
        }

        // Fallback: character-based splitting with word boundaries
        return $this->splitByCharacterWithWordBoundaries($content, $targetParts, $targetChunkSize);
    }

    /**
     * Split content into paragraphs
     */
    private function splitIntoParagraphs(string $content): array {
        // Split by double newlines (paragraph breaks)
        $paragraphs = preg_split('/\n\s*\n/', $content);
        return array_filter(array_map('trim', $paragraphs));
    }

    /**
     * Split content into sentences
     */
    private function splitIntoSentences(string $content): array {
        // Split by sentence endings, considering Japanese and Chinese punctuation
        $sentences = preg_split('/[.!?。！？]+\s*/', $content);
        return array_filter(array_map('trim', $sentences));
    }

    /**
     * Distribute paragraphs across target parts
     */
    private function distributeContentByParagraphs(array $paragraphs, int $targetParts, int $targetChunkSize): array {
        $chunks = [];
        $currentChunk = '';
        $currentSize = 0;
        $targetIndex = 0;
        $paragraphsPerChunk = ceil(count($paragraphs) / $targetParts);

        foreach ($paragraphs as $index => $paragraph) {
            $paragraphSize = mb_strlen($paragraph);

            // Check if we should start a new chunk
            if ($targetIndex < $targetParts - 1 && 
                ($currentSize + $paragraphSize > $targetChunkSize * 1.2 || 
                 ($index + 1) % $paragraphsPerChunk === 0)) {
                
                if (!empty($currentChunk)) {
                    $chunks[] = trim($currentChunk);
                    $currentChunk = '';
                    $currentSize = 0;
                    $targetIndex++;
                }
            }

            $currentChunk .= ($currentChunk ? "\n\n" : '') . $paragraph;
            $currentSize += $paragraphSize;
        }

        // Add the last chunk
        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        return $chunks;
    }

    /**
     * Distribute sentences across target parts
     */
    private function distributeContentBySentences(array $sentences, int $targetParts, int $targetChunkSize): array {
        $chunks = [];
        $currentChunk = '';
        $currentSize = 0;
        $targetIndex = 0;
        $sentencesPerChunk = ceil(count($sentences) / $targetParts);

        foreach ($sentences as $index => $sentence) {
            $sentenceSize = mb_strlen($sentence);

            // Check if we should start a new chunk
            if ($targetIndex < $targetParts - 1 && 
                ($currentSize + $sentenceSize > $targetChunkSize * 1.2 || 
                 ($index + 1) % $sentencesPerChunk === 0)) {
                
                if (!empty($currentChunk)) {
                    $chunks[] = trim($currentChunk);
                    $currentChunk = '';
                    $currentSize = 0;
                    $targetIndex++;
                }
            }

            $currentChunk .= ($currentChunk ? ' ' : '') . $sentence;
            $currentSize += $sentenceSize;
        }

        // Add the last chunk
        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        return $chunks;
    }

    /**
     * Split by character count while respecting word boundaries
     */
    private function splitByCharacterWithWordBoundaries(string $content, int $targetParts, int $targetChunkSize): array {
        $chunks = [];
        $words = preg_split('/\s+/', $content);
        $currentChunk = '';
        $currentSize = 0;

        foreach ($words as $word) {
            $wordSize = mb_strlen($word) + 1; // +1 for space

            if ($currentSize + $wordSize > $targetChunkSize && !empty($currentChunk)) {
                $chunks[] = trim($currentChunk);
                $currentChunk = '';
                $currentSize = 0;
            }

            $currentChunk .= ($currentChunk ? ' ' : '') . $word;
            $currentSize += $wordSize;
        }

        // Add the last chunk
        if (!empty($currentChunk)) {
            $chunks[] = trim($currentChunk);
        }

        return $chunks;
    }

    /**
     * Determine chunk type based on content
     */
    private function determineChunkType(string $content): string {
        // Simple heuristics to determine chunk type
        if (preg_match('/^[「『"\']/u', trim($content))) {
            return 'dialogue';
        }
        
        if (preg_match('/\n\s*\n/', $content)) {
            return 'scene_break';
        }
        
        return 'paragraph';
    }

    /**
     * Save chunk to database
     */
    private function saveChunk(int $chapterId, int $chunkNumber, array $chunkData): void {
        $this->db->insert('chapter_chunks', [
            'chapter_id' => $chapterId,
            'chunk_number' => $chunkNumber,
            'original_content' => $chunkData['content'],
            'character_count' => $chunkData['character_count'],
            'word_count' => $chunkData['word_count'],
            'chunk_type' => $chunkData['type'],
            'translation_status' => 'pending'
        ]);
    }

    /**
     * Clear existing chunks for a chapter
     */
    private function clearExistingChunks(int $chapterId): void {
        $this->db->delete('chapter_chunks', 'chapter_id = ?', [$chapterId]);
    }
}
?>
