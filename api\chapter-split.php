<?php
/**
 * API Endpoint: Chapter Splitting
 * POST /api/chapter-split.php - Split a chapter into smaller chunks
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    jsonResponse(['error' => 'Method not allowed'], 405);
}

try {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required parameters
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        jsonResponse(['error' => 'Valid chapter_number is required'], 400);
    }

    if (!isset($input['split_parts']) || !is_numeric($input['split_parts'])) {
        jsonResponse(['error' => 'Valid split_parts is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];
    $splitParts = (int)$input['split_parts'];

    // Validate split parts range
    if ($splitParts < 2 || $splitParts > 10) {
        jsonResponse(['error' => 'Split parts must be between 2 and 10'], 400);
    }

    // Log the request
    error_log("Chapter Split API: Request for novel {$novelId}, chapter {$chapterNumber}, parts {$splitParts}");

    $db = Database::getInstance();

    // Get chapter data
    $chapter = $db->fetchOne(
        "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
        [$novelId, $chapterNumber]
    );

    if (!$chapter) {
        jsonResponse(['error' => 'Chapter not found'], 404);
    }

    if (empty($chapter['original_content'])) {
        jsonResponse(['error' => 'Chapter has no content to split'], 400);
    }

    // Check if chapter already has chunks
    $existingChunks = $db->fetchAll(
        "SELECT COUNT(*) as count FROM chapter_chunks WHERE chapter_id = ?",
        [$chapter['id']]
    );

    if ($existingChunks[0]['count'] > 0) {
        jsonResponse(['error' => 'Chapter already has chunks. Please clear existing chunks first.'], 400);
    }

    // Initialize the chapter splitter
    require_once '../classes/ChapterSplitter.php';
    $splitter = new ChapterSplitter();

    // Split the chapter
    $result = $splitter->splitChapterIntoUserDefinedParts(
        $chapter['id'],
        $chapter['original_content'],
        $splitParts
    );

    if ($result['success']) {
        // Log successful split
        error_log("Chapter Split API: Successfully split chapter {$chapter['id']} into {$result['chunks_created']} parts");

        jsonResponse([
            'success' => true,
            'message' => "Chapter successfully split into {$result['chunks_created']} parts",
            'data' => [
                'chapter_id' => $chapter['id'],
                'chunks_created' => $result['chunks_created'],
                'average_chunk_size' => $result['average_chunk_size'],
                'split_method' => $result['split_method']
            ]
        ]);
    } else {
        error_log("Chapter Split API: Failed to split chapter {$chapter['id']}: " . $result['error']);
        
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 500);
    }

} catch (Exception $e) {
    error_log("Chapter Split API: Exception - " . $e->getMessage());
    
    jsonResponse([
        'success' => false,
        'error' => 'An error occurred while splitting the chapter: ' . $e->getMessage()
    ], 500);
}
?>
