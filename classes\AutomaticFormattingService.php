<?php
/**
 * Automatic Formatting Service
 * 
 * Provides real-time formatting validation and automatic restoration
 * to ensure all translations preserve line breaks and paragraph structure
 * without requiring manual intervention.
 */

class AutomaticFormattingService {
    
    private $debugMode;
    
    public function __construct() {
        $this->debugMode = defined('DEBUG_MODE') && DEBUG_MODE;
    }
    
    /**
     * Validate and enforce formatting preservation in real-time
     * This is the main method that should be called for every translation
     */
    public function validateAndEnforceFormatting(string $originalText, string $translatedText, array $context = []): array {
        $startTime = microtime(true);
        
        // Analyze original text structure
        $originalStructure = $this->analyzeTextStructure($originalText);
        
        // Analyze translated text structure
        $translatedStructure = $this->analyzeTextStructure($translatedText);
        
        // Check if formatting is preserved
        $validationResult = $this->validateFormattingPreservation($originalStructure, $translatedStructure);
        
        if ($validationResult['is_valid']) {
            // Formatting is correct, return as-is
            return [
                'success' => true,
                'text' => $translatedText,
                'formatting_preserved' => true,
                'validation_details' => $validationResult,
                'execution_time' => microtime(true) - $startTime
            ];
        }
        
        // Formatting is incorrect, attempt automatic restoration
        $this->logFormattingIssue($originalStructure, $translatedStructure, $context);
        
        $restoredText = $this->automaticFormattingRestoration($originalText, $translatedText, $originalStructure, $translatedStructure);
        
        // Validate the restored text
        $restoredStructure = $this->analyzeTextStructure($restoredText);
        $finalValidation = $this->validateFormattingPreservation($originalStructure, $restoredStructure);
        
        return [
            'success' => true,
            'text' => $restoredText,
            'formatting_preserved' => $finalValidation['is_valid'],
            'was_restored' => true,
            'original_validation' => $validationResult,
            'final_validation' => $finalValidation,
            'execution_time' => microtime(true) - $startTime
        ];
    }
    
    /**
     * Analyze the structure of text (line breaks, paragraphs, etc.)
     */
    private function analyzeTextStructure(string $text): array {
        $trimmedText = trim($text);

        // More accurate paragraph counting
        $paragraphs = [];
        if (!empty($trimmedText)) {
            $paragraphs = preg_split('/\n\s*\n/', $trimmedText, -1, PREG_SPLIT_NO_EMPTY);
        }

        return [
            'total_length' => mb_strlen($text),
            'line_breaks' => substr_count($text, "\n"),
            'paragraph_breaks' => substr_count($text, "\n\n"),
            'paragraphs' => count($paragraphs),
            'lines' => count(explode("\n", $text)),
            'starts_with_newline' => str_starts_with($text, "\n"),
            'ends_with_newline' => str_ends_with($text, "\n"),
            'has_triple_breaks' => substr_count($text, "\n\n\n") > 0,
            'empty_lines' => $this->countEmptyLines($text)
        ];
    }
    
    /**
     * Count empty lines in text
     */
    private function countEmptyLines(string $text): int {
        $lines = explode("\n", $text);
        $emptyCount = 0;
        
        foreach ($lines as $line) {
            if (trim($line) === '') {
                $emptyCount++;
            }
        }
        
        return $emptyCount;
    }
    
    /**
     * Validate that formatting is properly preserved
     */
    private function validateFormattingPreservation(array $originalStructure, array $translatedStructure): array {
        $issues = [];
        $severity = 'none';
        
        // Check line breaks preservation
        if ($originalStructure['line_breaks'] !== $translatedStructure['line_breaks']) {
            $issues[] = [
                'type' => 'line_breaks_mismatch',
                'expected' => $originalStructure['line_breaks'],
                'actual' => $translatedStructure['line_breaks'],
                'severity' => 'high'
            ];
            $severity = 'high';
        }
        
        // Check paragraph breaks preservation
        if ($originalStructure['paragraph_breaks'] !== $translatedStructure['paragraph_breaks']) {
            $issues[] = [
                'type' => 'paragraph_breaks_mismatch',
                'expected' => $originalStructure['paragraph_breaks'],
                'actual' => $translatedStructure['paragraph_breaks'],
                'severity' => 'high'
            ];
            $severity = 'high';
        }
        
        // Check paragraph count preservation
        if ($originalStructure['paragraphs'] !== $translatedStructure['paragraphs']) {
            $issues[] = [
                'type' => 'paragraph_count_mismatch',
                'expected' => $originalStructure['paragraphs'],
                'actual' => $translatedStructure['paragraphs'],
                'severity' => 'medium'
            ];
            if ($severity === 'none') $severity = 'medium';
        }
        
        // Check for severe formatting loss (no line breaks when original has many)
        if ($originalStructure['line_breaks'] > 3 && $translatedStructure['line_breaks'] === 0) {
            $issues[] = [
                'type' => 'severe_formatting_loss',
                'description' => 'All line breaks were removed from translation',
                'severity' => 'critical'
            ];
            $severity = 'critical';
        }
        
        // Check for paragraph structure collapse
        if ($originalStructure['paragraphs'] > 2 && $translatedStructure['paragraphs'] === 1) {
            $issues[] = [
                'type' => 'paragraph_structure_collapse',
                'description' => 'Multiple paragraphs were merged into one',
                'severity' => 'critical'
            ];
            $severity = 'critical';
        }
        
        return [
            'is_valid' => empty($issues),
            'severity' => $severity,
            'issues' => $issues,
            'score' => $this->calculateFormattingScore($originalStructure, $translatedStructure)
        ];
    }
    
    /**
     * Calculate a formatting preservation score (0-100)
     */
    private function calculateFormattingScore(array $original, array $translated): int {
        $score = 100;
        
        // Deduct points for line break mismatches
        if ($original['line_breaks'] > 0) {
            $lineBreakAccuracy = min(1, $translated['line_breaks'] / $original['line_breaks']);
            $score -= (1 - $lineBreakAccuracy) * 40;
        }
        
        // Deduct points for paragraph break mismatches
        if ($original['paragraph_breaks'] > 0) {
            $paragraphBreakAccuracy = min(1, $translated['paragraph_breaks'] / $original['paragraph_breaks']);
            $score -= (1 - $paragraphBreakAccuracy) * 30;
        }
        
        // Deduct points for paragraph count mismatches
        if ($original['paragraphs'] > 1) {
            $paragraphCountAccuracy = min(1, $translated['paragraphs'] / $original['paragraphs']);
            $score -= (1 - $paragraphCountAccuracy) * 30;
        }
        
        return max(0, (int)round($score));
    }
    
    /**
     * Log formatting issues for debugging and monitoring
     */
    private function logFormattingIssue(array $originalStructure, array $translatedStructure, array $context): void {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'context' => $context,
            'original_structure' => $originalStructure,
            'translated_structure' => $translatedStructure,
            'severity' => $this->calculateFormattingScore($originalStructure, $translatedStructure) < 70 ? 'high' : 'medium'
        ];
        
        file_put_contents('debug.log', "AutomaticFormattingService: Formatting issue detected: " . json_encode($logEntry) . "\n", FILE_APPEND);
    }
    
    /**
     * Automatically restore formatting based on original text structure
     */
    private function automaticFormattingRestoration(string $originalText, string $translatedText, array $originalStructure, array $translatedStructure): string {
        // If translation has no line breaks but original has many, attempt intelligent restoration
        if ($originalStructure['line_breaks'] > 2 && $translatedStructure['line_breaks'] === 0) {
            return $this->restoreLineBreaksIntelligently($originalText, $translatedText);
        }
        
        // If paragraph structure is collapsed, attempt paragraph restoration
        if ($originalStructure['paragraphs'] > 2 && $translatedStructure['paragraphs'] === 1) {
            return $this->restoreParagraphStructure($originalText, $translatedText);
        }
        
        // If line breaks are partially preserved, attempt fine-tuning
        if ($originalStructure['line_breaks'] > $translatedStructure['line_breaks']) {
            return $this->adjustLineBreakCount($originalText, $translatedText, $originalStructure['line_breaks']);
        }
        
        return $translatedText;
    }
    
    /**
     * Intelligently restore line breaks based on original text patterns
     */
    private function restoreLineBreaksIntelligently(string $originalText, string $translatedText): string {
        $originalLines = explode("\n", $originalText);
        $translatedWords = preg_split('/\s+/', trim($translatedText));
        
        if (empty($translatedWords)) {
            return $translatedText;
        }
        
        $restoredLines = [];
        $wordIndex = 0;
        $totalWords = count($translatedWords);
        
        foreach ($originalLines as $lineIndex => $originalLine) {
            if (trim($originalLine) === '') {
                // Empty line in original - preserve it
                $restoredLines[] = '';
                continue;
            }
            
            // Estimate how many words this line should have based on original length
            $originalWordCount = count(preg_split('/\s+/', trim($originalLine)));
            $wordsForThisLine = min($originalWordCount + 2, $totalWords - $wordIndex); // Allow some flexibility
            
            if ($wordsForThisLine > 0 && $wordIndex < $totalWords) {
                $lineWords = array_slice($translatedWords, $wordIndex, $wordsForThisLine);
                $restoredLines[] = implode(' ', $lineWords);
                $wordIndex += $wordsForThisLine;
            } else {
                $restoredLines[] = '';
            }
        }
        
        // Add any remaining words to the last non-empty line
        if ($wordIndex < $totalWords) {
            $remainingWords = array_slice($translatedWords, $wordIndex);
            $lastLineIndex = count($restoredLines) - 1;
            while ($lastLineIndex >= 0 && trim($restoredLines[$lastLineIndex]) === '') {
                $lastLineIndex--;
            }
            if ($lastLineIndex >= 0) {
                $restoredLines[$lastLineIndex] .= ' ' . implode(' ', $remainingWords);
            }
        }
        
        return implode("\n", $restoredLines);
    }
    
    /**
     * Restore paragraph structure by splitting text appropriately
     */
    private function restoreParagraphStructure(string $originalText, string $translatedText): string {
        $originalParagraphs = preg_split('/\n\s*\n/', trim($originalText), -1, PREG_SPLIT_NO_EMPTY);
        $translatedText = trim($translatedText);

        if (count($originalParagraphs) <= 1) {
            return $translatedText;
        }

        // Calculate relative lengths of original paragraphs
        $originalLengths = [];
        $totalOriginalLength = 0;
        foreach ($originalParagraphs as $paragraph) {
            $length = mb_strlen(trim($paragraph));
            $originalLengths[] = $length;
            $totalOriginalLength += $length;
        }

        // Split translated text proportionally
        $translatedLength = mb_strlen($translatedText);
        $restoredParagraphs = [];
        $currentPosition = 0;

        for ($i = 0; $i < count($originalParagraphs); $i++) {
            if ($i === count($originalParagraphs) - 1) {
                // Last paragraph gets all remaining text
                $paragraphText = mb_substr($translatedText, $currentPosition);
            } else {
                // Calculate proportional length for this paragraph
                $proportion = $originalLengths[$i] / $totalOriginalLength;
                $targetLength = (int)($translatedLength * $proportion);

                // Find a good break point near the target length
                $paragraphText = mb_substr($translatedText, $currentPosition, $targetLength);

                // Try to break at sentence boundaries
                $lastSentenceEnd = max(
                    mb_strrpos($paragraphText, '.'),
                    mb_strrpos($paragraphText, '!'),
                    mb_strrpos($paragraphText, '?')
                );

                if ($lastSentenceEnd !== false && $lastSentenceEnd > $targetLength * 0.7) {
                    $paragraphText = mb_substr($paragraphText, 0, $lastSentenceEnd + 1);
                } else {
                    // Break at word boundary
                    $lastSpace = mb_strrpos($paragraphText, ' ');
                    if ($lastSpace !== false && $lastSpace > $targetLength * 0.8) {
                        $paragraphText = mb_substr($paragraphText, 0, $lastSpace);
                    }
                }

                $currentPosition += mb_strlen($paragraphText);
            }

            $restoredParagraphs[] = trim($paragraphText);
        }

        return implode("\n\n", array_filter($restoredParagraphs));
    }
    
    /**
     * Split text by length to match paragraph count
     */
    private function splitTextByLength(string $text, int $targetParagraphs): string {
        $words = preg_split('/\s+/', trim($text));
        $wordsPerParagraph = ceil(count($words) / $targetParagraphs);
        
        $paragraphs = [];
        for ($i = 0; $i < $targetParagraphs; $i++) {
            $startIndex = $i * $wordsPerParagraph;
            $endIndex = min(($i + 1) * $wordsPerParagraph, count($words));
            
            if ($startIndex < count($words)) {
                $paragraphWords = array_slice($words, $startIndex, $endIndex - $startIndex);
                $paragraphs[] = implode(' ', $paragraphWords);
            }
        }
        
        return implode("\n\n", $paragraphs);
    }
    
    /**
     * Adjust line break count to match original
     */
    private function adjustLineBreakCount(string $originalText, string $translatedText, int $targetLineBreaks): string {
        $currentLineBreaks = substr_count($translatedText, "\n");
        
        if ($currentLineBreaks >= $targetLineBreaks) {
            return $translatedText;
        }
        
        $neededBreaks = $targetLineBreaks - $currentLineBreaks;
        $lines = explode("\n", $translatedText);
        
        // Add line breaks by splitting longer lines
        for ($i = 0; $i < $neededBreaks && count($lines) > 1; $i++) {
            // Find the longest line to split
            $longestLineIndex = 0;
            $longestLineLength = 0;
            
            foreach ($lines as $index => $line) {
                if (mb_strlen($line) > $longestLineLength) {
                    $longestLineLength = mb_strlen($line);
                    $longestLineIndex = $index;
                }
            }
            
            // Split the longest line in half
            if ($longestLineLength > 20) {
                $line = $lines[$longestLineIndex];
                $midPoint = mb_strlen($line) / 2;
                $spacePos = mb_strpos($line, ' ', $midPoint);
                
                if ($spacePos !== false) {
                    $firstPart = mb_substr($line, 0, $spacePos);
                    $secondPart = mb_substr($line, $spacePos + 1);
                    
                    $lines[$longestLineIndex] = $firstPart;
                    array_splice($lines, $longestLineIndex + 1, 0, $secondPart);
                }
            }
        }
        
        return implode("\n", $lines);
    }
    
    /**
     * Check if translation should be retried due to severe formatting loss
     */
    public function shouldRetryTranslation(array $validationResult): bool {
        // Retry if formatting score is very low or critical issues exist
        if ($validationResult['score'] < 50) {
            return true;
        }

        // Retry if there are critical formatting issues
        foreach ($validationResult['issues'] as $issue) {
            if ($issue['severity'] === 'critical') {
                return true;
            }
        }

        return false;
    }

    /**
     * Generate enhanced formatting prompt for retry attempts
     */
    public function generateRetryPromptAddition(array $originalStructure, array $translatedStructure): string {
        $prompt = "\n\n🚨 CRITICAL FORMATTING CORRECTION REQUIRED 🚨\n";
        $prompt .= "The previous translation had severe formatting issues. You MUST fix these:\n\n";

        if ($originalStructure['line_breaks'] !== $translatedStructure['line_breaks']) {
            $prompt .= "❌ LINE BREAKS: Original has {$originalStructure['line_breaks']} line breaks, but translation had {$translatedStructure['line_breaks']}\n";
            $prompt .= "✅ REQUIRED: Your translation MUST have exactly {$originalStructure['line_breaks']} line breaks (\\n)\n\n";
        }

        if ($originalStructure['paragraph_breaks'] !== $translatedStructure['paragraph_breaks']) {
            $prompt .= "❌ PARAGRAPH BREAKS: Original has {$originalStructure['paragraph_breaks']} paragraph breaks, but translation had {$translatedStructure['paragraph_breaks']}\n";
            $prompt .= "✅ REQUIRED: Your translation MUST have exactly {$originalStructure['paragraph_breaks']} paragraph breaks (\\n\\n)\n\n";
        }

        if ($originalStructure['paragraphs'] !== $translatedStructure['paragraphs']) {
            $prompt .= "❌ PARAGRAPH COUNT: Original has {$originalStructure['paragraphs']} paragraphs, but translation had {$translatedStructure['paragraphs']}\n";
            $prompt .= "✅ REQUIRED: Your translation MUST have exactly {$originalStructure['paragraphs']} paragraphs\n\n";
        }

        $prompt .= "⚠️ FORMATTING VERIFICATION: Count the line breaks and paragraphs in your response before submitting.\n";
        $prompt .= "Your response will be automatically rejected if formatting is incorrect.\n\n";

        return $prompt;
    }

    /**
     * Check if automatic formatting enforcement is enabled
     */
    public function isEnabled(): bool {
        return true; // Always enabled for permanent solution
    }
    
    /**
     * Get formatting statistics for monitoring
     */
    public function getFormattingStatistics(string $originalText, string $translatedText): array {
        $originalStructure = $this->analyzeTextStructure($originalText);
        $translatedStructure = $this->analyzeTextStructure($translatedText);
        $validation = $this->validateFormattingPreservation($originalStructure, $translatedStructure);
        
        return [
            'original_structure' => $originalStructure,
            'translated_structure' => $translatedStructure,
            'validation' => $validation,
            'preservation_score' => $validation['score']
        ];
    }
}
