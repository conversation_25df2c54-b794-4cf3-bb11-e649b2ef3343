<?php
/**
 * Test Gemini 2.5 specifically for family term translation
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing Gemini 2.5 Family Term Translation ===\n\n";

// Test text with family terms
$testTexts = [
    "「父さん、お疲れ様です」と言った。",
    "母さんは微笑んだ。",
    "お父さんはどこですか？",
    "お母さんが呼んでいます。",
    "兄さんと一緒に行きます。"
];

echo "Testing Gemini 2.5 specifically...\n\n";

try {
    $providerManager = new AIProviderManager();
    
    // Test with Gemini 2.5 only
    $providerManager->setActiveProvider('gemini_25');
    $translationService = $providerManager->getTranslationService('gemini_25');
    
    echo "Using Gemini 2.5: " . $translationService->getVersion() . "\n";
    
    // Prepare context with name dictionary for novel 7
    $db = Database::getInstance();
    $names = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         AND original_name IN (?, ?, ?, ?, ?, ?, ?, ?)
         ORDER BY frequency DESC',
        ['父さん', 'お父さん', '父', '母さん', 'お母さん', '母', '兄さん', 'お兄ちゃん']
    );
    
    $context = [
        'type' => 'test',
        'novel_id' => 7,
        'names' => $names
    ];
    
    echo "Using name dictionary with " . count($names) . " family term entries\n\n";
    
    foreach ($testTexts as $i => $testText) {
        echo "Test " . ($i + 1) . ":\n";
        echo "Original: {$testText}\n";
        
        $result = $translationService->translateText(
            $testText,
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            $translation = $result['translated_text'];
            echo "Translation: {$translation}\n";
            
            // Check for problematic English family terms
            $problematicTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
            $foundProblems = [];
            
            foreach ($problematicTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundProblems[] = $term;
                }
            }
            
            if (empty($foundProblems)) {
                echo "✅ PASS: No English family terms found\n";
            } else {
                echo "❌ FAIL: Found English family terms: " . implode(', ', $foundProblems) . "\n";
            }
            
            // Check for expected romanized terms
            $expectedTerms = ['Tou-san', 'Kaa-san', 'Otou-san', 'Okaa-san', 'Nii-san'];
            $foundExpected = [];
            
            foreach ($expectedTerms as $term) {
                if (stripos($translation, $term) !== false) {
                    $foundExpected[] = $term;
                }
            }
            
            if (!empty($foundExpected)) {
                echo "✅ GOOD: Found romanized terms: " . implode(', ', $foundExpected) . "\n";
            } else {
                echo "⚠️ WARNING: No expected romanized terms found\n";
            }
            
        } else {
            echo "❌ Translation failed: " . $result['error'] . "\n";
        }
        
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
}

echo "=== Gemini 2.5 Test Complete ===\n";
