<?php
/**
 * WordPress Integration Service
 * Handles posting novels and chapters to WordPress via REST API
 */

class WordPressService {
    private $db;
    private $siteUrl;
    private $username;
    private $appPassword;
    private $novelPostType;
    private $chapterPostType;
    private $novelCustomPostType;
    private $chapterCustomPostType;
    private $useCustomPostTypes;
    private $defaultCategory;
    private $autoPublish;
    private $includeOriginalTitle;
    private $availablePostTypes;
    private $currentProfile;

    // Connection management
    private $curlHandle;
    private $lastConnectionTest;
    private $connectionTestInterval = 300; // 5 minutes
    private $maxRetries = 3;
    private $retryDelay = 2; // seconds
    private $connectionMonitor;

    public function __construct($profileId = null) {
        $this->db = Database::getInstance();
        if ($profileId) {
            $this->loadProfileConfiguration($profileId);
        } else {
            $this->loadConfiguration();
        }

        // Initialize connection management
        $this->lastConnectionTest = 0;
        $this->curlHandle = null;
        $this->connectionMonitor = new WordPressConnectionMonitor();
    }

    public function __destruct() {
        $this->closeCurlHandle();
    }

    /**
     * Close cURL handle if open
     */
    private function closeCurlHandle() {
        if ($this->curlHandle) {
            curl_close($this->curlHandle);
            $this->curlHandle = null;
        }
    }
    
    /**
     * Load WordPress configuration from user preferences (legacy method)
     */
    private function loadConfiguration(): void {
        $preferences = $this->db->fetchAll(
            "SELECT preference_key, preference_value FROM user_preferences
             WHERE preference_key LIKE 'wordpress_%'"
        );

        foreach ($preferences as $pref) {
            switch ($pref['preference_key']) {
                case 'wordpress_site_url':
                    $this->siteUrl = rtrim($pref['preference_value'], '/');
                    break;
                case 'wordpress_username':
                    $this->username = $pref['preference_value'];
                    break;
                case 'wordpress_app_password':
                    $this->appPassword = $pref['preference_value'];
                    break;
                case 'wordpress_novel_post_type':
                    $this->novelPostType = $pref['preference_value'];
                    break;
                case 'wordpress_chapter_post_type':
                    $this->chapterPostType = $pref['preference_value'];
                    break;
                case 'wordpress_novel_custom_post_type':
                    $this->novelCustomPostType = $pref['preference_value'];
                    break;
                case 'wordpress_chapter_custom_post_type':
                    $this->chapterCustomPostType = $pref['preference_value'];
                    break;
                case 'wordpress_use_custom_post_types':
                    $this->useCustomPostTypes = $pref['preference_value'] === 'true';
                    break;
                case 'wordpress_default_category':
                    $this->defaultCategory = $pref['preference_value'];
                    break;
                case 'wordpress_auto_publish':
                    $this->autoPublish = $pref['preference_value'] === 'true';
                    break;
                case 'wordpress_include_original_title':
                    $this->includeOriginalTitle = $pref['preference_value'] === 'true';
                    break;
            }
        }
    }

    /**
     * Public method to load a profile configuration
     */
    public function loadProfile(int $profileId): void {
        $this->loadProfileConfiguration($profileId);
    }

    /**
     * Load WordPress configuration from a specific profile
     */
    private function loadProfileConfiguration(int $profileId): void {
        $profile = $this->db->fetchOne(
            "SELECT * FROM wordpress_profiles WHERE id = ? AND is_active = 1",
            [$profileId]
        );

        if (!$profile) {
            throw new Exception("WordPress profile not found or inactive: $profileId");
        }

        $this->currentProfile = $profile;
        $this->siteUrl = rtrim($profile['site_url'], '/');
        $this->username = $profile['username'];
        $this->appPassword = $profile['app_password'];
        $this->novelPostType = $profile['novel_post_type'];
        $this->chapterPostType = $profile['chapter_post_type'];
        $this->novelCustomPostType = $profile['novel_custom_post_type'];
        $this->chapterCustomPostType = $profile['chapter_custom_post_type'];
        $this->useCustomPostTypes = (bool)$profile['use_custom_post_types'];
        $this->defaultCategory = $profile['default_category'];
        $this->autoPublish = (bool)$profile['auto_publish'];
        $this->includeOriginalTitle = (bool)$profile['include_original_title'];
    }

    /**
     * Test connection with a specific profile configuration
     */
    public function testConnectionWithProfile(array $profile): array {
        $originalConfig = [
            'siteUrl' => $this->siteUrl,
            'username' => $this->username,
            'appPassword' => $this->appPassword,
            'currentProfile' => $this->currentProfile
        ];

        // Temporarily load profile configuration
        $this->currentProfile = $profile;
        $this->siteUrl = rtrim($profile['site_url'], '/');
        $this->username = $profile['username'];
        $this->appPassword = $profile['app_password'];

        // Test connection
        $result = $this->testConnection();

        // Restore original configuration
        $this->siteUrl = $originalConfig['siteUrl'];
        $this->username = $originalConfig['username'];
        $this->appPassword = $originalConfig['appPassword'];
        $this->currentProfile = $originalConfig['currentProfile'];

        return $result;
    }
    
    /**
     * Get available post types from WordPress
     */
    public function getAvailablePostTypes(): array {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'error' => 'WordPress not configured'
            ];
        }

        try {
            $response = $this->makeRequestWithRetry('GET', '/wp-json/wp/v2/types');

            if ($response['success']) {
                $postTypes = [];
                foreach ($response['data'] as $slug => $typeData) {
                    $postTypes[$slug] = [
                        'name' => $typeData['name'] ?? $slug,
                        'slug' => $slug,
                        'description' => $typeData['description'] ?? '',
                        'public' => $typeData['public'] ?? false,
                        'hierarchical' => $typeData['hierarchical'] ?? false
                    ];
                }

                $this->availablePostTypes = $postTypes;

                return [
                    'success' => true,
                    'post_types' => $postTypes
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to fetch post types: ' . $response['error']
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Error fetching post types: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Fetch available categories from WordPress
     */
    public function fetchAvailableCategories(): array {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'error' => 'WordPress not configured'
            ];
        }

        try {
            $response = $this->makeRequestWithRetry('GET', '/wp-json/wp/v2/categories?per_page=100&orderby=name&order=asc');

            if ($response['success']) {
                $categories = [];
                foreach ($response['data'] as $categoryData) {
                    $categories[] = [
                        'id' => $categoryData['id'],
                        'name' => $categoryData['name'],
                        'slug' => $categoryData['slug'],
                        'description' => $categoryData['description'] ?? '',
                        'count' => $categoryData['count'] ?? 0,
                        'parent' => $categoryData['parent'] ?? 0
                    ];
                }

                return [
                    'success' => true,
                    'categories' => $categories
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to fetch categories: ' . $response['error']
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Error fetching categories: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate categories exist in WordPress
     */
    private function validateCategories(array $categoryIds): array {
        if (empty($categoryIds)) {
            return ['success' => true, 'categories' => []];
        }

        try {
            // Ensure all values are integers and remove empty values
            $cleanCategoryIds = array_map('intval', array_filter($categoryIds, function($id) {
                return !empty($id) && is_numeric($id);
            }));

            if (empty($cleanCategoryIds)) {
                return ['success' => true, 'categories' => []];
            }

            // Fetch available categories to validate against
            $categoriesResult = $this->fetchAvailableCategories();
            if (!$categoriesResult['success']) {
                return [
                    'success' => false,
                    'error' => 'Could not fetch categories for validation: ' . $categoriesResult['error']
                ];
            }

            $availableCategoryIds = array_column($categoriesResult['categories'], 'id');
            $invalidCategories = array_diff($cleanCategoryIds, $availableCategoryIds);

            if (!empty($invalidCategories)) {
                return [
                    'success' => false,
                    'error' => 'Invalid category IDs: ' . implode(', ', $invalidCategories)
                ];
            }

            return [
                'success' => true,
                'categories' => $cleanCategoryIds
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Category validation error: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Validate custom post types exist in WordPress
     */
    public function validateCustomPostTypes(): array {
        $postTypesResult = $this->getAvailablePostTypes();

        if (!$postTypesResult['success']) {
            return $postTypesResult;
        }

        $availableTypes = array_keys($postTypesResult['post_types']);
        $errors = [];

        // Check novel post type
        $novelPostType = $this->getEffectiveNovelPostType();
        if (!in_array($novelPostType, $availableTypes)) {
            $errors[] = "Novel post type '{$novelPostType}' not found in WordPress";
        }

        // Check chapter post type
        $chapterPostType = $this->getEffectiveChapterPostType();
        if (!in_array($chapterPostType, $availableTypes)) {
            $errors[] = "Chapter post type '{$chapterPostType}' not found in WordPress";
        }

        if (!empty($errors)) {
            return [
                'success' => false,
                'errors' => $errors,
                'available_types' => $availableTypes
            ];
        }

        return [
            'success' => true,
            'novel_post_type' => $novelPostType,
            'chapter_post_type' => $chapterPostType,
            'available_types' => $availableTypes
        ];
    }

    /**
     * Get the effective post type for novels (custom or default)
     */
    public function getEffectiveNovelPostType(): string {
        if ($this->useCustomPostTypes && !empty($this->novelCustomPostType)) {
            return $this->novelCustomPostType;
        }
        return $this->novelPostType ?: 'page';
    }

    /**
     * Get the effective post type for chapters (custom or default)
     */
    public function getEffectiveChapterPostType(): string {
        if ($this->useCustomPostTypes && !empty($this->chapterCustomPostType)) {
            return $this->chapterCustomPostType;
        }
        return $this->chapterPostType ?: 'post';
    }

    /**
     * Test WordPress connection with enhanced error handling
     */
    public function testConnection(): array {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'error' => 'WordPress not configured. Please set up WordPress settings first.'
            ];
        }

        $profileName = $this->currentProfile['profile_name'] ?? 'default';

        try {
            // Force a fresh connection test
            $this->lastConnectionTest = 0;
            $response = $this->makeRequestWithRetry('GET', '/wp-json/wp/v2/users/me');

            if ($response['success']) {
                $this->lastConnectionTest = time();

                // Log successful connection
                $this->connectionMonitor->logConnectionAttempt($profileName, $this->siteUrl, true);

                return [
                    'success' => true,
                    'message' => 'WordPress connection successful',
                    'user_info' => $response['data']
                ];
            } else {
                // Log failed connection
                $this->connectionMonitor->logConnectionAttempt($profileName, $this->siteUrl, false, $response['error']);

                return [
                    'success' => false,
                    'error' => 'Authentication failed: ' . $response['error']
                ];
            }
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            error_log("WordPress: Connection test failed: " . $errorMessage);

            // Log connection exception
            $this->connectionMonitor->logConnectionAttempt($profileName, $this->siteUrl, false, $errorMessage);

            return [
                'success' => false,
                'error' => 'Connection failed: ' . $errorMessage
            ];
        }
    }

    /**
     * Validate connection before making requests
     */
    private function validateConnection(): bool {
        $currentTime = time();

        // If we haven't tested connection recently, test it now
        if (($currentTime - $this->lastConnectionTest) > $this->connectionTestInterval) {
            $testResult = $this->testConnection();
            return $testResult['success'];
        }

        return true; // Assume connection is good if recently tested
    }
    
    /**
     * Check if WordPress is properly configured
     */
    public function isConfigured(): bool {
        return !empty($this->siteUrl) && 
               !empty($this->username) && 
               !empty($this->appPassword);
    }
    
    /**
     * Post a novel to WordPress with enhanced error handling
     */
    public function postNovel(int $novelId, int $profileId = null, string $customTitle = null, array $categories = null): array {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'error' => 'WordPress not configured'
            ];
        }

        // Validate connection before proceeding
        if (!$this->validateConnection()) {
            return [
                'success' => false,
                'error' => 'WordPress connection validation failed'
            ];
        }

        // Get novel data
        $novel = $this->db->fetchOne(
            "SELECT * FROM novels WHERE id = ?",
            [$novelId]
        );

        if (!$novel) {
            return [
                'success' => false,
                'error' => 'Novel not found'
            ];
        }

        // Determine which profile to use
        $currentProfileId = $profileId ?? ($this->currentProfile['id'] ?? null);

        if ($currentProfileId) {
            // Check if novel already posted with this profile
            $existingPost = $this->db->fetchOne(
                "SELECT * FROM wordpress_posts WHERE novel_id = ? AND post_type = 'novel' AND profile_id = ?",
                [$novelId, $currentProfileId]
            );
        } else {
            // Legacy check by domain for backward compatibility
            $currentDomain = parse_url($this->siteUrl, PHP_URL_HOST);
            $existingPost = $this->db->fetchOne(
                "SELECT * FROM wordpress_posts WHERE novel_id = ? AND post_type = 'novel' AND wordpress_domain = ?",
                [$novelId, $currentDomain]
            );
        }

        if ($existingPost) {
            $profileName = $this->currentProfile['profile_name'] ?? 'current domain';
            return [
                'success' => false,
                'error' => "Novel already posted to WordPress profile: $profileName",
                'wordpress_url' => $existingPost['wordpress_url'],
                'profile_id' => $currentProfileId
            ];
        }
        
        // Prepare post data
        // Use custom title if provided, otherwise use default title generation logic
        if (!empty($customTitle)) {
            $title = $customTitle;
        } else {
            $title = $this->includeOriginalTitle && $novel['translated_title']
                ? $novel['translated_title'] . ' (' . $novel['original_title'] . ')'
                : ($novel['translated_title'] ?: $novel['original_title']);
        }

        $content = $this->formatNovelContent($novel);
        
        $effectivePostType = $this->getEffectiveNovelPostType();

        $postData = [
            'title' => $title,
            'content' => $content,
            'status' => $this->autoPublish ? 'publish' : 'draft'
        ];
        
        // Add categories if specified
        $categoriesToUse = $categories ?? (!empty($this->defaultCategory) ? [$this->defaultCategory] : []);
        if (!empty($categoriesToUse)) {
            // Validate and prepare categories
            $validatedCategories = $this->validateCategories($categoriesToUse);
            if ($validatedCategories['success']) {
                $postData['categories'] = $validatedCategories['categories'];
            } else {
                return [
                    'success' => false,
                    'error' => 'Category validation failed: ' . $validatedCategories['error']
                ];
            }
        }
        
        try {
            // Determine the correct endpoint based on post type
            $endpoint = $this->getPostTypeEndpoint($effectivePostType);
            $response = $this->makeRequestWithRetry('POST', $endpoint, $postData);

            if ($response['success']) {
                $wordpressPostId = $response['data']['id'];
                $wordpressUrl = $response['data']['link'];

                // Prepare database insert data
                $insertData = [
                    'novel_id' => $novelId,
                    'wordpress_post_id' => $wordpressPostId,
                    'post_type' => 'novel',
                    'wordpress_url' => $wordpressUrl,
                    'post_status' => $this->autoPublish ? 'published' : 'draft'
                ];

                // Add profile_id if using profiles, otherwise add domain for backward compatibility
                if ($currentProfileId) {
                    $insertData['profile_id'] = $currentProfileId;
                    $insertData['wordpress_domain'] = parse_url($this->siteUrl, PHP_URL_HOST);
                } else {
                    $insertData['wordpress_domain'] = parse_url($this->siteUrl, PHP_URL_HOST);
                }

                // Save to database with error handling
                try {
                    $this->db->insert('wordpress_posts', $insertData);
                } catch (Exception $dbException) {
                    error_log("WordPress: Failed to store novel posting record: " . $dbException->getMessage());
                    // Continue anyway since the post was successful
                }

                // Log successful posting
                $profileName = $this->currentProfile['profile_name'] ?? 'default';
                $this->connectionMonitor->logPostingAttempt(
                    $profileName,
                    'novel',
                    $novelId,
                    true,
                    '',
                    ['site_url' => $this->siteUrl, 'wordpress_post_id' => $wordpressPostId]
                );

                return [
                    'success' => true,
                    'message' => 'Novel posted to WordPress successfully',
                    'wordpress_post_id' => $wordpressPostId,
                    'wordpress_url' => $wordpressUrl,
                    'profile_id' => $currentProfileId
                ];
            } else {
                // Log failed posting
                $profileName = $this->currentProfile['profile_name'] ?? 'default';
                $this->connectionMonitor->logPostingAttempt(
                    $profileName,
                    'novel',
                    $novelId,
                    false,
                    $response['error'],
                    ['site_url' => $this->siteUrl]
                );

                return [
                    'success' => false,
                    'error' => 'Failed to post novel: ' . $response['error']
                ];
            }
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            error_log("WordPress: Novel posting exception: " . $errorMessage);

            // Log posting exception
            $profileName = $this->currentProfile['profile_name'] ?? 'default';
            $this->connectionMonitor->logPostingAttempt(
                $profileName,
                'novel',
                $novelId,
                false,
                $errorMessage,
                ['site_url' => $this->siteUrl]
            );

            return [
                'success' => false,
                'error' => 'Error posting novel: ' . $errorMessage
            ];
        }
    }
    
    /**
     * Post a chapter to WordPress with enhanced error handling
     */
    public function postChapter(int $chapterId, int $profileId = null, string $customTitle = null, array $categories = null): array {
        if (!$this->isConfigured()) {
            return [
                'success' => false,
                'error' => 'WordPress not configured'
            ];
        }

        // Validate connection before proceeding
        if (!$this->validateConnection()) {
            return [
                'success' => false,
                'error' => 'WordPress connection validation failed'
            ];
        }
        
        // Get chapter and novel data
        $chapter = $this->db->fetchOne(
            "SELECT c.*, n.original_title, n.translated_title, n.author 
             FROM chapters c 
             JOIN novels n ON c.novel_id = n.id 
             WHERE c.id = ?",
            [$chapterId]
        );
        
        if (!$chapter) {
            return [
                'success' => false,
                'error' => 'Chapter not found'
            ];
        }
        
        if (empty($chapter['translated_content'])) {
            return [
                'success' => false,
                'error' => 'Chapter has no translated content'
            ];
        }
        
        // Determine which profile to use
        $currentProfileId = $profileId ?? ($this->currentProfile['id'] ?? null);

        if ($currentProfileId) {
            // Check if chapter already posted with this profile
            $existingPost = $this->db->fetchOne(
                "SELECT * FROM wordpress_posts WHERE chapter_id = ? AND profile_id = ?",
                [$chapterId, $currentProfileId]
            );
        } else {
            // Legacy check by domain for backward compatibility
            $currentDomain = parse_url($this->siteUrl, PHP_URL_HOST);
            $existingPost = $this->db->fetchOne(
                "SELECT * FROM wordpress_posts WHERE chapter_id = ? AND wordpress_domain = ?",
                [$chapterId, $currentDomain]
            );
        }

        if ($existingPost) {
            $profileName = $this->currentProfile['profile_name'] ?? 'current domain';
            return [
                'success' => false,
                'error' => "Chapter already posted to WordPress profile: $profileName",
                'wordpress_url' => $existingPost['wordpress_url'],
                'profile_id' => $currentProfileId
            ];
        }

        // Ensure novel is posted first with the same profile
        if ($currentProfileId) {
            $novelPost = $this->db->fetchOne(
                "SELECT * FROM wordpress_posts WHERE novel_id = ? AND post_type = 'novel' AND profile_id = ?",
                [$chapter['novel_id'], $currentProfileId]
            );
        } else {
            $currentDomain = parse_url($this->siteUrl, PHP_URL_HOST);
            $novelPost = $this->db->fetchOne(
                "SELECT * FROM wordpress_posts WHERE novel_id = ? AND post_type = 'novel' AND wordpress_domain = ?",
                [$chapter['novel_id'], $currentDomain]
            );
        }

        if (!$novelPost) {
            $novelResult = $this->postNovel($chapter['novel_id'], $currentProfileId);
            if (!$novelResult['success']) {
                return [
                    'success' => false,
                    'error' => 'Failed to post novel first: ' . $novelResult['error']
                ];
            }

            // Refresh novel post data
            if ($currentProfileId) {
                $novelPost = $this->db->fetchOne(
                    "SELECT * FROM wordpress_posts WHERE novel_id = ? AND post_type = 'novel' AND profile_id = ?",
                    [$chapter['novel_id'], $currentProfileId]
                );
            } else {
                $novelPost = $this->db->fetchOne(
                    "SELECT * FROM wordpress_posts WHERE novel_id = ? AND post_type = 'novel' AND wordpress_domain = ?",
                    [$chapter['novel_id'], $currentDomain]
                );
            }
        }
        
        // Prepare chapter post data
        // Use custom title if provided, otherwise use default title generation logic
        if (!empty($customTitle)) {
            $chapterTitle = $customTitle;
        } else {
            $novelTitle = $this->includeOriginalTitle && $chapter['translated_title']
                ? $chapter['translated_title'] . ' (' . $chapter['original_title'] . ')'
                : ($chapter['translated_title'] ?: $chapter['original_title']);

            $chapterTitle = $this->formatChapterTitle($chapter, $novelTitle);
        }
        $content = $this->formatChapterContent($chapter);

        $effectiveNovelPostType = $this->getEffectiveNovelPostType();
        $effectiveChapterPostType = $this->getEffectiveChapterPostType();

        $postData = [
            'title' => $chapterTitle,
            'content' => $content,
            'status' => $this->autoPublish ? 'publish' : 'draft'
        ];

        // Set parent if novel post type supports hierarchy and chapter post type supports parents
        if ($this->shouldSetParentRelationship($effectiveNovelPostType, $effectiveChapterPostType)) {
            $postData['parent'] = $novelPost['wordpress_post_id'];
        }
        
        // Add categories if specified
        $categoriesToUse = $categories ?? (!empty($this->defaultCategory) ? [$this->defaultCategory] : []);
        if (!empty($categoriesToUse)) {
            // Validate and prepare categories
            $validatedCategories = $this->validateCategories($categoriesToUse);
            if ($validatedCategories['success']) {
                $postData['categories'] = $validatedCategories['categories'];
            } else {
                return [
                    'success' => false,
                    'error' => 'Category validation failed: ' . $validatedCategories['error']
                ];
            }
        }
        
        try {
            // Determine the correct endpoint based on post type
            $endpoint = $this->getPostTypeEndpoint($effectiveChapterPostType);
            $response = $this->makeRequestWithRetry('POST', $endpoint, $postData);

            if ($response['success']) {
                $wordpressPostId = $response['data']['id'];
                $wordpressUrl = $response['data']['link'];

                // Prepare database insert data
                $insertData = [
                    'novel_id' => $chapter['novel_id'],
                    'chapter_id' => $chapterId,
                    'wordpress_post_id' => $wordpressPostId,
                    'post_type' => 'chapter',
                    'wordpress_url' => $wordpressUrl,
                    'post_status' => $this->autoPublish ? 'published' : 'draft'
                ];

                // Add profile_id if using profiles, otherwise add domain for backward compatibility
                if ($currentProfileId) {
                    $insertData['profile_id'] = $currentProfileId;
                    $insertData['wordpress_domain'] = parse_url($this->siteUrl, PHP_URL_HOST);
                } else {
                    $insertData['wordpress_domain'] = parse_url($this->siteUrl, PHP_URL_HOST);
                }

                // Save to database with error handling
                try {
                    $this->db->insert('wordpress_posts', $insertData);
                } catch (Exception $dbException) {
                    error_log("WordPress: Failed to store chapter posting record: " . $dbException->getMessage());
                    // Continue anyway since the post was successful
                }

                // Log successful chapter posting
                $profileName = $this->currentProfile['profile_name'] ?? 'default';
                $this->connectionMonitor->logPostingAttempt(
                    $profileName,
                    'chapter',
                    $chapterId,
                    true,
                    '',
                    ['site_url' => $this->siteUrl, 'wordpress_post_id' => $wordpressPostId, 'novel_id' => $chapter['novel_id']]
                );

                return [
                    'success' => true,
                    'message' => 'Chapter posted to WordPress successfully',
                    'wordpress_post_id' => $wordpressPostId,
                    'wordpress_url' => $wordpressUrl,
                    'profile_id' => $currentProfileId
                ];
            } else {
                // Log failed chapter posting
                $profileName = $this->currentProfile['profile_name'] ?? 'default';
                $this->connectionMonitor->logPostingAttempt(
                    $profileName,
                    'chapter',
                    $chapterId,
                    false,
                    $response['error'],
                    ['site_url' => $this->siteUrl, 'novel_id' => $chapter['novel_id']]
                );

                return [
                    'success' => false,
                    'error' => 'Failed to post chapter: ' . $response['error']
                ];
            }
        } catch (Exception $e) {
            $errorMessage = $e->getMessage();
            error_log("WordPress: Chapter posting exception: " . $errorMessage);

            // Log chapter posting exception
            $profileName = $this->currentProfile['profile_name'] ?? 'default';
            $this->connectionMonitor->logPostingAttempt(
                $profileName,
                'chapter',
                $chapterId,
                false,
                $errorMessage,
                ['site_url' => $this->siteUrl, 'novel_id' => $chapter['novel_id'] ?? null]
            );

            return [
                'success' => false,
                'error' => 'Error posting chapter: ' . $errorMessage
            ];
        }
    }

    /**
     * Get WordPress posting status for a novel
     */
    public function getNovelPostingStatus(int $novelId): array {
        $currentDomain = parse_url($this->siteUrl, PHP_URL_HOST);

        $novelPost = $this->db->fetchOne(
            "SELECT * FROM wordpress_posts WHERE novel_id = ? AND post_type = 'novel' AND wordpress_domain = ?",
            [$novelId, $currentDomain]
        );

        $chapterPosts = $this->db->fetchAll(
            "SELECT wp.*, c.chapter_number
             FROM wordpress_posts wp
             JOIN chapters c ON wp.chapter_id = c.id
             WHERE wp.novel_id = ? AND wp.post_type = 'chapter' AND wp.wordpress_domain = ?
             ORDER BY c.chapter_number",
            [$novelId, $currentDomain]
        );

        // Get all domains this novel has been posted to
        $allDomains = $this->db->fetchAll(
            "SELECT DISTINCT wordpress_domain, COUNT(*) as post_count
             FROM wordpress_posts
             WHERE novel_id = ?
             GROUP BY wordpress_domain",
            [$novelId]
        );

        return [
            'novel_posted' => $novelPost !== null,
            'novel_post' => $novelPost,
            'chapters_posted' => count($chapterPosts),
            'chapter_posts' => $chapterPosts,
            'current_domain' => $currentDomain,
            'all_domains' => $allDomains
        ];
    }

    /**
     * Get WordPress posting status for a chapter
     */
    public function getChapterPostingStatus(int $chapterId): array {
        $currentDomain = parse_url($this->siteUrl, PHP_URL_HOST);

        $chapterPost = $this->db->fetchOne(
            "SELECT * FROM wordpress_posts WHERE chapter_id = ? AND wordpress_domain = ?",
            [$chapterId, $currentDomain]
        );

        // Get all domains this chapter has been posted to
        $allDomains = $this->db->fetchAll(
            "SELECT wordpress_domain, wordpress_url, post_status
             FROM wordpress_posts
             WHERE chapter_id = ?",
            [$chapterId]
        );

        return [
            'posted' => $chapterPost !== null,
            'post_data' => $chapterPost,
            'current_domain' => $currentDomain,
            'all_domains' => $allDomains
        ];
    }

    /**
     * Format novel content for WordPress
     */
    private function formatNovelContent(array $novel): string {
        $content = '';

        // Add novel information
        if (!empty($novel['author'])) {
            $content .= '<p><strong>Author:</strong> ' . htmlspecialchars($novel['author']) . '</p>';
        }

        if (!empty($novel['publication_date'])) {
            $content .= '<p><strong>Publication Date:</strong> ' . htmlspecialchars($novel['publication_date']) . '</p>';
        }

        if (!empty($novel['platform'])) {
            $content .= '<p><strong>Source Platform:</strong> ' . htmlspecialchars(ucfirst($novel['platform'])) . '</p>';
        }

        if (!empty($novel['url'])) {
            $content .= '<p><strong>Original URL:</strong> <a href="' . htmlspecialchars($novel['url']) . '" target="_blank">' . htmlspecialchars($novel['url']) . '</a></p>';
        }

        // Add synopsis
        if (!empty($novel['translated_synopsis'])) {
            $content .= '<h3>Synopsis</h3>';
            $content .= '<div>' . $this->formatTextContent($novel['translated_synopsis']) . '</div>';
        } elseif (!empty($novel['original_synopsis'])) {
            $content .= '<h3>Synopsis (Original)</h3>';
            $content .= '<div>' . $this->formatTextContent($novel['original_synopsis']) . '</div>';
        }

        // Add chapter list link
        $content .= '<h3>Chapters</h3>';
        $content .= '<p>Translated chapters will be posted as separate posts on this website.</p>';

        return $content;
    }

    /**
     * Format chapter title for WordPress
     */
    private function formatChapterTitle(array $chapter, string $novelTitle): string {
        $chapterTitle = '';

        // Add novel title prefix
        $chapterTitle .= $novelTitle . ' - ';

        // Add chapter number
        $chapterTitle .= 'Chapter ' . $chapter['chapter_number'];

        // Add chapter title if available
        if (!empty($chapter['translated_title'])) {
            $chapterTitle .= ': ' . $chapter['translated_title'];
            if ($this->includeOriginalTitle && !empty($chapter['original_title'])) {
                $chapterTitle .= ' (' . $chapter['original_title'] . ')';
            }
        } elseif (!empty($chapter['original_title'])) {
            $chapterTitle .= ': ' . $chapter['original_title'];
        }

        return $chapterTitle;
    }

    /**
     * Format chapter content for WordPress
     */
    private function formatChapterContent(array $chapter): string {
        $content = '';

        // Add chapter navigation info
        $content .= '<div class="chapter-info">';
        $content .= '<p><strong>Chapter ' . $chapter['chapter_number'] . '</strong></p>';

        if (!empty($chapter['translated_title'])) {
            $content .= '<h2>' . htmlspecialchars($chapter['translated_title']) . '</h2>';
            if ($this->includeOriginalTitle && !empty($chapter['original_title'])) {
                $content .= '<p><em>Original: ' . htmlspecialchars($chapter['original_title']) . '</em></p>';
            }
        } elseif (!empty($chapter['original_title'])) {
            $content .= '<h2>' . htmlspecialchars($chapter['original_title']) . '</h2>';
        }
        $content .= '</div>';

        // Add main content
        $content .= '<div class="chapter-content">';
        $content .= $this->formatTextContent($chapter['translated_content']);
        $content .= '</div>';

        // Note: Word count is intentionally excluded from WordPress posts
        // It remains visible in the translation interface but is not posted to WordPress

        return $content;
    }

    /**
     * Format text content for WordPress (handle furigana, paragraphs, HTML formatting, etc.)
     */
    private function formatTextContent(string $text): string {
        // Convert line breaks to paragraphs
        $text = str_replace(["\r\n", "\r", "\n"], "\n", $text);
        $paragraphs = explode("\n\n", $text);

        $formatted = '';
        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);
            if (!empty($paragraph)) {
                // Check if paragraph already contains HTML formatting
                if ($this->containsHtmlTags($paragraph)) {
                    // Content has HTML formatting - preserve it but handle furigana
                    $paragraph = $this->convertFuriganaForWordPress($paragraph);

                    // If it's not already wrapped in a paragraph tag, wrap it
                    if (!preg_match('/^\s*<p\b/i', $paragraph)) {
                        $formatted .= '<p>' . $paragraph . '</p>';
                    } else {
                        $formatted .= $paragraph;
                    }
                } else {
                    // Plain text content - handle furigana and escape HTML
                    $paragraph = $this->convertFuriganaForWordPress($paragraph);
                    $formatted .= '<p>' . htmlspecialchars($paragraph) . '</p>';
                }
            }
        }

        return $formatted;
    }

    /**
     * Check if text contains HTML tags (excluding furigana markup)
     */
    private function containsHtmlTags(string $text): bool {
        // Look for HTML tags but exclude furigana markup {kanji|furigana}
        $htmlTagPattern = '/<[^>]+>/';
        $furiganaPattern = '/\{[^|]+\|[^}]+\}/';

        // Remove furigana markup first, then check for HTML tags
        $textWithoutFurigana = preg_replace($furiganaPattern, '', $text);
        return preg_match($htmlTagPattern, $textWithoutFurigana);
    }

    /**
     * Convert furigana markup for WordPress display
     */
    private function convertFuriganaForWordPress(string $text): string {
        // Convert furigana markup to HTML ruby tags
        // Pattern: {kanji|furigana} -> <ruby>kanji<rt>furigana</rt></ruby>
        $pattern = '/\{([^|]+)\|([^}]+)\}/';
        $replacement = '<ruby>$1<rt>$2</rt></ruby>';

        return preg_replace($pattern, $replacement, $text);
    }

    /**
     * Get the correct REST API endpoint for a post type
     */
    private function getPostTypeEndpoint(string $postType): string {
        // Standard post types have specific endpoints
        switch ($postType) {
            case 'post':
                return '/wp-json/wp/v2/posts';
            case 'page':
                return '/wp-json/wp/v2/pages';
            default:
                // Custom post types use a generic endpoint pattern
                return "/wp-json/wp/v2/{$postType}";
        }
    }

    /**
     * Determine if parent relationship should be set between post types
     */
    private function shouldSetParentRelationship(string $novelPostType, string $chapterPostType): bool {
        // Only set parent relationship for hierarchical post types
        // Pages are hierarchical, posts are not
        // For custom post types, we'll try to set it and let WordPress handle validation

        if ($novelPostType === 'page' && $chapterPostType === 'page') {
            return true; // Page-to-page hierarchy works
        }

        if ($novelPostType === 'post' && $chapterPostType === 'post') {
            return false; // Posts don't support hierarchy
        }

        // For custom post types, we'll attempt to set parent relationship
        // WordPress will ignore it if the post type doesn't support hierarchy
        if ($novelPostType !== 'post' && $chapterPostType !== 'post') {
            return true;
        }

        return false;
    }

    /**
     * Make HTTP request to WordPress REST API with retry logic
     */
    private function makeRequestWithRetry(string $method, string $endpoint, array $data = []): array {
        $lastException = null;

        for ($attempt = 1; $attempt <= $this->maxRetries; $attempt++) {
            try {
                $result = $this->makeRequest($method, $endpoint, $data);

                // If successful, return immediately
                if ($result['success']) {
                    if ($attempt > 1) {
                        error_log("WordPress: Request succeeded on attempt $attempt");
                    }
                    return $result;
                }

                // If it's a client error (4xx), don't retry
                if (isset($result['response_data']) &&
                    is_array($result['response_data']) &&
                    isset($result['response_data']['code']) &&
                    strpos($result['response_data']['code'], '4') === 0) {
                    return $result;
                }

                // For server errors (5xx) or other issues, retry
                if ($attempt < $this->maxRetries) {
                    error_log("WordPress: Request failed on attempt $attempt, retrying: " . $result['error']);
                    sleep($this->retryDelay * $attempt); // Exponential backoff
                }

                $lastException = new Exception($result['error']);

            } catch (Exception $e) {
                $lastException = $e;

                if ($attempt < $this->maxRetries) {
                    error_log("WordPress: Request exception on attempt $attempt, retrying: " . $e->getMessage());
                    sleep($this->retryDelay * $attempt);
                } else {
                    error_log("WordPress: All retry attempts failed: " . $e->getMessage());
                }
            }
        }

        // All attempts failed
        throw $lastException ?: new Exception('All retry attempts failed');
    }

    /**
     * Make HTTP request to WordPress REST API
     */
    private function makeRequest(string $method, string $endpoint, array $data = []): array {
        $url = $this->siteUrl . $endpoint;

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_CONNECTTIMEOUT => 10,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Basic ' . base64_encode($this->username . ':' . $this->appPassword),
                'Connection: close' // Prevent connection reuse issues
            ],
            CURLOPT_SSL_VERIFYPEER => false, // For development - should be true in production
            CURLOPT_USERAGENT => 'Novel Translator WordPress Integration',
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_MAXREDIRS => 3,
            CURLOPT_FRESH_CONNECT => true, // Force new connection
            CURLOPT_FORBID_REUSE => true   // Don't reuse connection
        ]);

        if ($method === 'POST' || $method === 'PUT') {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }

        $startTime = microtime(true);
        $response = curl_exec($ch);
        $duration = microtime(true) - $startTime;

        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        $info = curl_getinfo($ch);
        curl_close($ch);

        // Log request details for debugging
        error_log(sprintf(
            "WordPress API Request: %s %s - HTTP %d - %.2fs - %s",
            $method,
            $endpoint,
            $httpCode,
            $duration,
            $error ?: 'OK'
        ));

        if ($error) {
            throw new Exception('cURL error: ' . $error . ' (HTTP Code: ' . $httpCode . ')');
        }

        $responseData = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("WordPress: Invalid JSON response: " . json_last_error_msg() . " - Response: " . substr($response, 0, 500));
            throw new Exception('Invalid JSON response from WordPress: ' . json_last_error_msg());
        }

        if ($httpCode >= 200 && $httpCode < 300) {
            return [
                'success' => true,
                'data' => $responseData
            ];
        } else {
            $errorMessage = 'HTTP ' . $httpCode;
            if (isset($responseData['message'])) {
                $errorMessage .= ': ' . $responseData['message'];
            }

            // Log detailed error information
            error_log("WordPress API Error: $errorMessage - URL: $url - Response: " . substr($response, 0, 500));

            return [
                'success' => false,
                'error' => $errorMessage,
                'response_data' => $responseData,
                'http_code' => $httpCode
            ];
        }
    }
}
