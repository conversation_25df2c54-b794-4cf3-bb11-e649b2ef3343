<?php
/**
 * Check Family Term Priority in Name Dictionary
 * See if family terms are in the top 20 most frequent names
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Checking Family Term Priority in Name Dictionary ===\n\n";

$db = Database::getInstance();

// Get top 25 names by frequency to see where family terms rank
$topNames = $db->fetchAll(
    'SELECT original_name, romanization, translation, name_type, frequency 
     FROM name_dictionary 
     WHERE novel_id = 7 
     ORDER BY frequency DESC, name_type ASC, original_name ASC
     LIMIT 25',
    []
);

echo "Top 25 names by frequency:\n\n";

$familyTerms = ['父さん', 'お父さん', '母さん', 'お母さん', '兄さん', 'お兄さん', 'お兄ちゃん', 'お姉ちゃん'];
$familyTermsFound = [];

foreach ($topNames as $index => $name) {
    $rank = $index + 1;
    $isFamilyTerm = in_array($name['original_name'], $familyTerms);
    $marker = $isFamilyTerm ? ' ⭐ FAMILY TERM' : '';
    
    echo "{$rank}. {$name['original_name']} → '{$name['translation']}' (freq: {$name['frequency']}){$marker}\n";
    
    if ($isFamilyTerm) {
        $familyTermsFound[] = $name['original_name'];
    }
}

echo "\n=== Analysis ===\n";
echo "Family terms in top 20: " . count(array_slice($familyTermsFound, 0, 20)) . "\n";
echo "Family terms found in top 25: " . implode(', ', $familyTermsFound) . "\n";

if (count($familyTermsFound) < count($familyTerms)) {
    $missingTerms = array_diff($familyTerms, $familyTermsFound);
    echo "Missing family terms from top 25: " . implode(', ', $missingTerms) . "\n";
    
    echo "\n=== Checking Missing Terms ===\n";
    foreach ($missingTerms as $term) {
        $termData = $db->fetchOne(
            'SELECT original_name, romanization, translation, name_type, frequency 
             FROM name_dictionary 
             WHERE novel_id = 7 AND original_name = ?',
            [$term]
        );
        
        if ($termData) {
            echo "- {$term} → '{$termData['translation']}' (freq: {$termData['frequency']}) - EXISTS but low frequency\n";
        } else {
            echo "- {$term} → NOT FOUND in dictionary\n";
        }
    }
}

echo "\n=== Recommendation ===\n";
if (count($familyTermsFound) >= 3) {
    echo "✓ Most important family terms are in top 20 and should be included in Gemini prompts.\n";
} else {
    echo "⚠️  Important family terms may not be included in Gemini prompts due to frequency ranking.\n";
    echo "Consider increasing frequency of family terms or modifying prompt logic.\n";
}

echo "\n=== Complete ===\n";
