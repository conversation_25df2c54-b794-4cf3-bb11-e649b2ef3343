<?php
/**
 * Gemini 1.5 Translation Service
 * Google Gemini 1.5 Flash AI Translation Implementation
 * Novel Translation Application
 */

require_once 'BaseGeminiTranslationService.php';

class Gemini15TranslationService extends BaseGeminiTranslationService {

    protected function initializeVersionConfig(): void {
        $this->version = '1.5';
        $this->apiUrl = GEMINI_15_API_URL;
        $this->model = GEMINI_15_MODEL;
    }

    /**
     * Make API request to Gemini 1.5
     */
    protected function makeApiRequest(string $prompt, array $context = [], array $contentAnalysis = []): array {
        $maxRetries = 3;
        $retryDelay = 1; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            file_put_contents('debug.log', "Gemini15TranslationService: Attempt {$attempt} of {$maxRetries}\n", FILE_APPEND);

            $result = $this->executeSingleApiRequest($prompt, $context, $attempt);

            if ($result['success']) {
                return $result;
            }

            // Check if error is retryable
            if (!$this->isRetryableError($result)) {
                file_put_contents('debug.log', "Gemini15TranslationService: Non-retryable error, stopping retries\n", FILE_APPEND);
                return $result;
            }

            // Wait before retry (exponential backoff)
            if ($attempt < $maxRetries) {
                $delay = $retryDelay * pow(2, $attempt - 1);
                file_put_contents('debug.log', "Gemini15TranslationService: Waiting {$delay}s before retry\n", FILE_APPEND);
                sleep($delay);
            }
        }

        return $result;
    }

    /**
     * Execute single API request to Gemini 1.5
     */
    private function executeSingleApiRequest(string $prompt, array $context, int $attempt): array {
        error_log("Gemini15TranslationService: Making API request (attempt {$attempt})");
        error_log("Gemini15TranslationService: API Key length: " . strlen($this->apiKey));
        error_log("Gemini15TranslationService: Prompt length: " . strlen($prompt));

        // Calculate adaptive token limit
        $adaptiveTokenLimit = $this->calculateAdaptiveTokenLimit($prompt, $context);

        // Default generation config for Gemini 1.5
        $generationConfig = [
            'temperature' => 0.3,
            'topK' => 40,
            'topP' => 0.95,
            'maxOutputTokens' => $adaptiveTokenLimit
        ];

        // Adjust parameters for title translations
        if (isset($context['type']) && $context['type'] === 'title') {
            $generationConfig['temperature'] = 0.1;
            $generationConfig['topK'] = 20;
            $generationConfig['topP'] = 0.8;
            $generationConfig['maxOutputTokens'] = 50; // Very conservative to prevent truncation

            file_put_contents('debug.log', "Gemini15TranslationService: Using conservative maxOutputTokens: 50 for title translation\n", FILE_APPEND);
        }

        // Build request data in Gemini format
        $requestData = [
            'contents' => [
                [
                    'parts' => [
                        [
                            'text' => $prompt
                        ]
                    ]
                ]
            ],
            'generationConfig' => $generationConfig,
            'safetySettings' => [
                [
                    'category' => 'HARM_CATEGORY_HARASSMENT',
                    'threshold' => 'BLOCK_NONE'
                ],
                [
                    'category' => 'HARM_CATEGORY_HATE_SPEECH',
                    'threshold' => 'BLOCK_NONE'
                ],
                [
                    'category' => 'HARM_CATEGORY_SEXUALLY_EXPLICIT',
                    'threshold' => 'BLOCK_NONE'
                ],
                [
                    'category' => 'HARM_CATEGORY_DANGEROUS_CONTENT',
                    'threshold' => 'BLOCK_NONE'
                ]
            ]
        ];

        // Log token limit for debugging
        file_put_contents('debug.log', "Gemini15TranslationService: Using maxOutputTokens: {$generationConfig['maxOutputTokens']}\n", FILE_APPEND);

        $jsonData = json_encode($requestData);
        error_log("Gemini15TranslationService: Request data size: " . strlen($jsonData) . " bytes");

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl . '?key=' . $this->apiKey,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
            ],
            CURLOPT_TIMEOUT => TRANSLATION_TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log response details
        error_log("Gemini15TranslationService: HTTP Code: " . $httpCode);
        error_log("Gemini15TranslationService: Response length: " . strlen($response));

        if ($error) {
            error_log("Gemini15TranslationService: cURL error: " . $error);
            return ['success' => false, 'error' => "cURL error: {$error}", 'retryable' => true];
        }

        if ($httpCode !== 200) {
            error_log("Gemini15TranslationService: HTTP error response: " . substr($response, 0, 500));

            // Parse error response for better error handling
            $errorDetails = $this->parseErrorResponse($response, $httpCode);

            return [
                'success' => false,
                'error' => "HTTP error: {$httpCode}",
                'details' => $errorDetails,
                'retryable' => $this->isHttpCodeRetryable($httpCode),
                'response' => $response
            ];
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("Gemini15TranslationService: JSON decode error: " . json_last_error_msg());
            error_log("Gemini15TranslationService: Raw response: " . substr($response, 0, 500));
            return ['success' => false, 'error' => 'Invalid JSON response: ' . json_last_error_msg(), 'retryable' => false];
        }

        error_log("Gemini15TranslationService: API request successful");
        return ['success' => true, 'data' => $decodedResponse];
    }

    /**
     * Calculate adaptive token limit based on content size and type
     */
    private function calculateAdaptiveTokenLimit(string $prompt, array $context): int {
        $promptLength = mb_strlen($prompt);

        // For titles, use very small limit to prevent truncation
        if (isset($context['type']) && $context['type'] === 'title') {
            $baseLimit = 50; // Very conservative to prevent truncation

            // Reduce for retry attempts
            if (isset($context['truncation_retry'])) {
                $retryAttempt = $context['truncation_retry'];
                $baseLimit = max(20, $baseLimit - ($retryAttempt * 10));
                file_put_contents('debug.log', "Gemini15TranslationService: Title retry attempt {$retryAttempt}, using token limit: {$baseLimit}\n", FILE_APPEND);
            }

            return $baseLimit;
        }

        // For content, calculate based on input size
        $baseLimit = 2048; // Default for Gemini 1.5

        if ($promptLength < 2000) {
            $baseLimit = 2048; // Small content
        } elseif ($promptLength < 6000) {
            $baseLimit = 4096; // Medium content
        } elseif ($promptLength < 12000) {
            $baseLimit = 6144; // Large content
        } else {
            $baseLimit = 8192; // Very large content
        }

        // Reduce token limit for retry attempts
        if (isset($context['truncation_retry'])) {
            $retryAttempt = $context['truncation_retry'];
            $reductionFactor = 1 - ($retryAttempt * 0.25); // Reduce by 25% per retry
            $baseLimit = max(1024, floor($baseLimit * $reductionFactor));

            file_put_contents('debug.log', "Gemini15TranslationService: Retry attempt {$retryAttempt}, reduced token limit to {$baseLimit}\n", FILE_APPEND);
        }

        return $baseLimit;
    }

    /**
     * Check if error is retryable
     */
    protected function isRetryableError(array $response): bool {
        if (!isset($response['error'])) return false;

        $error = strtolower($response['error']);
        $retryableErrors = [
            'timeout',
            'connection',
            'network',
            'temporary',
            'rate limit',
            'quota exceeded',
            'service unavailable',
            'internal error',
            'server error'
        ];

        foreach ($retryableErrors as $retryableError) {
            if (strpos($error, $retryableError) !== false) {
                return true;
            }
        }

        // Check HTTP codes
        if (isset($response['details']['http_code'])) {
            $retryableCodes = [429, 500, 502, 503, 504];
            return in_array($response['details']['http_code'], $retryableCodes);
        }

        return false;
    }

    /**
     * Check if HTTP code is retryable
     */
    protected function isHttpCodeRetryable(int $httpCode): bool {
        $retryableCodes = [429, 500, 502, 503, 504];
        return in_array($httpCode, $retryableCodes);
    }

    /**
     * Parse error response for better error handling
     */
    protected function parseErrorResponse(string $response, int $httpCode): array {
        $details = [
            'http_code' => $httpCode,
            'raw_response' => substr($response, 0, 1000)
        ];

        try {
            $decoded = json_decode($response, true);
            if ($decoded && isset($decoded['error'])) {
                $details['error_message'] = $decoded['error']['message'] ?? 'Unknown error';
                $details['error_code'] = $decoded['error']['code'] ?? $httpCode;
                $details['error_status'] = $decoded['error']['status'] ?? 'UNKNOWN';
            }
        } catch (Exception $e) {
            $details['parse_error'] = $e->getMessage();
        }

        return $details;
    }

    /**
     * Build translation prompt for Gemini 1.5
     */
    protected function buildTranslationPrompt(string $text, string $targetLanguage, string $sourceLanguage, array $context, array $contentAnalysis = []): string {
        $languageNames = [
            'en' => 'English',
            'ja' => 'Japanese',
            'zh' => 'Chinese',
            'ko' => 'Korean'
        ];

        $targetLangName = $languageNames[$targetLanguage] ?? $targetLanguage;
        $sourceLangName = $sourceLanguage === 'auto' ? 'the source language' : ($languageNames[$sourceLanguage] ?? $sourceLanguage);

        // Check if this is a simple test context - use minimal prompt
        if (isset($context['simple']) && $context['simple'] === true) {
            return "Translate this text from {$sourceLangName} to {$targetLangName}:\n\n{$text}";
        }

        // For titles, use very simple prompt to avoid complexity issues
        if (isset($context['type']) && $context['type'] === 'title') {
            return "Translate this novel title from {$sourceLangName} to {$targetLangName}. Provide only the translated title:\n\n{$text}";
        }

        $prompt = "Translate the following text from {$sourceLangName} to {$targetLangName}.\n\n";

        // Add timeout optimization instructions for large content
        if (!empty($contentAnalysis) && $contentAnalysis['high_timeout_risk']) {
            $prompt .= "IMPORTANT: This is a large content block. Please provide a direct, efficient translation without extensive explanations or commentary to ensure timely completion.\n\n";
        }

        // Add strict no-explanation instruction for all translations
        $prompt .= "CRITICAL: Provide ONLY the translation. Do not add any notes, explanations, comments, or meta-text about the translation process, sound effects, formatting, or any other aspects. Your response should contain only the translated text.\n\n";

        // Essential dialogue preservation rules (simplified)
        $prompt .= "DIALOGUE RULES:\n";
        $prompt .= "- Do NOT add dialogue tags (\"he said\", \"she replied\") that don't exist in the original\n";
        $prompt .= "- Preserve the exact dialogue structure and formatting\n";
        $prompt .= "- Only translate what is actually present in the source text\n\n";

        // Translation requirements (simplified)
        $prompt .= "TRANSLATION REQUIREMENTS:\n";
        $prompt .= "- Translate text to natural English, EXCEPT family terms which must remain romanized\n";
        $prompt .= "- Convert sound effects to descriptive English words\n";
        $prompt .= "- Use character names and honorifics exactly as specified in name dictionary\n";
        $prompt .= "- CRITICAL: NEVER use English words 'father', 'mother', 'brother', 'sister', 'mom', 'dad'\n";
        $prompt .= "- ALWAYS use romanized family terms: Otou-san, Okaa-san, Onii-san, Onee-san, etc.\n";
        $prompt .= "- Make the text fully comprehensible to English readers\n\n";

        // Formatting preservation (simplified)
        $prompt .= "FORMATTING RULES:\n";
        $prompt .= "- Preserve Japanese/Chinese punctuation marks exactly: 「」『』【】（）・※〜～\n";
        $prompt .= "- Keep all line breaks and paragraph structure\n";
        $prompt .= "- Maintain the same visual layout as the original\n\n";

        // Add specific instructions based on context
        if (isset($context['type'])) {
            switch ($context['type']) {
                case 'chapter':
                case 'content':
                case 'chunk':
                    $prompt .= "This is novel content. Maintain the narrative style and preserve character names.\n";

                    // Add narrative perspective instructions based on context analysis
                    if (isset($context['narrative_context'])) {
                        $narrativeContext = $context['narrative_context'];
                        $prompt .= $this->buildNarrativePerspectiveInstructions($narrativeContext, $context);
                    }

                    // Add specific dialogue and narrative formatting instructions
                    $prompt .= "DIALOGUE AND NARRATIVE FORMATTING:\n";
                    $prompt .= "- Preserve all dialogue quotation marks and formatting exactly as they appear\n";
                    $prompt .= "- Maintain speaker attribution and dialogue tags in their original positions\n";
                    $prompt .= "- Keep conversation flow natural and easy to follow\n";
                    $prompt .= "- Preserve narrative descriptions between dialogue exactly as formatted\n";
                    $prompt .= "- Maintain the original rhythm and pacing through proper punctuation\n";
                    $prompt .= "- Keep all exclamation points, question marks, and ellipses in dialogue\n";
                    $prompt .= "- Preserve any special formatting for thoughts, internal monologue, or emphasis\n\n";

                    // Add chunk context if available
                    if (isset($context['chunk_number'])) {
                        $prompt .= "This is chunk {$context['chunk_number']}";
                        if (isset($context['total_chunks'])) {
                            $prompt .= " of {$context['total_chunks']}";
                        }
                        $prompt .= " from a larger chapter. Maintain consistency with the overall narrative.\n";

                        // Add context from previous/next chunks if available
                        if (isset($context['previous_context'])) {
                            $prompt .= "Previous context: " . substr($context['previous_context'], 0, 100) . "...\n";
                        }
                        if (isset($context['next_context'])) {
                            $prompt .= "Following context: " . substr($context['next_context'], 0, 100) . "...\n";
                        }
                    }

                    // Add dialogue-specific instructions if dialogue is detected
                    $prompt .= $this->addDialogueSpecificInstructions($text);

                    // Add honorific preservation instructions
                    $detectedLanguage = $this->honorificService->detectLanguage($text);
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions($detectedLanguage);
                    $prompt .= "\n";
                    break;
                case 'synopsis':
                    $prompt .= "This is a novel synopsis/description. Keep it engaging and informative.\n\n";

                    // Add honorific preservation instructions for synopsis
                    $detectedLanguage = $this->honorificService->detectLanguage($text);
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions($detectedLanguage);
                    break;
            }
        }

        // Add name consistency instructions (simplified)
        // Skip name dictionary for title translations to avoid prompt bloat
        if (isset($context['names']) && !empty($context['names']) &&
            (!isset($context['type']) || $context['type'] !== 'title')) {

            $prompt .= "CHARACTER NAMES:\n";

            // First, prioritize family terms to ensure they're always included
            $familyTerms = [];
            $otherNames = [];

            foreach ($context['names'] as $name) {
                $original = isset($name['original_name']) ? trim($name['original_name']) : '';

                // Check if this is a family term
                $isFamilyTerm = preg_match('/(?:父|母|兄|姉|おじ|おば|じい|ばあ)(?:さん|ちゃん|様)?/', $original);

                if ($isFamilyTerm) {
                    $familyTerms[] = $name;
                } else {
                    $otherNames[] = $name;
                }
            }

            // Combine family terms first, then other names
            $prioritizedNames = array_merge($familyTerms, $otherNames);

            $nameCount = 0;
            foreach ($prioritizedNames as $name) {
                // Use translation if available and not empty, otherwise romanization, otherwise original
                $translation = isset($name['translation']) ? trim($name['translation']) : '';
                $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';
                $original = isset($name['original_name']) ? trim($name['original_name']) : '';

                // Priority: translation > romanization > original (only if not empty)
                if (!empty($translation)) {
                    $targetName = $translation;
                } elseif (!empty($romanization)) {
                    $targetName = $romanization;
                } else {
                    $targetName = $original;
                }

                // Skip entries with empty original names or target names
                if (empty($original) || empty($targetName)) {
                    continue;
                }

                $prompt .= "- {$original} → {$targetName}\n";
                $nameCount++;

                // Limit to first 25 names (increased from 20) to accommodate family terms
                if ($nameCount >= 25) {
                    $prompt .= "- (and " . (count($context['names']) - 25) . " more names...)\n";
                    break;
                }
            }
            $prompt .= "Use these exact name translations consistently. Family terms MUST ALWAYS use the complete romanized form (e.g., Tou-san, Kaa-san, Onii-chan).\n";
            $prompt .= "FORBIDDEN: NEVER use English words 'father', 'mother', 'brother', 'sister', 'mom', 'dad', 'papa', 'mama'.\n";
            $prompt .= "REQUIRED: ALWAYS use romanized forms like Otou-san, Okaa-san, Onii-san, Onee-san, Tou-san, Kaa-san.\n\n";
        }

        // Final reminder (simplified)
        $prompt .= "FINAL REQUIREMENTS:\n";
        $prompt .= "- Translate to natural, fluent English EXCEPT family terms which stay romanized\n";
        $prompt .= "- Use provided character names and family terms exactly as specified\n";
        $prompt .= "- ABSOLUTELY FORBIDDEN: 'father', 'mother', 'brother', 'sister', 'mom', 'dad'\n";
        $prompt .= "- MANDATORY: Use Otou-san, Okaa-san, Onii-san, Onee-san, Tou-san, Kaa-san\n";
        $prompt .= "- Preserve original formatting and structure\n";
        $prompt .= "- Provide only the translation, no explanations\n\n";

        $prompt .= "Text to translate:\n" . $text;

        return $prompt;
    }

    /**
     * Build narrative perspective instructions based on context analysis
     */
    private function buildNarrativePerspectiveInstructions(array $narrativeContext, array $fullContext): string {
        // Check if we have intelligent perspective analysis
        if (isset($narrativeContext['optimal_perspective']) && isset($narrativeContext['perspective_reasoning'])) {
            // Use the intelligent perspective service results
            return $this->buildIntelligentPerspectiveInstructions($narrativeContext, $fullContext);
        }

        // Fallback to legacy perspective handling for backward compatibility
        return $this->buildLegacyPerspectiveInstructions($narrativeContext, $fullContext);
    }

    /**
     * Build intelligent perspective instructions using PerspectiveService results
     */
    private function buildIntelligentPerspectiveInstructions(array $narrativeContext, array $fullContext): string {
        $instructions = "\n🎯 INTELLIGENT PERSPECTIVE HANDLING 🎯\n\n";

        $contentType = $narrativeContext['content_type'] ?? 'narrative';
        $optimalPerspective = $narrativeContext['optimal_perspective'] ?? 'preserve_original';
        $originalPerspective = $narrativeContext['narrative_voice'] ?? 'third_person';
        $confidence = $narrativeContext['narrative_confidence'] ?? 0.3;
        $reasoning = $narrativeContext['perspective_reasoning'] ?? 'No reasoning provided';

        $instructions .= "📋 CONTENT TYPE: " . strtoupper(str_replace('_', ' ', $contentType)) . "\n";
        $instructions .= "🎭 OPTIMAL PERSPECTIVE: " . strtoupper(str_replace('_', ' ', $optimalPerspective)) . "\n";
        $instructions .= "📊 ORIGINAL PERSPECTIVE: " . strtoupper($originalPerspective) . " (confidence: " . round($confidence * 100, 1) . "%)\n";
        $instructions .= "💡 REASONING: " . $reasoning . "\n\n";

        // Generate specific instructions based on optimal perspective
        switch ($optimalPerspective) {
            case 'first_person':
                $instructions .= $this->getFirstPersonInstructions();
                break;

            case 'second_person':
                $instructions .= $this->getSecondPersonInstructions();
                break;

            case 'third_person':
                $instructions .= $this->getThirdPersonInstructions();
                break;

            case 'third_person_limited':
                $instructions .= $this->getThirdPersonLimitedInstructions();
                break;

            case 'third_person_omniscient':
                $instructions .= $this->getThirdPersonOmniscientInstructions();
                break;

            case 'preserve_original':
            default:
                $instructions .= $this->getPreserveOriginalInstructions($originalPerspective);
                break;
        }

        // Add consistency requirements
        $instructions .= "\n🔒 CONSISTENCY REQUIREMENTS:\n";
        $instructions .= "- Maintain the chosen perspective throughout the entire text\n";
        $instructions .= "- Do not mix different perspectives within the same sentence or paragraph\n";
        $instructions .= "- Ensure pronoun consistency and grammatical correctness\n";
        $instructions .= "- Preserve the natural flow and readability of the text\n";
        $instructions .= "- For dialogue: Always preserve the original perspective within quotation marks\n";
        $instructions .= "- For narrative: Follow the determined optimal perspective consistently\n\n";

        return $instructions;
    }

    /**
     * Legacy perspective handling for backward compatibility
     */
    private function buildLegacyPerspectiveInstructions(array $narrativeContext, array $fullContext): string {
        $instructions = "\n⚠️ LEGACY PERSPECTIVE HANDLING ⚠️\n\n";

        $perspective = $narrativeContext['narrative_voice'] ?? 'third_person';
        $confidence = $narrativeContext['narrative_confidence'] ?? 0.3;

        $instructions .= "📊 DETECTED PERSPECTIVE: " . strtoupper($perspective) . " (confidence: " . round($confidence * 100, 1) . "%)\n";
        $instructions .= "🎭 APPLIED PERSPECTIVE: PRESERVE ORIGINAL\n\n";

        $instructions .= "📝 PERSPECTIVE PRESERVATION REQUIREMENTS:\n";
        $instructions .= "- Maintain the original narrative perspective as detected in the source text\n";
        $instructions .= "- Do not force conversion between first, second, or third person\n";
        $instructions .= "- Preserve the author's intended narrative voice and style\n";
        $instructions .= "- Keep dialogue perspective exactly as it appears in the original\n";
        $instructions .= "- Maintain consistency within the same narrative section\n\n";

        return $instructions;
    }

    /**
     * Generate first person perspective instructions
     */
    private function getFirstPersonInstructions(): string {
        $instructions = "📝 FIRST PERSON PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'I', 'me', 'my', 'mine', 'myself' for singular first person\n";
        $instructions .= "- Use 'we', 'us', 'our', 'ours', 'ourselves' for plural first person\n";
        $instructions .= "- Express thoughts and feelings directly: 'I think', 'I feel', 'I believe'\n";
        $instructions .= "- Use active voice: 'I did', 'I saw', 'I went'\n";
        $instructions .= "- Maintain personal, intimate tone appropriate for first person narration\n";
        $instructions .= "- Express internal thoughts and emotions directly\n";
        $instructions .= "- Describe actions and observations from personal viewpoint\n";
        $instructions .= "- Use 'I thought', 'I realized', 'I noticed' for mental processes\n";

        return $instructions;
    }

    /**
     * Generate second person perspective instructions
     */
    private function getSecondPersonInstructions(): string {
        $instructions = "📝 SECOND PERSON PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'you', 'your', 'yours', 'yourself' to address the reader/user\n";
        $instructions .= "- Use imperative mood for instructions: 'Click here', 'Enter your name'\n";
        $instructions .= "- Make the text feel direct and engaging\n";
        $instructions .= "- Use active voice: 'You can', 'You should', 'You will'\n";
        $instructions .= "- Guide user actions with clear, actionable language\n";
        $instructions .= "- Be helpful and direct in communication\n";
        $instructions .= "- Provide clear next steps and guidance\n";

        return $instructions;
    }

    /**
     * Generate third person perspective instructions
     */
    private function getThirdPersonInstructions(): string {
        $instructions = "📝 THIRD PERSON PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'he', 'she', 'it', 'they' for subjects\n";
        $instructions .= "- Use 'him', 'her', 'it', 'them' for objects\n";
        $instructions .= "- Use 'his', 'her', 'its', 'their' for possession\n";
        $instructions .= "- Maintain objective, external viewpoint\n";
        $instructions .= "- Use 'he said', 'she thought', 'they realized' for attribution\n";
        $instructions .= "- Describe actions from external observer perspective\n";
        $instructions .= "- Express character thoughts as: 'he thought', 'she wondered'\n";
        $instructions .= "- Describe emotions objectively: 'his heart raced', 'her eyes widened'\n";

        return $instructions;
    }

    /**
     * Generate third person limited perspective instructions
     */
    private function getThirdPersonLimitedInstructions(): string {
        $instructions = "📝 THIRD PERSON LIMITED PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'he', 'she', 'it', 'they' for subjects\n";
        $instructions .= "- Use 'him', 'her', 'it', 'them' for objects\n";
        $instructions .= "- Use 'his', 'her', 'its', 'their' for possession\n";
        $instructions .= "- Focus on ONE character's thoughts and feelings\n";
        $instructions .= "- Only reveal what the focal character knows or experiences\n";
        $instructions .= "- Use 'he thought', 'she wondered' for the main character only\n";
        $instructions .= "- Describe other characters from the focal character's perspective\n";
        $instructions .= "- Maintain consistent focus on the chosen character throughout\n";

        return $instructions;
    }

    /**
     * Generate third person omniscient perspective instructions
     */
    private function getThirdPersonOmniscientInstructions(): string {
        $instructions = "📝 THIRD PERSON OMNISCIENT PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Use 'he', 'she', 'it', 'they' for subjects\n";
        $instructions .= "- Use 'him', 'her', 'it', 'them' for objects\n";
        $instructions .= "- Use 'his', 'her', 'its', 'their' for possession\n";
        $instructions .= "- Access to ALL characters' thoughts and feelings\n";
        $instructions .= "- Can reveal information unknown to any single character\n";
        $instructions .= "- Use 'he thought', 'she wondered' for multiple characters\n";
        $instructions .= "- Provide broader perspective and context\n";
        $instructions .= "- Can shift focus between different characters\n";
        $instructions .= "- Maintain objective, all-knowing narrator voice\n";
        $instructions .= "- Convert first-person pronouns (I, we, me, us) to third-person (he, she, they, him, her, them)\n";
        $instructions .= "- Transform 'I think' to 'he thought', 'we decided' to 'they decided'\n";

        return $instructions;
    }

    /**
     * Generate preserve original perspective instructions
     */
    private function getPreserveOriginalInstructions(string $originalPerspective): string {
        $instructions = "📝 PRESERVE ORIGINAL PERSPECTIVE REQUIREMENTS:\n";
        $instructions .= "- Maintain the original point of view as detected in the source text\n";
        $instructions .= "- Do not convert between first, second, or third person\n";
        $instructions .= "- Keep the natural voice and tone of the original content\n";
        $instructions .= "- Preserve the author's intended narrative style\n";
        $instructions .= "- Original perspective detected: " . strtoupper($originalPerspective) . "\n";
        $instructions .= "- For dialogue: Preserve character speech patterns exactly\n";
        $instructions .= "- Maintain quotation marks and dialogue formatting\n";
        $instructions .= "- Keep character voice authentic to original\n";
        $instructions .= "- For narrative: Respect the author's chosen narrative voice\n";
        $instructions .= "- Maintain consistency with the established perspective\n";
        $instructions .= "- Preserve the intimacy or distance created by the original perspective\n";

        return $instructions;
    }

    /**
     * Detect dialogue content and add specific formatting instructions
     */
    private function addDialogueSpecificInstructions(string $text): string {
        $instructions = "";

        // Detect various dialogue patterns
        $hasJapaneseQuotes = preg_match('/[「」『』]/', $text);
        $hasWesternQuotes = preg_match('/["\'"]/', $text);
        $hasDialogueTags = preg_match('/(said|asked|replied|whispered|shouted|muttered|exclaimed)/i', $text);
        $hasConversation = preg_match('/[「"\'"][^「"\']*[」"\'"][^「"\']*[「"\'"]/', $text);

        if ($hasJapaneseQuotes || $hasWesternQuotes || $hasDialogueTags || $hasConversation) {
            $instructions .= "DIALOGUE DETECTED - SPECIAL FORMATTING REQUIREMENTS:\n";

            if ($hasJapaneseQuotes) {
                // Check which specific types are present
                $has_single_quotes = preg_match('/[「」]/', $text);
                $has_double_quotes = preg_match('/[『』]/', $text);

                if ($has_single_quotes && $has_double_quotes) {
                    $instructions .= "- MIXED Japanese quotation marks detected: Preserve BOTH 「」 and 『』 exactly as they appear - do NOT convert between types\n";
                } elseif ($has_single_quotes) {
                    $instructions .= "- Japanese single quotation marks 「」 detected: Keep ALL as 「」 - do NOT convert to 『』 or English quotes\n";
                } elseif ($has_double_quotes) {
                    $instructions .= "- Japanese double quotation marks 『』 detected: Keep ALL as 『』 - do NOT convert to 「」 or English quotes\n";
                }
            }

            if ($hasWesternQuotes) {
                $instructions .= "- Quotation marks detected: Maintain exact placement and nesting of quotes\n";
            }

            if ($hasDialogueTags) {
                $instructions .= "- Dialogue tags detected: Keep speaker attribution clear and natural\n";
            }

            if ($hasConversation) {
                $instructions .= "- Multi-speaker conversation detected: Ensure each speaker's lines are clearly distinguished\n";
            }

            $instructions .= "- Preserve all dialogue punctuation including commas, periods, exclamation points, and question marks within quotes\n";
            $instructions .= "- Maintain the natural flow and rhythm of conversation\n";
            $instructions .= "- Keep any narrative interruptions within dialogue properly formatted\n\n";
        }

        return $instructions;
    }

    /**
     * Extract translation from Gemini 1.5 API response
     */
    protected function extractTranslationFromResponse(array $response, array $context = []): string {
        if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            $translatedText = trim($response['candidates'][0]['content']['parts'][0]['text']);

            // Check if response was truncated due to token limits
            $completionInfo = $this->validateResponseCompletion($response, $translatedText, $context);

            // Skip truncation check for titles to avoid false positives
            if (isset($context['type']) && $context['type'] === 'title') {
                file_put_contents('debug.log', "Gemini15TranslationService: Skipping truncation check for title translation\n", FILE_APPEND);
            } elseif (!$completionInfo['is_complete']) {
                file_put_contents('debug.log', "Gemini15TranslationService: Response truncation detected - " . $completionInfo['reason'] . "\n", FILE_APPEND);

                // If truncated mid-sentence, try to find a good stopping point
                if ($completionInfo['truncated_mid_sentence']) {
                    $adjustedText = $this->findCompleteSentenceEnding($translatedText);
                    if ($adjustedText !== $translatedText) {
                        file_put_contents('debug.log', "Gemini15TranslationService: Adjusted truncated response to complete sentence\n", FILE_APPEND);
                        $translatedText = $adjustedText;
                    }
                }

                // Throw exception to trigger retry with adjusted parameters
                throw new Exception('Response was truncated due to token limits: ' . $completionInfo['reason']);
            }

            return $translatedText;
        }

        throw new Exception('No translation found in response');
    }

    /**
     * Validate if API response was completed or truncated
     */
    private function validateResponseCompletion(array $response, string $translatedText, array $context = []): array {
        $result = [
            'is_complete' => true,
            'reason' => '',
            'truncated_mid_sentence' => false,
            'finish_reason' => null
        ];

        // Check Gemini finish reason
        if (isset($response['candidates'][0]['finishReason'])) {
            $finishReason = $response['candidates'][0]['finishReason'];
            $result['finish_reason'] = $finishReason;

            if ($finishReason === 'MAX_TOKENS') {
                $result['is_complete'] = false;
                $result['reason'] = 'Response truncated due to maxOutputTokens limit';
                $result['truncated_mid_sentence'] = $this->isTextTruncatedMidSentence($translatedText, $context);
            } elseif ($finishReason === 'SAFETY') {
                $result['is_complete'] = false;
                $result['reason'] = 'Response blocked by safety filter';
            }
        }

        // Additional heuristic checks for truncation
        if ($result['is_complete']) {
            // Check if text ends abruptly (common signs of truncation)
            if ($this->isTextTruncatedMidSentence($translatedText, $context)) {
                $result['is_complete'] = false;
                $result['reason'] = 'Text appears to end mid-sentence (heuristic detection)';
                $result['truncated_mid_sentence'] = true;
            }

            // Check for incomplete dialogue
            if ($this->hasIncompleteDialogue($translatedText)) {
                $result['is_complete'] = false;
                $result['reason'] = 'Text contains incomplete dialogue markers';
                $result['truncated_mid_sentence'] = true;
            }
        }

        return $result;
    }

    /**
     * Check if text appears to be truncated mid-sentence
     */
    private function isTextTruncatedMidSentence(string $text, array $context = []): bool {
        $text = trim($text);
        if (empty($text)) {
            return false;
        }

        // For titles, use much more lenient truncation detection
        if (isset($context['type']) && $context['type'] === 'title') {
            return $this->isTitleTruncated($text);
        }

        $lastChar = mb_substr($text, -1);

        // Check if ends with sentence-ending punctuation
        if (in_array($lastChar, ['.', '!', '?', '。', '！', '？', '』', '」', '"', "'"])) {
            return false;
        }

        // Check if ends with common continuation patterns
        $continuationPatterns = [
            '/\s+(and|but|or|so|then|however|therefore|meanwhile|suddenly|finally)$/i',
            '/\s+(the|a|an|this|that|these|those|his|her|their|my|your)$/i',
            '/\s+(is|are|was|were|will|would|could|should|might|must)$/i',
            '/\s+(to|for|with|by|from|in|on|at|of|about)$/i',
            '/,\s*$/i', // Ends with comma
            '/\s+[a-z]+$/i' // Ends with lowercase word (likely mid-sentence)
        ];

        foreach ($continuationPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        // Check if last sentence is significantly shorter than average
        $sentences = preg_split('/[.!?。！？]+/', $text);
        if (count($sentences) > 1) {
            $lastSentence = trim(end($sentences));
            if (!empty($lastSentence)) {
                $avgLength = array_sum(array_map('mb_strlen', array_slice($sentences, 0, -1))) / (count($sentences) - 1);
                if (mb_strlen($lastSentence) < $avgLength * 0.3) {
                    return true;
                }
            }
        }

        // Check for very short text that's likely incomplete (but allow single complete words)
        if (mb_strlen($text) <= 3 && !preg_match('/^[A-Z][a-z]*[.!?]$/', $text)) {
            return true;
        }

        return false;
    }

    /**
     * Check if a title appears to be truncated
     */
    private function isTitleTruncated(string $text): bool {
        // For titles, be much more lenient about truncation detection
        // Only flag as truncated if it's obviously incomplete

        // Check for very obvious truncation patterns
        $obviousTruncationPatterns = [
            '/\s+(and|or|of|the|a|an)$/i', // Ends with common connecting words
            '/,\s*$/i', // Ends with comma
            '/:\s*$/i', // Ends with colon
            '/\s+[a-z]+$/i' // Ends with lowercase word
        ];

        foreach ($obviousTruncationPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        // Check if title is suspiciously long (likely truncated)
        if (mb_strlen($text) > 200) {
            return true;
        }

        return false;
    }

    /**
     * Check for incomplete dialogue markers
     */
    private function hasIncompleteDialogue(string $text): bool {
        // Check for unmatched quotation marks
        $openQuotes = substr_count($text, '「') + substr_count($text, '『') + substr_count($text, '"');
        $closeQuotes = substr_count($text, '」') + substr_count($text, '』') + substr_count($text, '"');

        return $openQuotes !== $closeQuotes;
    }

    /**
     * Find a complete sentence ending for truncated text
     */
    private function findCompleteSentenceEnding(string $text): string {
        // Find the last complete sentence
        $sentences = preg_split('/([.!?。！？]+)/', $text, -1, PREG_SPLIT_DELIM_CAPTURE);

        $result = '';
        for ($i = 0; $i < count($sentences) - 1; $i += 2) {
            if (isset($sentences[$i + 1])) {
                $result .= $sentences[$i] . $sentences[$i + 1];
            }
        }

        return trim($result) ?: $text;
    }

    /**
     * Clean title translation to ensure single, clean result
     */
    protected function cleanTitleTranslation(string $translatedText): string {
        // Remove any explanatory text or multiple options
        $lines = explode("\n", $translatedText);
        $cleanedLines = [];

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Skip lines that look like explanations or notes
            if (preg_match('/^(Note:|Translation:|Alternative:|Here|This|The title)/i', $line)) {
                continue;
            }

            // Skip lines with multiple options (containing "or", "alternatively", etc.)
            if (preg_match('/\b(or|alternatively|another option|could be)\b/i', $line)) {
                continue;
            }

            // Skip lines that are clearly not titles (too long, contain explanations)
            if (mb_strlen($line) > 150 || preg_match('/\b(means|refers to|is about|translates to)\b/i', $line)) {
                continue;
            }

            $cleanedLines[] = $line;
        }

        // Take the first valid line as the title
        $result = !empty($cleanedLines) ? $cleanedLines[0] : $translatedText;

        // Remove quotation marks if they wrap the entire title
        $result = preg_replace('/^["\'""](.+)["\'""]$/', '$1', $result);

        // Remove any remaining formatting artifacts
        $result = preg_replace('/^\d+\.\s*/', '', $result); // Remove numbering
        $result = preg_replace('/^[-*]\s*/', '', $result); // Remove bullet points

        return trim($result);
    }

    /**
     * Restore formatting if lost during translation
     */
    protected function restoreFormattingIfLost(string $originalText, string $translatedText, array $context): string {
        // Check if formatting was preserved
        $originalLineBreaks = substr_count($originalText, "\n");
        $translatedLineBreaks = substr_count($translatedText, "\n");

        $originalParagraphs = substr_count($originalText, "\n\n");
        $translatedParagraphs = substr_count($translatedText, "\n\n");

        // If formatting is severely lost, attempt to restore it
        if ($originalLineBreaks > 2 && $translatedLineBreaks === 0) {
            file_put_contents('debug.log', "Gemini15TranslationService: Severe formatting loss detected, attempting restoration\n", FILE_APPEND);
            return $this->attemptFormattingRestoration($originalText, $translatedText);
        }

        // If paragraph structure is lost, attempt to restore it
        if ($originalParagraphs > 2 && $translatedParagraphs === 1) {
            file_put_contents('debug.log', "Gemini15TranslationService: Paragraph structure lost, attempting restoration\n", FILE_APPEND);
            return $this->attemptParagraphRestoration($originalText, $translatedText);
        }

        return $translatedText;
    }

    /**
     * Attempt to restore formatting based on original structure
     */
    private function attemptFormattingRestoration(string $originalText, string $translatedText): string {
        // Simple approach: try to match sentence count and add line breaks accordingly
        $originalSentences = preg_split('/[.!?。！？]+/', $originalText, -1, PREG_SPLIT_NO_EMPTY);
        $translatedSentences = preg_split('/[.!?。！？]+/', $translatedText, -1, PREG_SPLIT_NO_EMPTY);

        if (count($originalSentences) > 1 && count($translatedSentences) > 1) {
            // Add line breaks between sentences if original had them
            $result = '';
            foreach ($translatedSentences as $index => $sentence) {
                $sentence = trim($sentence);
                if (!empty($sentence)) {
                    $result .= $sentence;
                    if ($index < count($translatedSentences) - 1) {
                        $result .= ".\n";
                    } else {
                        $result .= ".";
                    }
                }
            }
            return $result;
        }

        return $translatedText;
    }

    /**
     * Attempt to restore paragraph structure
     */
    private function attemptParagraphRestoration(string $originalText, string $translatedText): string {
        // Split original into paragraphs and try to match with translated content
        $originalParagraphs = preg_split('/\n\s*\n/', $originalText, -1, PREG_SPLIT_NO_EMPTY);

        if (count($originalParagraphs) > 1) {
            // Simple approach: split translated text into roughly equal parts
            $translatedLength = mb_strlen($translatedText);
            $avgParagraphLength = $translatedLength / count($originalParagraphs);

            $sentences = preg_split('/([.!?。！？]+)/', $translatedText, -1, PREG_SPLIT_DELIM_CAPTURE);
            $paragraphs = [];
            $currentParagraph = '';

            for ($i = 0; $i < count($sentences); $i += 2) {
                if (isset($sentences[$i])) {
                    $sentence = $sentences[$i];
                    $punctuation = $sentences[$i + 1] ?? '';

                    $currentParagraph .= $sentence . $punctuation;

                    if (mb_strlen($currentParagraph) >= $avgParagraphLength || $i >= count($sentences) - 2) {
                        $paragraphs[] = trim($currentParagraph);
                        $currentParagraph = '';
                    }
                }
            }

            if (!empty($currentParagraph)) {
                $paragraphs[] = trim($currentParagraph);
            }

            return implode("\n\n", $paragraphs);
        }

        return $translatedText;
    }

    /**
     * Restore punctuation symbols that might have been converted
     */
    protected function restorePunctuationSymbols(string $originalText, string $translatedText): string {
        // Restore Japanese/Chinese punctuation marks that should be preserved
        $punctuationMap = [
            '...' => '…',
            '~' => '〜',
            '!' => '！',
            '?' => '？'
        ];

        // Only restore if original had these punctuation marks
        foreach ($punctuationMap as $western => $eastern) {
            if (strpos($originalText, $eastern) !== false) {
                $translatedText = str_replace($western, $eastern, $translatedText);
            }
        }

        return $translatedText;
    }
}