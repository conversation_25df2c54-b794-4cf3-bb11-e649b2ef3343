<?php
/**
 * Base Gemini Translation Service
 * Shared functionality for all Gemini AI versions
 * Novel Translation Application
 */

abstract class BaseGeminiTranslationService {
    protected $apiKey;
    protected $apiUrl;
    protected $model;
    protected $db;
    protected $currentNovelId;
    protected $chapterChunker;
    protected $soundEffectsService;
    protected $honorificService;
    protected $perspectiveService;
    protected $version; // Gemini version (1.5, 2.0, 2.5)

    public function __construct() {
        $this->apiKey = GEMINI_API_KEY;
        $this->db = Database::getInstance();
        $this->currentNovelId = null;
        $this->chapterChunker = new ChapterChunker();
        $this->soundEffectsService = new SoundEffectsService();
        $this->honorificService = new HonorificService();
        $this->perspectiveService = new PerspectiveService();
        
        // Initialize version-specific configuration
        $this->initializeVersionConfig();
    }

    /**
     * Initialize version-specific configuration (to be implemented by child classes)
     */
    abstract protected function initializeVersionConfig(): void;

    /**
     * Get the Gemini version identifier
     */
    public function getVersion(): string {
        return $this->version;
    }

    /**
     * Translate text using Gemini AI with enhanced timeout handling
     */
    public function translateText(string $text, string $targetLanguage = 'en', string $sourceLanguage = 'auto', array $context = []): array {
        $startTime = microtime(true);

        try {
            // Validate input
            if (empty(trim($text))) {
                throw new Exception('Text to translate cannot be empty');
            }

            // Check if content might cause timeout and suggest chunking
            $contentAnalysis = $this->analyzeContentForTimeout($text, $context);
            if ($contentAnalysis['high_timeout_risk']) {
                error_log("GeminiTranslationService ({$this->version}): High timeout risk detected - " . $contentAnalysis['reason']);

                // If this is a chunk that's still too large, try to split it further
                if (isset($context['chunk_number']) && mb_strlen($text) > 10000) {
                    return $this->handleOversizedChunk($text, $targetLanguage, $sourceLanguage, $context);
                }

                // If this is not a chunk and content is very large, suggest immediate chunking
                if (!isset($context['chunk_number']) && mb_strlen($text) > 20000) {
                    throw new Exception('Content too large for single translation. Auto-chunking will be applied automatically.');
                }
            }

            // Log name dictionary usage for debugging
            if (isset($context['names']) && !empty($context['names'])) {
                // For title translations, skip name dictionary entirely to avoid prompt bloat
                if (isset($context['type']) && $context['type'] === 'title') {
                    file_put_contents('debug.log', "GeminiTranslationService ({$this->version}): Skipping name dictionary for title translation to avoid prompt bloat\n", FILE_APPEND);
                } else {
                    $nameCount = count($context['names']);
                    file_put_contents('debug.log', "GeminiTranslationService ({$this->version}): Using name dictionary with {$nameCount} entries\n", FILE_APPEND);

                    foreach ($context['names'] as $name) {
                        $translation = isset($name['translation']) ? trim($name['translation']) : '';
                        $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';
                        $original = isset($name['original_name']) ? trim($name['original_name']) : '';

                        // Priority: translation > romanization > original (only if not empty)
                        if (!empty($translation)) {
                            $targetName = $translation;
                            $source = 'translation';
                        } elseif (!empty($romanization)) {
                            $targetName = $romanization;
                            $source = 'romanization';
                        } else {
                            $targetName = $original;
                            $source = 'original';
                        }

                        // Skip entries with empty original names or target names
                        if (empty($original) || empty($targetName)) {
                            file_put_contents('debug.log', "GeminiTranslationService ({$this->version}): Skipping empty name entry - original: '{$original}', target: '{$targetName}'\n", FILE_APPEND);
                            continue;
                        }

                        file_put_contents('debug.log', "GeminiTranslationService ({$this->version}): Name mapping - {$original} → {$targetName} (using {$source})\n", FILE_APPEND);
                    }
                }
            }

            // Prepare the prompt
            $prompt = $this->buildTranslationPrompt($text, $targetLanguage, $sourceLanguage, $context, $contentAnalysis);

            // Make API request with adaptive timeout
            $response = $this->makeApiRequest($prompt, $context, $contentAnalysis);

            if (!$response['success']) {
                // Check if this was a timeout and we can retry with smaller chunks
                if ($this->isTimeoutError($response)) {
                    error_log("GeminiTranslationService ({$this->version}): Timeout detected - " . $response['error']);

                    if (!isset($context['chunk_number'])) {
                        // This is not a chunk, suggest auto-chunking
                        throw new Exception('Translation timed out. Content will be automatically split into smaller chunks for processing.');
                    } else {
                        // This is already a chunk that timed out, try emergency splitting
                        if (mb_strlen($text) > 8000) {
                            return $this->handleOversizedChunk($text, $targetLanguage, $sourceLanguage, $context);
                        } else {
                            throw new Exception('Chunk translation timed out despite small size. API may be overloaded.');
                        }
                    }
                }
                throw new Exception($response['error']);
            }

            $translatedText = $this->extractTranslationFromResponse($response['data'], $context);

            // Clean title translations if needed
            if (isset($context['type']) && $context['type'] === 'title') {
                $translatedText = $this->cleanTitleTranslation($translatedText);
            } else {
                // For content translations, ensure formatting is preserved
                $translatedText = $this->restoreFormattingIfLost($text, $translatedText, $context);

                // Restore any punctuation that might have been converted by the API
                $translatedText = $this->restorePunctuationSymbols($text, $translatedText);
            }

            $executionTime = microtime(true) - $startTime;

            return [
                'success' => true,
                'original_text' => $text,
                'translated_text' => trim($translatedText),
                'target_language' => $targetLanguage,
                'source_language' => $sourceLanguage,
                'execution_time' => round($executionTime, 2),
                'api_used' => 'gemini_' . str_replace('.', '', $this->version),
                'model_version' => $this->version
            ];

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2),
                'original_text' => $text,
                'target_language' => $targetLanguage,
                'api_used' => 'gemini_' . str_replace('.', '', $this->version),
                'model_version' => $this->version
            ];
        }
    }

    /**
     * Analyze content for timeout risk (shared implementation)
     */
    protected function analyzeContentForTimeout(string $text, array $context): array {
        $length = mb_strlen($text);
        $analysis = [
            'high_timeout_risk' => false,
            'reason' => '',
            'recommended_action' => '',
            'estimated_time' => 0
        ];

        // Base timeout risk on content length (more conservative thresholds for Gemini)
        if ($length > 12000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 12,000 characters';
            $analysis['recommended_action'] = 'chunk_content';
        } elseif ($length > 10000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 10,000 characters';
            $analysis['recommended_action'] = 'reduce_timeout';
        }

        // Check for complexity factors that increase timeout risk
        $complexityFactors = [];

        // High dialogue density
        $dialogueMatches = preg_match_all('/「[^」]*」|"[^"]*"/', $text);
        if ($dialogueMatches > 25) {
            $complexityFactors[] = 'high_dialogue_density';
        }

        // Technical terms or complex vocabulary
        $kanjiDensity = preg_match_all('/[\x{4e00}-\x{9faf}]/u', $text) / max(1, $length);
        if ($kanjiDensity > 0.25) {
            $complexityFactors[] = 'high_kanji_density';
        }

        // Check for furigana content (more processing intensive)
        if (preg_match('/[\x{3040}-\x{309F}]/u', $text)) {
            $complexityFactors[] = 'furigana_content';
        }

        // Check for long continuous text blocks (harder to process)
        $longBlocks = preg_match_all('/[^\n]{150,}/', $text);
        if ($longBlocks > 5) {
            $complexityFactors[] = 'long_text_blocks';
        }

        // If we have complexity factors and moderate length, increase risk
        if (!empty($complexityFactors) && $length > 6000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Complex content (' . implode(', ', $complexityFactors) . ') with length ' . $length . ' characters';
            $analysis['recommended_action'] = 'chunk_content';
        }

        // Estimate translation time (rough approximation for Gemini)
        $analysis['estimated_time'] = max(20, ($length / 80) * 2); // ~2 seconds per 80 characters

        return $analysis;
    }

    /**
     * Handle oversized chunk by splitting it further
     */
    protected function handleOversizedChunk(string $text, string $targetLanguage, string $sourceLanguage, array $context): array {
        error_log("GeminiTranslationService ({$this->version}): Handling oversized chunk of " . mb_strlen($text) . " characters");

        // Split the chunk into smaller pieces
        $pieces = $this->splitOversizedContent($text);
        $translatedPieces = [];
        $totalTime = 0;

        foreach ($pieces as $index => $piece) {
            $pieceContext = $context;
            $pieceContext['sub_chunk'] = $index + 1;
            $pieceContext['total_sub_chunks'] = count($pieces);

            $result = $this->translateText($piece, $targetLanguage, $sourceLanguage, $pieceContext);

            if (!$result['success']) {
                return $result; // Return error if any piece fails
            }

            $translatedPieces[] = $result['translated_text'];
            $totalTime += $result['execution_time'];
        }

        return [
            'success' => true,
            'original_text' => $text,
            'translated_text' => implode("\n\n", $translatedPieces),
            'target_language' => $targetLanguage,
            'source_language' => $sourceLanguage,
            'execution_time' => $totalTime,
            'sub_chunks_processed' => count($pieces),
            'api_used' => 'gemini_' . str_replace('.', '', $this->version),
            'model_version' => $this->version
        ];
    }

    /**
     * Split oversized content into smaller pieces
     */
    protected function splitOversizedContent(string $text): array {
        $maxPieceSize = 6000; // Smaller pieces for Gemini
        $pieces = [];

        // Try to split by paragraphs first
        $paragraphs = preg_split('/\n\s*\n/', $text, -1, PREG_SPLIT_NO_EMPTY);

        $currentPiece = '';
        foreach ($paragraphs as $paragraph) {
            if (mb_strlen($currentPiece . $paragraph) > $maxPieceSize && !empty($currentPiece)) {
                $pieces[] = trim($currentPiece);
                $currentPiece = $paragraph;
            } else {
                $currentPiece .= ($currentPiece ? "\n\n" : '') . $paragraph;
            }
        }

        if (!empty($currentPiece)) {
            $pieces[] = trim($currentPiece);
        }

        return $pieces;
    }

    /**
     * Check if error indicates a timeout
     */
    protected function isTimeoutError(array $response): bool {
        if (!isset($response['error'])) return false;

        $error = strtolower($response['error']);
        $timeoutIndicators = [
            'timeout',
            'timed out',
            'time limit',
            'deadline exceeded',
            'operation timed out',
            'curl error',
            'request timeout',
            'gateway timeout',
            'connection timeout',
            'read timeout',
            'execution timeout',
            'too large',
            'content too large',
            'content length exceeds',
            'quota exceeded',
            'rate limit'
        ];

        foreach ($timeoutIndicators as $indicator) {
            if (strpos($error, $indicator) !== false) {
                return true;
            }
        }

        // Check for specific HTTP codes that indicate timeout
        if (isset($response['details']['http_code'])) {
            $timeoutCodes = [504, 408, 524, 429]; // Gateway Timeout, Request Timeout, Cloudflare Timeout, Rate Limit
            if (in_array($response['details']['http_code'], $timeoutCodes)) {
                return true;
            }
        }

        return false;
    }

    // Abstract methods to be implemented by child classes
    abstract protected function makeApiRequest(string $prompt, array $context = [], array $contentAnalysis = []): array;
    abstract protected function buildTranslationPrompt(string $text, string $targetLanguage, string $sourceLanguage, array $context, array $contentAnalysis = []): string;
    abstract protected function extractTranslationFromResponse(array $response, array $context = []): string;
    abstract protected function cleanTitleTranslation(string $translatedText): string;
    abstract protected function restoreFormattingIfLost(string $originalText, string $translatedText, array $context): string;
    abstract protected function restorePunctuationSymbols(string $originalText, string $translatedText): string;
}
