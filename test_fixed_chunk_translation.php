<?php
/**
 * Test the fixed chunk translation with POV preference
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/POVPreferenceManager.php';
require_once 'classes/PerspectiveService.php';

echo "=== Testing Fixed Chunk Translation POV Flow ===\n\n";

$db = Database::getInstance();
$povManager = new POVPreferenceManager();

// Get chapter 171 first chunk
$chapter = $db->fetchOne(
    'SELECT id, novel_id FROM chapters WHERE novel_id = 1 AND chapter_number = 171',
    []
);

if (!$chapter) {
    echo "❌ Chapter 171 not found\n";
    exit;
}

$firstChunk = $db->fetchOne(
    'SELECT id, chunk_number, original_content FROM chapter_chunks 
     WHERE chapter_id = ? ORDER BY chunk_number LIMIT 1',
    [$chapter['id']]
);

if (!$firstChunk) {
    echo "❌ No chunks found for chapter 171\n";
    exit;
}

echo "Testing with:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Chunk ID: {$firstChunk['id']}\n";
echo "- Chunk Number: {$firstChunk['chunk_number']}\n\n";

// Simulate the EXACT fixed code from chapter-chunks.php
$chunkId = $firstChunk['id'];
$povPreferenceOverride = null;

// Get chunk info with chapter and novel data for validation and POV preferences
$chunk = $db->fetchOne(
    "SELECT cc.character_count, cc.original_content, cc.translation_status, cc.chapter_id,
            c.novel_id, c.chapter_number
     FROM chapter_chunks cc
     JOIN chapters c ON cc.chapter_id = c.id
     WHERE cc.id = ?",
    [$chunkId]
);

// Get POV preference for this chapter/novel
$povPreference = $povPreferenceOverride ?: $povManager->getChapterPOVPreference($chunk['novel_id'], $chunk['chapter_id']);

echo "=== POV Preference Check ===\n";
echo "POV Preference: " . ($povPreference ?: 'NULL') . "\n\n";

// Create translation context with POV preference
$translationContext = [];
if ($povPreference) {
    $translationContext['user_pov_preference'] = $povPreference;
    echo "✅ POV preference added to translation context: {$povPreference}\n";
} else {
    echo "❌ No POV preference added to translation context\n";
}

// Add narrative context with POV analysis for proper perspective handling (NEW CODE)
if ($povPreference) {
    echo "\n=== Adding Narrative Context (NEW) ===\n";
    
    $perspectiveService = new PerspectiveService();
    
    // Get chunk content for perspective analysis
    $chunkContent = $db->fetchOne(
        "SELECT original_content FROM chapter_chunks WHERE id = ?",
        [$chunkId]
    );
    
    if ($chunkContent && !empty($chunkContent['original_content'])) {
        echo "Chunk content length: " . strlen($chunkContent['original_content']) . " characters\n";
        
        // Generate perspective analysis with POV preference
        $perspectiveAnalysis = $perspectiveService->determineOptimalPerspective(
            $chunkContent['original_content'],
            array_merge($translationContext, ['type' => 'chunk'])
        );
        
        echo "Perspective analysis result:\n";
        echo "- Content Type: {$perspectiveAnalysis['content_type']}\n";
        echo "- Optimal Perspective: {$perspectiveAnalysis['optimal_perspective']}\n";
        echo "- User Selected: " . ($perspectiveAnalysis['user_selected'] ? 'YES' : 'NO') . "\n";
        echo "- Confidence: " . round($perspectiveAnalysis['confidence'] * 100, 1) . "%\n";
        
        // Create narrative context with perspective analysis
        $translationContext['narrative_context'] = [
            'content_type' => $perspectiveAnalysis['content_type'],
            'optimal_perspective' => $perspectiveAnalysis['optimal_perspective'],
            'perspective_reasoning' => $perspectiveAnalysis['reasoning'],
            'perspective_instructions' => $perspectiveAnalysis['instructions'],
            'user_selected' => $perspectiveAnalysis['user_selected'] ?? false
        ];
        
        echo "✅ Narrative context added with POV analysis\n";
        echo "- Instructions preview: " . substr($perspectiveAnalysis['instructions'], 0, 100) . "...\n";
    } else {
        echo "❌ Could not get chunk content for perspective analysis\n";
    }
} else {
    echo "\n❌ No POV preference - skipping narrative context creation\n";
}

echo "\n=== Final Translation Context ===\n";
echo "Translation context keys: " . implode(', ', array_keys($translationContext)) . "\n";

if (isset($translationContext['narrative_context'])) {
    $narrativeContext = $translationContext['narrative_context'];
    echo "Narrative context keys: " . implode(', ', array_keys($narrativeContext)) . "\n";
    echo "Optimal perspective: {$narrativeContext['optimal_perspective']}\n";
    echo "User selected: " . ($narrativeContext['user_selected'] ? 'YES' : 'NO') . "\n";
} else {
    echo "❌ No narrative context in translation context\n";
}

// Test if DeepSeek service would use this context correctly
echo "\n=== Testing DeepSeek Service Integration ===\n";

try {
    require_once 'classes/DeepSeekTranslationService.php';
    $deepSeekService = new DeepSeekTranslationService();
    
    // Test the buildTranslationPrompt method with our context
    $testText = substr($chunkContent['original_content'], 0, 200);
    echo "Test text (first 200 chars): " . $testText . "\n\n";
    
    // Use reflection to call the protected method
    $reflection = new ReflectionClass($deepSeekService);
    $method = $reflection->getMethod('buildTranslationPrompt');
    $method->setAccessible(true);
    
    $prompt = $method->invoke($deepSeekService, $testText, 'en', 'auto', $translationContext, []);
    
    echo "Generated prompt preview (first 500 chars):\n";
    echo substr($prompt, 0, 500) . "...\n\n";
    
    // Check if POV instructions are in the prompt
    if (strpos($prompt, 'THIRD PERSON OMNISCIENT') !== false) {
        echo "✅ POV instructions found in prompt!\n";
    } elseif (strpos($prompt, 'PERSPECTIVE') !== false) {
        echo "⚠️ Some perspective instructions found in prompt\n";
    } else {
        echo "❌ No POV instructions found in prompt\n";
    }
    
    // Check for specific third-person omniscient instructions
    if (strpos($prompt, 'Convert first-person pronouns') !== false) {
        echo "✅ First-person to third-person conversion instructions found!\n";
    } else {
        echo "❌ No first-person conversion instructions found\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing DeepSeek service: " . $e->getMessage() . "\n";
}

echo "\nDone.\n";
