<?php
/**
 * Test DeepSeek Family Term Translation with Name Dictionary
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/AIProviderManager.php';

echo "=== Testing DeepSeek Family Term Translation ===\n\n";

// Test text with family terms
$testText = "「父さん、お疲れ様です」と言った。母さんは微笑んだ。お兄ちゃんも来た。";

echo "Test text: {$testText}\n";
echo "Expected: Family terms should use romanized forms like 'Tou-san', 'Kaa-san', 'Onii-chan'\n\n";

try {
    $providerManager = new AIProviderManager();
    
    echo "--- Testing DeepSeek ---\n";
    
    // Set the active provider to DeepSeek
    $providerManager->setActiveProvider('deepseek');
    
    // Get the translation service
    $translationService = $providerManager->getTranslationService('deepseek');
    
    // Prepare context with name dictionary for novel 7
    $db = Database::getInstance();
    $names = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         ORDER BY frequency DESC',
        []
    );
    
    $context = [
        'type' => 'test',
        'novel_id' => 7,
        'names' => $names
    ];
    
    echo "Using name dictionary with " . count($names) . " entries\n";
    
    // Show top family terms in dictionary
    echo "Top family terms in dictionary:\n";
    foreach ($names as $name) {
        $original = $name['original_name'];
        if (preg_match('/(?:父|母|兄|姉)(?:さん|ちゃん)?/', $original)) {
            echo "  - {$original} → '{$name['translation']}'\n";
        }
    }
    echo "\n";
    
    // Perform translation
    $result = $translationService->translateText(
        $testText,
        'en',
        'ja',
        $context
    );
    
    if ($result['success']) {
        echo "Translation: {$result['translated_text']}\n\n";
        
        // Check if family terms are correctly translated (case-insensitive)
        $translation = $result['translated_text'];
        $translationLower = strtolower($translation);
        $hasCorrectTousan = strpos($translationLower, 'tou-san') !== false;
        $hasCorrectKaasan = strpos($translationLower, 'kaa-san') !== false;
        $hasCorrectOniichan = strpos($translationLower, 'onii-chan') !== false;
        $hasIncorrectFatherSan = strpos($translationLower, 'father-san') !== false;
        $hasIncorrectMotherSan = strpos($translationLower, 'mother-san') !== false;
        $hasIncorrectBrotherChan = strpos($translationLower, 'brother-chan') !== false;
        
        echo "Analysis:\n";
        echo "  ✓ Contains 'Tou-san': " . ($hasCorrectTousan ? 'YES' : 'NO') . "\n";
        echo "  ✓ Contains 'Kaa-san': " . ($hasCorrectKaasan ? 'YES' : 'NO') . "\n";
        echo "  ✓ Contains 'Onii-chan': " . ($hasCorrectOniichan ? 'YES' : 'NO') . "\n";
        echo "  ✗ Contains 'father-san': " . ($hasIncorrectFatherSan ? 'YES (BAD)' : 'NO (GOOD)') . "\n";
        echo "  ✗ Contains 'mother-san': " . ($hasIncorrectMotherSan ? 'YES (BAD)' : 'NO (GOOD)') . "\n";
        echo "  ✗ Contains 'brother-chan': " . ($hasIncorrectBrotherChan ? 'YES (BAD)' : 'NO (GOOD)') . "\n";
        
        $correctCount = ($hasCorrectTousan ? 1 : 0) + ($hasCorrectKaasan ? 1 : 0) + ($hasCorrectOniichan ? 1 : 0);
        $incorrectCount = ($hasIncorrectFatherSan ? 1 : 0) + ($hasIncorrectMotherSan ? 1 : 0) + ($hasIncorrectBrotherChan ? 1 : 0);
        
        if ($correctCount == 3 && $incorrectCount == 0) {
            echo "  🎉 PERFECT: All family terms correctly romanized!\n";
        } elseif ($correctCount >= 2 && $incorrectCount == 0) {
            echo "  ✅ GOOD: Most family terms correctly romanized\n";
        } elseif ($correctCount >= 1) {
            echo "  ⚠️  PARTIAL: Some family terms correctly romanized\n";
        } else {
            echo "  ❌ ISSUE: Family terms not using correct romanized format\n";
        }
        
        // Check for broken fragments (isolated honorifics without family terms)
        // This should NOT match complete family terms like "Tou-san", "Kaa-san", "Onii-chan"
        $hasBrokenFragments = preg_match('/(?<!Tou|Kaa|Onii|Onee|Otou|Okaa|Nii|Oji|Oba)\b-(?:san|chan|kun|sama)\b/', $translation);
        if ($hasBrokenFragments) {
            echo "  ⚠️  WARNING: Found isolated honorific fragments in translation\n";
        } else {
            echo "  ✅ GOOD: No broken honorific fragments detected\n";
        }
        
    } else {
        echo "Translation failed: {$result['error']}\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}

echo "\n=== DeepSeek Test Complete ===\n";
