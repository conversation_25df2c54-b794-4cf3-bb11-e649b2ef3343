<?php
/**
 * Test chunk translation with POV preference
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/POVPreferenceManager.php';

echo "=== Testing Chunk Translation POV Flow ===\n\n";

$db = Database::getInstance();
$povManager = new POVPreferenceManager();

// Get chapter 171 chunks
$chapter = $db->fetchOne(
    'SELECT id, novel_id FROM chapters WHERE novel_id = 1 AND chapter_number = 171',
    []
);

if (!$chapter) {
    echo "❌ Chapter 171 not found\n";
    exit;
}

echo "Chapter 171 ID: {$chapter['id']}\n";
echo "Novel ID: {$chapter['novel_id']}\n\n";

// Check POV preference
$novelPOV = $povManager->getNovelPOVPreference(1);
$chapterPOV = $povManager->getChapterPOVPreference(1, $chapter['id']);

echo "=== POV Preferences ===\n";
echo "Novel POV: " . ($novelPOV ?: 'NULL') . "\n";
echo "Chapter POV: " . ($chapterPOV ?: 'NULL') . "\n\n";

// Get chunks for this chapter
$chunks = $db->fetchAll(
    'SELECT id, chunk_number, original_content, translated_content, translation_status, updated_at
     FROM chapter_chunks 
     WHERE chapter_id = ? 
     ORDER BY chunk_number',
    [$chapter['id']]
);

if (empty($chunks)) {
    echo "❌ No chunks found for chapter 171\n";
    exit;
}

echo "=== Chapter Chunks ===\n";
echo "Found " . count($chunks) . " chunks:\n";
foreach ($chunks as $chunk) {
    echo "- Chunk {$chunk['chunk_number']}: {$chunk['translation_status']} (updated: {$chunk['updated_at']})\n";
}

// Test the exact flow that chapter-chunks.php uses
echo "\n=== Testing Chapter-Chunks API Flow ===\n";

$testChunk = $chunks[0]; // Test with first chunk
echo "Testing with chunk {$testChunk['chunk_number']} (ID: {$testChunk['id']})\n";

// Simulate the exact code from chapter-chunks.php
$chunkId = $testChunk['id'];
$povPreferenceOverride = null; // No override from request

// Get chunk info with chapter and novel data for validation and POV preferences
$chunk = $db->fetchOne(
    "SELECT cc.character_count, cc.original_content, cc.translation_status, cc.chapter_id,
            c.novel_id, c.chapter_number
     FROM chapter_chunks cc
     JOIN chapters c ON cc.chapter_id = c.id
     WHERE cc.id = ?",
    [$chunkId]
);

if (!$chunk) {
    echo "❌ Chunk not found in join query\n";
    exit;
}

echo "Chunk data from join:\n";
echo "- Novel ID: {$chunk['novel_id']}\n";
echo "- Chapter ID: {$chunk['chapter_id']}\n";
echo "- Chapter Number: {$chunk['chapter_number']}\n";

// Get POV preference for this chunk (exact code from chapter-chunks.php)
$povManager = new POVPreferenceManager();
$povPreference = $povPreferenceOverride ?: $povManager->getChapterPOVPreference($chunk['novel_id'], $chunk['chapter_id']);

echo "\nPOV preference retrieval:\n";
echo "- Override: " . ($povPreferenceOverride ?: 'NULL') . "\n";
echo "- From database: " . ($povPreference ?: 'NULL') . "\n";

// Create translation context with POV preference (exact code from chapter-chunks.php)
$translationContext = [];
if ($povPreference) {
    $translationContext['user_pov_preference'] = $povPreference;
    echo "✅ POV preference added to translation context: {$povPreference}\n";
} else {
    echo "❌ No POV preference added to translation context\n";
}

echo "\nTranslation context: " . json_encode($translationContext) . "\n";

// Test if the perspective service would work with this context
if (!empty($translationContext['user_pov_preference'])) {
    echo "\n=== Testing PerspectiveService with Context ===\n";
    
    require_once 'classes/PerspectiveService.php';
    $perspectiveService = new PerspectiveService();
    
    $testContent = substr($testChunk['original_content'], 0, 200);
    echo "Test content (first 200 chars): " . $testContent . "\n\n";
    
    try {
        $result = $perspectiveService->determineOptimalPerspective($testContent, $translationContext);
        
        echo "PerspectiveService result:\n";
        echo "- Content Type: {$result['content_type']}\n";
        echo "- Optimal Perspective: {$result['optimal_perspective']}\n";
        echo "- User Selected: " . ($result['user_selected'] ? 'YES' : 'NO') . "\n";
        echo "- Confidence: " . round($result['confidence'] * 100, 1) . "%\n";
        
        if (isset($result['instructions'])) {
            echo "- Instructions preview: " . substr($result['instructions'], 0, 100) . "...\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error testing PerspectiveService: " . $e->getMessage() . "\n";
    }
} else {
    echo "\n❌ Cannot test PerspectiveService - no POV preference in context\n";
}

// Check recent debug logs for this chunk
echo "\n=== Debug Log Analysis ===\n";
$debugLogPath = 'debug.log';
if (file_exists($debugLogPath)) {
    $debugContent = file_get_contents($debugLogPath);
    $lines = explode("\n", $debugContent);
    
    $recentLines = array_slice($lines, -50); // Last 50 lines
    $chunkRelatedLines = [];
    
    foreach ($recentLines as $line) {
        if (stripos($line, 'chunk') !== false || stripos($line, 'pov') !== false || stripos($line, 'perspective') !== false) {
            $chunkRelatedLines[] = $line;
        }
    }
    
    if (!empty($chunkRelatedLines)) {
        echo "Recent chunk/POV related log entries:\n";
        foreach ($chunkRelatedLines as $line) {
            echo "- " . trim($line) . "\n";
        }
    } else {
        echo "No recent chunk/POV related log entries found\n";
    }
} else {
    echo "Debug log file not found\n";
}

echo "\nDone.\n";
