<?php
/**
 * Analyze why we're getting mixed results (some correct, some incorrect)
 */

require_once 'config/config.php';
require_once 'config/database.php';

echo "=== Analyzing Mixed Translation Results ===\n\n";

$db = Database::getInstance();

// Get the current translation
$chapter = $db->fetchOne(
    'SELECT translated_content FROM chapters WHERE novel_id = 7 AND chapter_number = 56',
    []
);

$translatedContent = $chapter['translated_content'];

echo "Analyzing current translation...\n";
echo "Content length: " . strlen($translatedContent) . " characters\n\n";

// Find all instances of family terms (both correct and incorrect)
$familyTermInstances = [];

// Correct romanized terms
$correctTerms = ['Otou-san', 'Okaa-san', 'Onii-san', 'Onee-san', 'Tou-san', 'Kaa-san', 'Nii-san'];
foreach ($correctTerms as $term) {
    $offset = 0;
    while (($pos = strpos($translatedContent, $term, $offset)) !== false) {
        $start = max(0, $pos - 80);
        $end = min(strlen($translatedContent), $pos + strlen($term) + 80);
        $context = substr($translatedContent, $start, $end - $start);
        
        $familyTermInstances[] = [
            'term' => $term,
            'type' => 'CORRECT',
            'position' => $pos,
            'context' => $context
        ];
        
        $offset = $pos + 1;
    }
}

// Incorrect English terms
$incorrectTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
foreach ($incorrectTerms as $term) {
    $offset = 0;
    while (($pos = stripos($translatedContent, $term, $offset)) !== false) {
        $beforeChar = $pos > 0 ? $translatedContent[$pos - 1] : ' ';
        $afterChar = $pos + strlen($term) < strlen($translatedContent) ? $translatedContent[$pos + strlen($term)] : ' ';
        
        // Check if it's a standalone word
        if (!ctype_alnum($beforeChar) && !ctype_alnum($afterChar)) {
            $start = max(0, $pos - 80);
            $end = min(strlen($translatedContent), $pos + strlen($term) + 80);
            $context = substr($translatedContent, $start, $end - $start);
            
            $familyTermInstances[] = [
                'term' => $term,
                'type' => 'INCORRECT',
                'position' => $pos,
                'context' => $context
            ];
        }
        
        $offset = $pos + 1;
    }
}

// Sort by position to see the pattern
usort($familyTermInstances, function($a, $b) {
    return $a['position'] - $b['position'];
});

echo "=== Family Term Analysis (in order of appearance) ===\n";
foreach ($familyTermInstances as $i => $instance) {
    $status = $instance['type'] === 'CORRECT' ? '✅' : '❌';
    echo "Instance " . ($i + 1) . " {$status}: '{$instance['term']}' at position {$instance['position']}\n";
    echo "Context: ...{$instance['context']}...\n\n";
}

// Analyze patterns
echo "=== Pattern Analysis ===\n";
$correctCount = 0;
$incorrectCount = 0;
$correctPositions = [];
$incorrectPositions = [];

foreach ($familyTermInstances as $instance) {
    if ($instance['type'] === 'CORRECT') {
        $correctCount++;
        $correctPositions[] = $instance['position'];
    } else {
        $incorrectCount++;
        $incorrectPositions[] = $instance['position'];
    }
}

echo "Correct romanized terms: {$correctCount}\n";
echo "Incorrect English terms: {$incorrectCount}\n\n";

// Check if there's a pattern in positioning
if (!empty($correctPositions) && !empty($incorrectPositions)) {
    $avgCorrectPos = array_sum($correctPositions) / count($correctPositions);
    $avgIncorrectPos = array_sum($incorrectPositions) / count($incorrectPositions);
    
    echo "Average position of correct terms: " . round($avgCorrectPos) . "\n";
    echo "Average position of incorrect terms: " . round($avgIncorrectPos) . "\n";
    
    if ($avgCorrectPos < $avgIncorrectPos) {
        echo "🔍 PATTERN: Correct terms appear earlier, incorrect terms appear later\n";
        echo "This suggests the translation system might be inconsistent across the chapter\n";
    } elseif ($avgIncorrectPos < $avgCorrectPos) {
        echo "🔍 PATTERN: Incorrect terms appear earlier, correct terms appear later\n";
        echo "This suggests the system was fixed partway through translation\n";
    } else {
        echo "🔍 PATTERN: Terms are mixed throughout the chapter\n";
        echo "This suggests inconsistent behavior within the same translation session\n";
    }
}

// Check for potential chunking
echo "\n=== Chunking Analysis ===\n";
$contentLength = strlen($translatedContent);
$chunkSize = 2000; // Typical chunk size
$estimatedChunks = ceil($contentLength / $chunkSize);

echo "Content length: {$contentLength} characters\n";
echo "Estimated chunks (if chunked): {$estimatedChunks}\n";

if ($estimatedChunks > 1) {
    echo "⚠️ Content is large enough to potentially be chunked\n";
    echo "Different chunks might use different translation methods\n";
    
    // Analyze family terms by chunk
    for ($chunk = 0; $chunk < $estimatedChunks; $chunk++) {
        $chunkStart = $chunk * $chunkSize;
        $chunkEnd = min($contentLength, ($chunk + 1) * $chunkSize);
        
        $correctInChunk = 0;
        $incorrectInChunk = 0;
        
        foreach ($familyTermInstances as $instance) {
            if ($instance['position'] >= $chunkStart && $instance['position'] < $chunkEnd) {
                if ($instance['type'] === 'CORRECT') {
                    $correctInChunk++;
                } else {
                    $incorrectInChunk++;
                }
            }
        }
        
        if ($correctInChunk > 0 || $incorrectInChunk > 0) {
            echo "Chunk " . ($chunk + 1) . " ({$chunkStart}-{$chunkEnd}): ";
            echo "✅{$correctInChunk} ❌{$incorrectInChunk}\n";
        }
    }
}

echo "\n=== Diagnosis ===\n";
if ($correctCount > 0 && $incorrectCount > 0) {
    echo "🔍 MIXED RESULTS CONFIRMED:\n";
    echo "- Some family terms are correctly romanized\n";
    echo "- Some family terms are incorrectly in English\n";
    echo "- This suggests inconsistent application of the fixed instructions\n";
    echo "- Possible causes:\n";
    echo "  1. Different translation methods used for different parts\n";
    echo "  2. Chunked translation with inconsistent prompts\n";
    echo "  3. Fallback systems with different instructions\n";
    echo "  4. Caching of old translations mixed with new ones\n";
} elseif ($correctCount > 0) {
    echo "✅ ALL CORRECT: Family terms are properly romanized\n";
} else {
    echo "❌ ALL INCORRECT: Family terms are in English\n";
}

echo "\n=== Analysis Complete ===\n";
