<?php
/**
 * Clean and re-translate Chapter 56 completely
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

echo "=== Clean and Re-translate Chapter 56 ===\n\n";

$db = Database::getInstance();

// Get chapter 56
$chapter = $db->fetchOne(
    'SELECT id, novel_id, chapter_number, original_content 
     FROM chapters 
     WHERE novel_id = 7 AND chapter_number = 56',
    []
);

if (!$chapter) {
    echo "❌ Chapter 56 not found\n";
    exit;
}

echo "Chapter 56 found:\n";
echo "- Chapter ID: {$chapter['id']}\n";
echo "- Original content length: " . strlen($chapter['original_content']) . " characters\n\n";

// Step 1: Complete cleanup
echo "=== Step 1: Complete Cleanup ===\n";

// Clear chunks
$db->query('DELETE FROM chapter_chunks WHERE chapter_id = ?', [$chapter['id']]);
echo "✅ Cleared chunks\n";

// Clear any translation logs
$db->query('DELETE FROM translation_logs WHERE chapter_id = ?', [$chapter['id']]);
echo "✅ Cleared translation logs\n";

// Reset chapter completely
$db->query(
    'UPDATE chapters SET 
     translated_content = "", 
     translated_title = "",
     translation_status = "pending",
     updated_at = NOW() 
     WHERE id = ?',
    [$chapter['id']]
);
echo "✅ Reset chapter completely\n\n";

// Step 2: Validate original content
echo "=== Step 2: Content Validation ===\n";

$originalContent = $chapter['original_content'];

// Check for problematic characters that might cause HTTP 400
$problematicPatterns = [
    '/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', // Control characters
    '/[\x{FEFF}\x{FFFF}]/u', // BOM and invalid Unicode
    '/[\x{D800}-\x{DFFF}]/u', // Surrogate pairs
];

$hasProblems = false;
foreach ($problematicPatterns as $pattern) {
    if (preg_match($pattern, $originalContent)) {
        echo "⚠️ Found problematic characters: {$pattern}\n";
        $hasProblems = true;
    }
}

if (!$hasProblems) {
    echo "✅ Content validation passed\n";
}

// Check content length and complexity
echo "Content analysis:\n";
echo "- Length: " . strlen($originalContent) . " characters\n";
echo "- MB Length: " . mb_strlen($originalContent) . " characters\n";
echo "- Lines: " . substr_count($originalContent, "\n") . "\n";
echo "- Family terms: " . preg_match_all('/[父母兄姉]/', $originalContent) . "\n\n";

// Step 3: Test translation with smaller chunks first
echo "=== Step 3: Incremental Translation Test ===\n";

try {
    $translationService = new TranslationService();
    
    // Get family terms
    $familyNames = $db->fetchAll(
        'SELECT original_name, romanization, translation, name_type 
         FROM name_dictionary 
         WHERE novel_id = 7 
         ORDER BY frequency DESC',
        []
    );
    
    $context = [
        'type' => 'chapter',
        'novel_id' => 7,
        'names' => $familyNames
    ];
    
    echo "Using name dictionary with " . count($familyNames) . " entries\n";
    
    // Test with progressively larger chunks
    $testSizes = [500, 1000, 2000];
    $maxWorkingSize = 0;
    
    foreach ($testSizes as $size) {
        echo "\nTesting chunk size: {$size} characters\n";
        
        $testChunk = substr($originalContent, 0, $size);
        
        $result = $translationService->translateText(
            $testChunk,
            'en',
            'ja',
            $context
        );
        
        if ($result['success']) {
            echo "✅ Size {$size}: SUCCESS\n";
            $maxWorkingSize = $size;
            
            // Check for family term issues
            $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
            $foundForbidden = [];
            
            foreach ($forbiddenTerms as $term) {
                if (stripos($result['translated_text'], $term) !== false) {
                    $foundForbidden[] = $term;
                }
            }
            
            if (empty($foundForbidden)) {
                echo "✅ No forbidden family terms\n";
            } else {
                echo "❌ Found forbidden terms: " . implode(', ', $foundForbidden) . "\n";
            }
            
        } else {
            echo "❌ Size {$size}: FAILED - " . $result['error'] . "\n";
            break;
        }
    }
    
    if ($maxWorkingSize > 0) {
        echo "\n✅ Maximum working chunk size: {$maxWorkingSize} characters\n";
        
        // Step 4: Translate in working chunks
        echo "\n=== Step 4: Chunked Translation ===\n";
        
        $chunkSize = $maxWorkingSize;
        $translatedChunks = [];
        $totalChunks = ceil(strlen($originalContent) / $chunkSize);
        
        echo "Translating in {$totalChunks} chunks of {$chunkSize} characters each\n\n";
        
        for ($i = 0; $i < $totalChunks; $i++) {
            $start = $i * $chunkSize;
            $chunk = substr($originalContent, $start, $chunkSize);
            
            echo "Chunk " . ($i + 1) . "/{$totalChunks} (chars {$start}-" . ($start + strlen($chunk)) . ")\n";
            
            $chunkContext = $context;
            $chunkContext['chunk_number'] = $i + 1;
            $chunkContext['total_chunks'] = $totalChunks;
            
            $result = $translationService->translateText(
                $chunk,
                'en',
                'ja',
                $chunkContext
            );
            
            if ($result['success']) {
                $translatedChunks[] = $result['translated_text'];
                echo "✅ Chunk " . ($i + 1) . " translated successfully\n";
                
                // Check for family term issues in this chunk
                $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
                $foundForbidden = [];
                
                foreach ($forbiddenTerms as $term) {
                    if (stripos($result['translated_text'], $term) !== false) {
                        $foundForbidden[] = $term;
                    }
                }
                
                if (!empty($foundForbidden)) {
                    echo "⚠️ Chunk " . ($i + 1) . " has forbidden terms: " . implode(', ', $foundForbidden) . "\n";
                }
                
            } else {
                echo "❌ Chunk " . ($i + 1) . " failed: " . $result['error'] . "\n";
                throw new Exception("Chunk translation failed");
            }
            
            // Small delay between chunks
            usleep(500000); // 0.5 second
        }
        
        // Step 5: Reassemble and save
        echo "\n=== Step 5: Reassemble and Save ===\n";
        
        $finalTranslation = implode("\n\n", $translatedChunks);
        
        // Update chapter with new translation
        $db->query(
            'UPDATE chapters SET 
             translated_content = ?, 
             translation_status = "completed",
             updated_at = NOW() 
             WHERE id = ?',
            [$finalTranslation, $chapter['id']]
        );
        
        echo "✅ Chapter 56 updated with new translation\n";
        echo "Final translation length: " . strlen($finalTranslation) . " characters\n";
        
        // Final validation
        echo "\n=== Final Validation ===\n";
        
        $forbiddenTerms = ['father', 'mother', 'brother', 'sister', 'mom', 'dad'];
        $finalProblems = [];
        
        foreach ($forbiddenTerms as $term) {
            $count = substr_count(strtolower($finalTranslation), strtolower($term));
            if ($count > 0) {
                $finalProblems[] = "{$term}: {$count}";
            }
        }
        
        if (empty($finalProblems)) {
            echo "🎉 SUCCESS: No English family terms in final translation!\n";
        } else {
            echo "❌ Final translation still has issues: " . implode(', ', $finalProblems) . "\n";
        }
        
        // Show sample
        echo "\nSample of final translation:\n";
        echo substr($finalTranslation, 0, 500) . "...\n";
        
    } else {
        echo "❌ No working chunk size found - content has fundamental issues\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error during translation: " . $e->getMessage() . "\n";
}

echo "\n=== Clean and Re-translate Complete ===\n";
