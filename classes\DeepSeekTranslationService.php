<?php
/**
 * Translation Service using DeepSeek AI
 * Novel Translation Application
 */

class DeepSeekTranslationService {
    private $apiKey;
    private $apiUrl;
    private $model;
    private $db;
    private $currentNovelId;
    private $chapterChunker;
    private $soundEffectsService;
    private $honorificService;
    private $cacheOptimizer;
    private $perspectiveService;

    public function __construct() {
        $this->apiKey = DEEPSEEK_API_KEY;
        $this->apiUrl = DEEPSEEK_API_URL;
        $this->model = DEEPSEEK_CHAT_MODEL;
        $this->db = Database::getInstance();
        $this->currentNovelId = null;
        $this->chapterChunker = new ChapterChunker();
        $this->soundEffectsService = new SoundEffectsService();
        $this->honorificService = new HonorificService();
        $this->cacheOptimizer = new CacheOptimizationService();
        $this->perspectiveService = new PerspectiveService();
    }

    /**
     * Translate text using DeepSeek AI with enhanced timeout handling
     */
    public function translateText(string $text, string $targetLanguage = 'en', string $sourceLanguage = 'auto', array $context = []): array {
        $startTime = microtime(true);

        try {
            // Validate input
            if (empty(trim($text))) {
                throw new Exception('Text to translate cannot be empty');
            }

            // Check if content might cause timeout and suggest chunking
            $contentAnalysis = $this->analyzeContentForTimeout($text, $context);
            if ($contentAnalysis['high_timeout_risk']) {
                error_log("DeepSeekTranslationService: High timeout risk detected - " . $contentAnalysis['reason']);

                // If this is a chunk that's still too large, try to split it further
                if (isset($context['chunk_number']) && mb_strlen($text) > 10000) {
                    return $this->handleOversizedChunk($text, $targetLanguage, $sourceLanguage, $context);
                }

                // If this is not a chunk and content is very large, suggest immediate chunking
                if (!isset($context['chunk_number']) && mb_strlen($text) > 20000) {
                    throw new Exception('Content too large for single translation. Auto-chunking will be applied automatically.');
                }
            }

            // Log name dictionary usage for debugging
            if (isset($context['names']) && !empty($context['names'])) {
                // For title translations, skip name dictionary entirely to avoid prompt bloat
                if (isset($context['type']) && $context['type'] === 'title') {
                    file_put_contents('debug.log', "DeepSeekTranslationService: Skipping name dictionary for title translation to avoid prompt bloat\n", FILE_APPEND);
                } else {
                    $nameCount = count($context['names']);
                    file_put_contents('debug.log', "DeepSeekTranslationService: Using name dictionary with {$nameCount} entries\n", FILE_APPEND);

                    foreach ($context['names'] as $name) {
                        $translation = isset($name['translation']) ? trim($name['translation']) : '';
                        $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';
                        $original = isset($name['original_name']) ? trim($name['original_name']) : '';

                        // Priority: translation > romanization > original (only if not empty)
                        if (!empty($translation)) {
                            $targetName = $translation;
                            $source = 'translation';
                        } elseif (!empty($romanization)) {
                            $targetName = $romanization;
                            $source = 'romanization';
                        } else {
                            $targetName = $original;
                            $source = 'original';
                        }

                        // Skip entries with empty original names or target names
                        if (empty($original) || empty($targetName)) {
                            file_put_contents('debug.log', "DeepSeekTranslationService: Skipping empty name entry - original: '{$original}', target: '{$targetName}'\n", FILE_APPEND);
                            continue;
                        }

                        file_put_contents('debug.log', "DeepSeekTranslationService: Name mapping - {$original} → {$targetName} (using {$source})\n", FILE_APPEND);
                    }
                }
            }

            // Prepare the prompt with timeout-optimized instructions and cache optimization
            $basePrompt = $this->buildTranslationPrompt($text, $targetLanguage, $sourceLanguage, $context, $contentAnalysis);
            $prompt = $this->cacheOptimizer->optimizePromptForCaching($basePrompt, $context);

            // Make API request with adaptive timeout
            $response = $this->makeApiRequest($prompt, $context, $contentAnalysis);

            if (!$response['success']) {
                // Check if this was a timeout and we can retry with smaller chunks
                if ($this->isTimeoutError($response)) {
                    error_log("DeepSeekTranslationService: Timeout detected - " . $response['error']);

                    if (!isset($context['chunk_number'])) {
                        // This is not a chunk, suggest auto-chunking
                        throw new Exception('Translation timed out. Content will be automatically split into smaller chunks for processing.');
                    } else {
                        // This is already a chunk that timed out, try emergency splitting
                        if (mb_strlen($text) > 8000) {
                            return $this->handleOversizedChunk($text, $targetLanguage, $sourceLanguage, $context);
                        } else {
                            throw new Exception('Chunk translation timed out despite small size. API may be overloaded.');
                        }
                    }
                }
                throw new Exception($response['error']);
            }

            $translatedText = $this->extractTranslationFromResponse($response['data'], $context);

            // Clean title translations if needed
            if (isset($context['type']) && $context['type'] === 'title') {
                $translatedText = $this->cleanTitleTranslation($translatedText);
            } else {
                // For content translations, ensure formatting is preserved
                $translatedText = $this->restoreFormattingIfLost($text, $translatedText, $context);

                // Restore any punctuation that might have been converted by the API
                $translatedText = $this->restorePunctuationSymbols($text, $translatedText);
            }

            $executionTime = microtime(true) - $startTime;

            // Extract cache hit information from response
            $cacheInfo = $this->extractCacheInfo($response['data']);

            return [
                'success' => true,
                'original_text' => $text,
                'translated_text' => trim($translatedText),
                'target_language' => $targetLanguage,
                'source_language' => $sourceLanguage,
                'execution_time' => round($executionTime, 2),
                'cache_hit_tokens' => $cacheInfo['cache_hit_tokens'],
                'cache_miss_tokens' => $cacheInfo['cache_miss_tokens'],
                'total_tokens' => $cacheInfo['total_tokens'],
                'cache_hit_rate' => $cacheInfo['cache_hit_rate'],
                'estimated_cost_savings' => $cacheInfo['estimated_cost_savings']
            ];

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2),
                'original_text' => $text,
                'target_language' => $targetLanguage
            ];
        }
    }

    /**
     * Analyze content for timeout risk
     */
    private function analyzeContentForTimeout(string $text, array $context): array {
        $length = mb_strlen($text);
        $analysis = [
            'high_timeout_risk' => false,
            'reason' => '',
            'recommended_action' => '',
            'estimated_time' => 0
        ];

        // Base timeout risk on content length (more conservative thresholds)
        if ($length > 15000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 15,000 characters';
            $analysis['recommended_action'] = 'chunk_content';
        } elseif ($length > 12000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 12,000 characters';
            $analysis['recommended_action'] = 'reduce_timeout';
        }

        // Check for complexity factors that increase timeout risk
        $complexityFactors = [];

        // High dialogue density (more conservative threshold)
        $dialogueMatches = preg_match_all('/「[^」]*」|"[^"]*"/', $text);
        if ($dialogueMatches > 30) {
            $complexityFactors[] = 'high_dialogue_density';
        }

        // Technical terms or complex vocabulary
        $kanjiDensity = preg_match_all('/[\x{4e00}-\x{9faf}]/u', $text) / max(1, $length);
        if ($kanjiDensity > 0.3) { // More conservative threshold
            $complexityFactors[] = 'high_kanji_density';
        }

        // Check for furigana content (more processing intensive)
        if (preg_match('/[\x{3040}-\x{309F}]/u', $text)) {
            $complexityFactors[] = 'furigana_content';
        }

        // Check for long continuous text blocks (harder to process)
        $longBlocks = preg_match_all('/[^\n]{200,}/', $text);
        if ($longBlocks > 5) {
            $complexityFactors[] = 'long_text_blocks';
        }

        // If we have complexity factors and moderate length, increase risk (more conservative)
        if (!empty($complexityFactors) && $length > 8000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Complex content (' . implode(', ', $complexityFactors) . ') with length ' . $length . ' characters';
            $analysis['recommended_action'] = 'chunk_content';
        }

        // Estimate translation time (rough approximation)
        $analysis['estimated_time'] = max(30, ($length / 100) * 2); // ~2 seconds per 100 characters

        return $analysis;
    }

    /**
     * Handle oversized chunk by splitting it further
     */
    private function handleOversizedChunk(string $text, string $targetLanguage, string $sourceLanguage, array $context): array {
        error_log("DeepSeekTranslationService: Handling oversized chunk of " . mb_strlen($text) . " characters");

        // Split the chunk into smaller pieces
        $pieces = $this->splitOversizedContent($text);
        $translatedPieces = [];
        $totalTime = 0;

        foreach ($pieces as $index => $piece) {
            $pieceContext = $context;
            $pieceContext['sub_chunk'] = $index + 1;
            $pieceContext['total_sub_chunks'] = count($pieces);

            $result = $this->translateText($piece, $targetLanguage, $sourceLanguage, $pieceContext);

            if (!$result['success']) {
                return $result; // Return error if any piece fails
            }

            $translatedPieces[] = $result['translated_text'];
            $totalTime += $result['execution_time'];
        }

        return [
            'success' => true,
            'original_text' => $text,
            'translated_text' => implode("\n\n", $translatedPieces),
            'target_language' => $targetLanguage,
            'source_language' => $sourceLanguage,
            'execution_time' => $totalTime,
            'sub_chunks_processed' => count($pieces)
        ];
    }

    /**
     * Split oversized content into smaller pieces
     */
    private function splitOversizedContent(string $text): array {
        $maxPieceSize = 8000; // Smaller pieces for emergency splitting
        $pieces = [];

        // Try to split by paragraphs first
        $paragraphs = preg_split('/\n\s*\n/', $text, -1, PREG_SPLIT_NO_EMPTY);

        $currentPiece = '';
        foreach ($paragraphs as $paragraph) {
            if (mb_strlen($currentPiece . $paragraph) > $maxPieceSize && !empty($currentPiece)) {
                $pieces[] = trim($currentPiece);
                $currentPiece = $paragraph;
            } else {
                $currentPiece .= ($currentPiece ? "\n\n" : '') . $paragraph;
            }
        }

        if (!empty($currentPiece)) {
            $pieces[] = trim($currentPiece);
        }

        return $pieces;
    }

    /**
     * Check if error indicates a timeout
     */
    private function isTimeoutError(array $response): bool {
        if (!isset($response['error'])) return false;

        $error = strtolower($response['error']);
        $timeoutIndicators = [
            'timeout',
            'timed out',
            'time limit',
            'deadline exceeded',
            'operation timed out',
            'curl error',
            'request timeout',
            'gateway timeout',
            'connection timeout',
            'read timeout',
            'execution timeout',
            'too large',
            'content too large',
            'content length exceeds'
        ];

        foreach ($timeoutIndicators as $indicator) {
            if (strpos($error, $indicator) !== false) {
                return true;
            }
        }

        // Check for specific HTTP codes that indicate timeout
        if (isset($response['details']['http_code'])) {
            $timeoutCodes = [504, 408, 524]; // Gateway Timeout, Request Timeout, Cloudflare Timeout
            if (in_array($response['details']['http_code'], $timeoutCodes)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Make API request to DeepSeek
     */
    private function makeApiRequest(string $prompt, array $context = [], array $contentAnalysis = []): array {
        $maxRetries = 3;
        $retryDelay = 1; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            file_put_contents('debug.log', "DeepSeekTranslationService: Attempt {$attempt} of {$maxRetries}\n", FILE_APPEND);

            $result = $this->executeSingleApiRequest($prompt, $context, $attempt);

            if ($result['success']) {
                return $result;
            }

            // Check if error is retryable
            if (!$this->isRetryableError($result)) {
                file_put_contents('debug.log', "DeepSeekTranslationService: Non-retryable error, stopping retries\n", FILE_APPEND);
                return $result;
            }

            // Wait before retry (exponential backoff)
            if ($attempt < $maxRetries) {
                $delay = $retryDelay * pow(2, $attempt - 1);
                file_put_contents('debug.log', "DeepSeekTranslationService: Waiting {$delay}s before retry\n", FILE_APPEND);
                sleep($delay);
            }
        }

        return $result;
    }

    /**
     * Execute single API request
     */
    private function executeSingleApiRequest(string $prompt, array $context, int $attempt): array {
        error_log("DeepSeekTranslationService: Making API request (attempt {$attempt})");
        error_log("DeepSeekTranslationService: API Key length: " . strlen($this->apiKey));
        error_log("DeepSeekTranslationService: Prompt length: " . strlen($prompt));

        // Calculate adaptive token limit
        $adaptiveTokenLimit = $this->calculateAdaptiveTokenLimit($prompt, $context);

        // Build request data in OpenAI format
        $requestData = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a professional translator specializing in Japanese to English translation for novels and literature.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => 0.3,
            'max_tokens' => $adaptiveTokenLimit,
            'stream' => false
        ];

        // Adjust parameters for title translations
        if (isset($context['type']) && $context['type'] === 'title') {
            $requestData['temperature'] = 0.1;
            // Use very conservative token limit for titles to prevent truncation
            $requestData['max_tokens'] = 50; // Very conservative to prevent truncation

            // Skip name dictionary for titles to avoid prompt bloat
            file_put_contents('debug.log', "DeepSeekTranslationService: Using conservative max_tokens: 50 for title translation\n", FILE_APPEND);
        }

        // Adjust parameters for simple test translations
        if (isset($context['simple']) && $context['simple'] === true) {
            $requestData['temperature'] = 0.2;
            $requestData['max_tokens'] = 100; // Higher limit for simple tests
            file_put_contents('debug.log', "DeepSeekTranslationService: Using simple test max_tokens: 100\n", FILE_APPEND);
        }

        // Log token limit for debugging
        file_put_contents('debug.log', "DeepSeekTranslationService: Using max_tokens: {$requestData['max_tokens']}\n", FILE_APPEND);

        $jsonData = json_encode($requestData);

        // Handle JSON encoding issues
        if ($jsonData === false) {
            $jsonError = json_last_error_msg();

            // Check if it's a UTF-8 encoding issue and try to fix it
            if (strpos($jsonError, 'UTF-8') !== false) {
                // Try to fix UTF-8 encoding issues
                $requestData['messages'][1]['content'] = mb_convert_encoding($prompt, 'UTF-8', 'UTF-8');

                // Try encoding again
                $jsonData = json_encode($requestData);
                if ($jsonData === false) {
                    // Alternative: remove problematic characters
                    $cleanPrompt = preg_replace('/[\x00-\x1F\x80-\xFF]/', '', $prompt);
                    $requestData['messages'][1]['content'] = $cleanPrompt;

                    $jsonData = json_encode($requestData);
                    if ($jsonData === false) {
                        return [
                            'success' => false,
                            'error' => 'JSON encoding failed after UTF-8 fixes: ' . json_last_error_msg(),
                            'retryable' => false
                        ];
                    }
                }
            } else {
                return [
                    'success' => false,
                    'error' => 'JSON encoding failed: ' . $jsonError,
                    'retryable' => false
                ];
            }
        }

        error_log("DeepSeekTranslationService: Request data size: " . strlen($jsonData) . " bytes");

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey,
            ],
            CURLOPT_TIMEOUT => TRANSLATION_TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log response details
        error_log("DeepSeekTranslationService: HTTP Code: " . $httpCode);
        error_log("DeepSeekTranslationService: Response length: " . strlen($response));

        if ($error) {
            error_log("DeepSeekTranslationService: cURL error: " . $error);
            return ['success' => false, 'error' => "cURL error: {$error}", 'retryable' => true];
        }

        if ($httpCode !== 200) {
            error_log("DeepSeekTranslationService: HTTP error response: " . substr($response, 0, 500));

            // Parse error response for better error handling
            $errorDetails = $this->parseErrorResponse($response, $httpCode);

            return [
                'success' => false,
                'error' => "HTTP error: {$httpCode}",
                'details' => $errorDetails,
                'retryable' => $this->isHttpCodeRetryable($httpCode),
                'response' => $response
            ];
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("DeepSeekTranslationService: JSON decode error: " . json_last_error_msg());
            error_log("DeepSeekTranslationService: Raw response: " . substr($response, 0, 500));
            return ['success' => false, 'error' => 'Invalid JSON response: ' . json_last_error_msg(), 'retryable' => false];
        }

        error_log("DeepSeekTranslationService: API request successful");
        return ['success' => true, 'data' => $decodedResponse];
    }

    /**
     * Extract translation from DeepSeek API response with completion validation
     */
    public function extractTranslationFromResponse(array $response, array $context = []): string {
        if (isset($response['choices'][0]['message']['content'])) {
            $translatedText = trim($response['choices'][0]['message']['content']);

            // Check if response was truncated due to token limits
            $completionInfo = $this->validateResponseCompletion($response, $translatedText, $context);

            // Skip truncation check for titles to avoid false positives
            if (isset($context['type']) && $context['type'] === 'title') {
                file_put_contents('debug.log', "DeepSeekTranslationService: Skipping truncation check for title translation\n", FILE_APPEND);
            } elseif (!$completionInfo['is_complete']) {
                file_put_contents('debug.log', "DeepSeekTranslationService: Response truncation detected - " . $completionInfo['reason'] . "\n", FILE_APPEND);

                // If truncated mid-sentence, try to find a good stopping point
                if ($completionInfo['truncated_mid_sentence']) {
                    $adjustedText = $this->findCompleteSentenceEnding($translatedText);
                    if ($adjustedText !== $translatedText) {
                        file_put_contents('debug.log', "DeepSeekTranslationService: Adjusted truncated response to complete sentence\n", FILE_APPEND);
                        $translatedText = $adjustedText;
                    }
                }

                // Throw exception to trigger retry with adjusted parameters
                throw new Exception('Response was truncated due to token limits: ' . $completionInfo['reason']);
            }

            return $translatedText;
        }

        throw new Exception('No translation found in response');
    }

    /**
     * Extract cache hit information from DeepSeek API response
     */
    private function extractCacheInfo(array $response): array {
        $cacheInfo = [
            'cache_hit_tokens' => 0,
            'cache_miss_tokens' => 0,
            'total_tokens' => 0,
            'cache_hit_rate' => 0.0,
            'estimated_cost_savings' => 0.0
        ];

        if (isset($response['usage'])) {
            $usage = $response['usage'];

            // Extract cache hit/miss tokens (new fields from DeepSeek)
            $cacheInfo['cache_hit_tokens'] = $usage['prompt_cache_hit_tokens'] ?? 0;
            $cacheInfo['cache_miss_tokens'] = $usage['prompt_cache_miss_tokens'] ?? 0;

            // Calculate total prompt tokens
            $cacheInfo['total_tokens'] = $cacheInfo['cache_hit_tokens'] + $cacheInfo['cache_miss_tokens'];

            // Calculate cache hit rate
            if ($cacheInfo['total_tokens'] > 0) {
                $cacheInfo['cache_hit_rate'] = round(
                    ($cacheInfo['cache_hit_tokens'] / $cacheInfo['total_tokens']) * 100,
                    2
                );
            }

            // Calculate estimated cost savings (DeepSeek pricing: $0.014 vs $0.14 per million tokens)
            $cacheInfo['estimated_cost_savings'] = round(
                ($cacheInfo['cache_hit_tokens'] / 1000000) * (0.14 - 0.014),
                6
            );

            // Log cache performance for monitoring
            if ($cacheInfo['total_tokens'] > 0) {
                error_log("DeepSeekTranslationService: Cache Performance - Hit: {$cacheInfo['cache_hit_tokens']}, Miss: {$cacheInfo['cache_miss_tokens']}, Rate: {$cacheInfo['cache_hit_rate']}%, Savings: \${$cacheInfo['estimated_cost_savings']}");
            }
        }

        return $cacheInfo;
    }

    /**
     * Build translation prompt with timeout optimization
     */
    public function buildTranslationPrompt(string $text, string $targetLanguage, string $sourceLanguage, array $context, array $contentAnalysis = []): string {
        $languageNames = [
            'en' => 'English',
            'ja' => 'Japanese',
            'zh' => 'Chinese',
            'ko' => 'Korean'
        ];

        $targetLangName = $languageNames[$targetLanguage] ?? $targetLanguage;
        $sourceLangName = $sourceLanguage === 'auto' ? 'the source language' : ($languageNames[$sourceLanguage] ?? $sourceLanguage);

        // Check if this is a simple test context - use minimal prompt
        if (isset($context['simple']) && $context['simple'] === true) {
            return "Translate this text from {$sourceLangName} to {$targetLangName}:\n\n{$text}";
        }

        // For titles, use very simple prompt to avoid complexity issues
        if (isset($context['type']) && $context['type'] === 'title') {
            return "Translate this novel title from {$sourceLangName} to {$targetLangName}. Provide only the translated title:\n\n{$text}";
        }

        $prompt = "Translate the following text from {$sourceLangName} to {$targetLangName}.\n\n";

        // Add timeout optimization instructions for large content
        if (!empty($contentAnalysis) && $contentAnalysis['high_timeout_risk']) {
            $prompt .= "IMPORTANT: This is a large content block. Please provide a direct, efficient translation without extensive explanations or commentary to ensure timely completion.\n\n";
        }

        // Add strict no-explanation instruction for all translations
        $prompt .= "CRITICAL: Provide ONLY the translation. Do not add any notes, explanations, comments, or meta-text about the translation process, sound effects, formatting, or any other aspects. Your response should contain only the translated text.\n\n";

        // Essential dialogue preservation rules (simplified)
        $prompt .= "DIALOGUE RULES:\n";
        $prompt .= "- Do NOT add dialogue tags (\"he said\", \"she replied\") that don't exist in the original\n";
        $prompt .= "- Preserve the exact dialogue structure and formatting\n";
        $prompt .= "- Only translate what is actually present in the source text\n\n";

        // Translation requirements (simplified)
        $prompt .= "TRANSLATION REQUIREMENTS:\n";
        $prompt .= "- Translate ALL text to natural English (no romanization)\n";
        $prompt .= "- Convert sound effects to descriptive English words\n";
        $prompt .= "- Only keep character names and honorifics as specified in name dictionary\n";
        $prompt .= "- Make the text fully comprehensible to English readers\n\n";

        // Formatting preservation (simplified)
        $prompt .= "FORMATTING RULES:\n";
        $prompt .= "- Preserve Japanese/Chinese punctuation marks exactly: 「」『』【】（）・※〜～\n";
        $prompt .= "- Keep all line breaks and paragraph structure\n";
        $prompt .= "- Maintain the same visual layout as the original\n\n";

        // Add specific instructions based on context
        if (isset($context['type'])) {
            switch ($context['type']) {
                case 'title':
                    // For titles, use minimal instructions to avoid prompt bloat
                    $prompt = "Translate this novel title from {$sourceLangName} to {$targetLangName}. Provide only the translated title, no explanations:\n\n{$text}";
                    return $prompt; // Return early with minimal prompt for titles
                    break;
                case 'chapter':
                case 'content':
                case 'chunk':
                    $prompt .= "This is novel content. Maintain the narrative style and preserve character names.\n";

                    // Add narrative perspective instructions based on context analysis
                    if (isset($context['narrative_context'])) {
                        $narrativeContext = $context['narrative_context'];
                        $prompt .= $this->buildNarrativePerspectiveInstructions($narrativeContext, $context);
                    }

                    // Add specific dialogue and narrative formatting instructions
                    $prompt .= "DIALOGUE AND NARRATIVE FORMATTING:\n";
                    $prompt .= "- Preserve all dialogue quotation marks and formatting exactly as they appear\n";
                    $prompt .= "- Maintain speaker attribution and dialogue tags in their original positions\n";
                    $prompt .= "- Keep conversation flow natural and easy to follow\n";
                    $prompt .= "- Preserve narrative descriptions between dialogue exactly as formatted\n";
                    $prompt .= "- Maintain the original rhythm and pacing through proper punctuation\n";
                    $prompt .= "- Keep all exclamation points, question marks, and ellipses in dialogue\n";
                    $prompt .= "- Preserve any special formatting for thoughts, internal monologue, or emphasis\n\n";

                    // Add chunk context if available
                    if (isset($context['chunk_number'])) {
                        $prompt .= "This is chunk {$context['chunk_number']}";
                        if (isset($context['total_chunks'])) {
                            $prompt .= " of {$context['total_chunks']}";
                        }
                        $prompt .= " from a larger chapter. Maintain consistency with the overall narrative.\n";

                        // Add context from previous/next chunks if available
                        if (isset($context['previous_context'])) {
                            $prompt .= "Previous context: " . substr($context['previous_context'], 0, 100) . "...\n";
                        }
                        if (isset($context['next_context'])) {
                            $prompt .= "Following context: " . substr($context['next_context'], 0, 100) . "...\n";
                        }
                    }

                    // Sound effects processing removed - all onomatopoeia will be translated to descriptive English

                    // Add dialogue-specific instructions if dialogue is detected
                    $prompt .= $this->addDialogueSpecificInstructions($text);

                    // Add honorific preservation instructions
                    $detectedLanguage = $this->honorificService->detectLanguage($text);
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions($detectedLanguage);
                    $prompt .= "\n";
                    break;
                case 'synopsis':
                    $prompt .= "This is a novel synopsis/description. Keep it engaging and informative.\n\n";

                    // Add honorific preservation instructions for synopsis
                    $detectedLanguage = $this->honorificService->detectLanguage($text);
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions($detectedLanguage);
                    break;
            }
        }

        // Add name consistency instructions (simplified)
        // Skip name dictionary for title translations to avoid prompt bloat
        if (isset($context['names']) && !empty($context['names']) &&
            (!isset($context['type']) || $context['type'] !== 'title')) {

            $prompt .= "CHARACTER NAMES:\n";
            $nameCount = 0;
            foreach ($context['names'] as $name) {
                // Use translation if available and not empty, otherwise romanization, otherwise original
                $translation = isset($name['translation']) ? trim($name['translation']) : '';
                $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';
                $original = isset($name['original_name']) ? trim($name['original_name']) : '';

                // Priority: translation > romanization > original (only if not empty)
                if (!empty($translation)) {
                    $targetName = $translation;
                } elseif (!empty($romanization)) {
                    $targetName = $romanization;
                } else {
                    $targetName = $original;
                }

                // Skip entries with empty original names or target names
                if (empty($original) || empty($targetName)) {
                    continue;
                }

                $prompt .= "- {$original} → {$targetName}\n";
                $nameCount++;

                // Limit to first 20 names to avoid prompt bloat
                if ($nameCount >= 20) {
                    $prompt .= "- (and " . (count($context['names']) - 20) . " more names...)\n";
                    break;
                }
            }
            $prompt .= "Use these exact name translations consistently.\n\n";
        }

        // Final reminder (simplified)
        $prompt .= "FINAL REQUIREMENTS:\n";
        $prompt .= "- Translate to natural, fluent English\n";
        $prompt .= "- Use provided character names exactly\n";
        $prompt .= "- Preserve original formatting and structure\n";
        $prompt .= "- Provide only the translation, no explanations\n\n";

        $prompt .= "Text to translate:\n" . $text;

        return $prompt;
    }

    /**
     * Build narrative perspective instructions based on context analysis and user POV preference
     */
    private function buildNarrativePerspectiveInstructions(array $narrativeContext, array $fullContext): string {
        $instructions = "\n🚨🚨🚨 CRITICAL NARRATIVE PERSPECTIVE REQUIREMENTS 🚨🚨🚨\n\n";

        $perspective = $narrativeContext['narrative_voice'] ?? 'third_person';
        $confidence = $narrativeContext['narrative_confidence'] ?? 0.3;

        // Check for user POV preference
        $userPOVPreference = $fullContext['user_pov_preference'] ?? null;
        $optimalPerspective = $narrativeContext['optimal_perspective'] ?? null;

        // Determine the target perspective based on user preference or intelligent analysis
        $targetPerspective = null;
        $isUserSelected = false;

        if ($userPOVPreference && $userPOVPreference !== 'preserve_original') {
            $targetPerspective = $userPOVPreference;
            $isUserSelected = true;
            $instructions .= "🎯 USER-SELECTED POV: {$userPOVPreference}\n";
            $instructions .= "📋 APPLYING USER PREFERENCE: All narrative text will follow the selected perspective.\n\n";
        } elseif ($optimalPerspective && $optimalPerspective !== 'preserve_original') {
            $targetPerspective = $optimalPerspective;
            $instructions .= "🤖 AI-DETERMINED POV: {$optimalPerspective}\n";
            $instructions .= "📋 APPLYING INTELLIGENT ANALYSIS: Using AI-determined optimal perspective.\n\n";
        } else {
            // Default to third person for consistency (like the backup system)
            $targetPerspective = 'third_person_omniscient';
            $instructions .= "🔧 DEFAULT POV: third_person_omniscient\n";
            $instructions .= "📋 APPLYING DEFAULT RULE: Using third person omniscient for narrative consistency.\n\n";
        }

        // Generate specific instructions based on target perspective
        switch ($targetPerspective) {
            case 'first_person':
                $instructions .= $this->getFirstPersonNarrativeInstructions();
                break;

            case 'third_person_limited':
                $instructions .= $this->getThirdPersonLimitedNarrativeInstructions();
                break;

            case 'third_person_omniscient':
                $instructions .= $this->getThirdPersonOmniscientNarrativeInstructions();
                break;

            case 'third_person':
            default:
                $instructions .= $this->getThirdPersonNarrativeInstructions();
                break;
        }

        // Add dialogue preservation rules (common to all perspectives)
        $instructions .= "\n✅ DIALOGUE PRESERVATION (ALL PERSPECTIVES):\n";
        $instructions .= "• Character dialogue within quotation marks (「」『』\"\" etc.) → Preserve character's natural speech\n";
        $instructions .= "• Direct speech attribution → Match the narrative perspective\n";
        $instructions .= "• Character voice authenticity → Keep character personality in dialogue\n\n";

        // Add consistency requirements
        $instructions .= "🔒 CONSISTENCY REQUIREMENTS:\n";
        $instructions .= "- Maintain the chosen perspective throughout the entire text\n";
        $instructions .= "- Do not mix different perspectives within narrative sections\n";
        $instructions .= "- Ensure pronoun consistency and grammatical correctness\n";
        $instructions .= "- Preserve the natural flow and readability of the text\n\n";

        // Add perspective analysis information for debugging
        if ($confidence > 0) {
            $instructions .= "📊 PERSPECTIVE ANALYSIS:\n";
            $instructions .= "- Original detected perspective: {$perspective} (confidence: " . round($confidence * 100, 1) . "%)\n";
            $instructions .= "- Target perspective: {$targetPerspective}" . ($isUserSelected ? " (USER SELECTED)" : " (AUTO/DEFAULT)") . "\n";
            if (!empty($narrativeContext['perspective_indicators']['first_person'])) {
                $instructions .= "- First person indicators: " . implode(', ', array_slice($narrativeContext['perspective_indicators']['first_person'], 0, 3)) . "\n";
            }
            if (!empty($narrativeContext['perspective_indicators']['third_person'])) {
                $instructions .= "- Third person indicators: " . implode(', ', array_slice($narrativeContext['perspective_indicators']['third_person'], 0, 3)) . "\n";
            }
            $instructions .= "\n";
        }

        return $instructions;
    }





    /**
     * Generate first person narrative instructions
     */
    private function getFirstPersonNarrativeInstructions(): string {
        $instructions = "📝 FIRST PERSON NARRATIVE REQUIREMENTS:\n";
        $instructions .= "1. 🔥 NARRATIVE TEXT → FIRST PERSON: All narrative descriptions, actions, and observations MUST use first person pronouns ('I', 'me', 'my', 'we', 'us', 'our')\n";
        $instructions .= "2. 🔥 PERSONAL PERSPECTIVE: Describe all actions and observations from the protagonist's personal viewpoint\n";
        $instructions .= "3. 🔥 INTERNAL ACCESS: Express thoughts and feelings directly: 'I think', 'I feel', 'I believe'\n";
        $instructions .= "4. 🔥 DIALOGUE ATTRIBUTION: Use 'I said', 'I asked', 'I replied' for protagonist speech\n";
        $instructions .= "5. 🔥 ACTIONS AND OBSERVATIONS: Use 'I saw', 'I heard', 'I felt', 'I moved', 'I did'\n";
        $instructions .= "6. 🔥 EMOTIONAL EXPRESSION: 'My heart raced', 'I felt confused', 'My eyes widened'\n\n";

        $instructions .= "❌ CONVERSION EXAMPLES (Third → First Person):\n";
        $instructions .= "- 'He thought to himself' → ✅ 'I thought to myself'\n";
        $instructions .= "- 'His heart raced' → ✅ 'My heart raced'\n";
        $instructions .= "- 'She decided to go' → ✅ 'I decided to go'\n";
        $instructions .= "- 'They felt confused' → ✅ 'I felt confused'\n";
        $instructions .= "- 'He walked to the door' → ✅ 'I walked to the door'\n\n";

        return $instructions;
    }

    /**
     * Generate third person narrative instructions
     */
    private function getThirdPersonNarrativeInstructions(): string {
        $instructions = "📝 THIRD PERSON NARRATIVE REQUIREMENTS:\n";
        $instructions .= "1. 🔥 NARRATIVE TEXT → THIRD PERSON: All narrative descriptions, actions, and observations MUST use third person pronouns ('he', 'she', 'they', 'him', 'her', 'them')\n";
        $instructions .= "2. 🔥 EXTERNAL PERSPECTIVE: Describe all actions and observations from an outside observer's viewpoint\n";
        $instructions .= "3. 🔥 OBJECTIVE NARRATION: Maintain objective, external viewpoint throughout\n";
        $instructions .= "4. 🔥 DIALOGUE ATTRIBUTION: Use 'he said', 'she asked', 'they replied' for character speech\n";
        $instructions .= "5. 🔥 CHARACTER THOUGHTS: Express as 'he thought', 'she wondered', 'they realized'\n";
        $instructions .= "6. 🔥 ACTIONS AND OBSERVATIONS: Use 'he saw', 'she heard', 'they felt', 'he moved', 'she did'\n\n";

        $instructions .= "❌ CONVERSION EXAMPLES (First → Third Person):\n";
        $instructions .= "- 'I thought to myself' → ✅ 'He thought to himself'\n";
        $instructions .= "- 'My heart raced' → ✅ 'His heart raced'\n";
        $instructions .= "- 'I decided to go' → ✅ 'She decided to go'\n";
        $instructions .= "- 'I felt confused' → ✅ 'They felt confused'\n";
        $instructions .= "- 'I walked to the door' → ✅ 'He walked to the door'\n\n";

        return $instructions;
    }

    /**
     * Generate third person limited narrative instructions
     */
    private function getThirdPersonLimitedNarrativeInstructions(): string {
        $instructions = "📝 THIRD PERSON LIMITED NARRATIVE REQUIREMENTS:\n";
        $instructions .= "1. 🔥 NARRATIVE TEXT → THIRD PERSON: Use third person pronouns ('he', 'she', 'they') for the focal character\n";
        $instructions .= "2. 🔥 LIMITED PERSPECTIVE: Focus on ONE character's thoughts, feelings, and experiences\n";
        $instructions .= "3. 🔥 RESTRICTED ACCESS: Only reveal what the focal character knows or experiences\n";
        $instructions .= "4. 🔥 FOCAL CHARACTER THOUGHTS: Use 'he thought', 'she wondered' for the main character only\n";
        $instructions .= "5. 🔥 OTHER CHARACTERS: Describe other characters only from the focal character's perspective\n";
        $instructions .= "6. 🔥 CONSISTENT FOCUS: Maintain focus on the chosen character throughout the text\n\n";

        $instructions .= "❌ CONVERSION EXAMPLES (First → Third Person Limited):\n";
        $instructions .= "- 'I thought about her' → ✅ 'He thought about her'\n";
        $instructions .= "- 'I couldn't understand what she was thinking' → ✅ 'He couldn't understand what she was thinking'\n";
        $instructions .= "- 'My confusion grew' → ✅ 'His confusion grew'\n\n";

        return $instructions;
    }

    /**
     * Generate third person omniscient narrative instructions
     */
    private function getThirdPersonOmniscientNarrativeInstructions(): string {
        $instructions = "📝 THIRD PERSON OMNISCIENT NARRATIVE REQUIREMENTS:\n";
        $instructions .= "1. 🔥 NARRATIVE TEXT → THIRD PERSON: Use third person pronouns ('he', 'she', 'they') for all characters\n";
        $instructions .= "2. 🔥 OMNISCIENT ACCESS: Access to ALL characters' thoughts, feelings, and motivations\n";
        $instructions .= "3. 🔥 BROAD PERSPECTIVE: Can reveal information unknown to any single character\n";
        $instructions .= "4. 🔥 MULTIPLE VIEWPOINTS: Use 'he thought', 'she wondered' for multiple characters\n";
        $instructions .= "5. 🔥 NARRATIVE AUTHORITY: Provide broader context and background information\n";
        $instructions .= "6. 🔥 CHARACTER SHIFTING: Can shift focus between different characters as needed\n\n";

        $instructions .= "❌ CONVERSION EXAMPLES (First → Third Person Omniscient):\n";
        $instructions .= "- 'I thought to myself' → ✅ 'He thought to himself'\n";
        $instructions .= "- 'My heart raced' → ✅ 'His heart raced'\n";
        $instructions .= "- 'We decided to go' → ✅ 'They decided to go'\n";
        $instructions .= "- 'I felt confused' → ✅ 'He felt confused'\n";
        $instructions .= "- 'Our plan was working' → ✅ 'Their plan was working'\n\n";

        return $instructions;
    }

    // Sound effects context instruction methods removed - no longer needed since all onomatopoeia are translated to English

    /**
     * Detect dialogue content and add specific formatting instructions
     */
    private function addDialogueSpecificInstructions(string $text): string {
        $instructions = "";

        // Detect various dialogue patterns
        $hasJapaneseQuotes = preg_match('/[「」『』]/', $text);
        $hasWesternQuotes = preg_match('/["\'"]/', $text);
        $hasDialogueTags = preg_match('/(said|asked|replied|whispered|shouted|muttered|exclaimed)/i', $text);
        $hasConversation = preg_match('/[「"\'"][^「"\']*[」"\'"][^「"\']*[「"\'"]/', $text);

        if ($hasJapaneseQuotes || $hasWesternQuotes || $hasDialogueTags || $hasConversation) {
            $instructions .= "DIALOGUE DETECTED - SPECIAL FORMATTING REQUIREMENTS:\n";

            if ($hasJapaneseQuotes) {
                // Check which specific types are present
                $has_single_quotes = preg_match('/[「」]/', $text);
                $has_double_quotes = preg_match('/[『』]/', $text);

                if ($has_single_quotes && $has_double_quotes) {
                    $instructions .= "- MIXED Japanese quotation marks detected: Preserve BOTH 「」 and 『』 exactly as they appear - do NOT convert between types\n";
                } elseif ($has_single_quotes) {
                    $instructions .= "- Japanese single quotation marks 「」 detected: Keep ALL as 「」 - do NOT convert to 『』 or English quotes\n";
                } elseif ($has_double_quotes) {
                    $instructions .= "- Japanese double quotation marks 『』 detected: Keep ALL as 『』 - do NOT convert to 「」 or English quotes\n";
                }
            }

            if ($hasWesternQuotes) {
                $instructions .= "- Quotation marks detected: Maintain exact placement and nesting of quotes\n";
            }

            if ($hasDialogueTags) {
                $instructions .= "- Dialogue tags detected: Keep speaker attribution clear and natural\n";
            }

            if ($hasConversation) {
                $instructions .= "- Multi-speaker conversation detected: Ensure each speaker's lines are clearly distinguished\n";
            }

            $instructions .= "- Preserve all dialogue punctuation including commas, periods, exclamation points, and question marks within quotes\n";
            $instructions .= "- Maintain the natural flow and rhythm of conversation\n";
            $instructions .= "- Keep any narrative interruptions within dialogue properly formatted\n\n";
        }

        return $instructions;
    }

    /**
     * Calculate adaptive token limit based on content size and type
     */
    private function calculateAdaptiveTokenLimit(string $prompt, array $context): int {
        $promptLength = mb_strlen($prompt);

        // For titles, use very small limit to prevent truncation
        if (isset($context['type']) && $context['type'] === 'title') {
            $baseLimit = 50; // Very conservative to prevent truncation

            // Reduce for retry attempts
            if (isset($context['truncation_retry'])) {
                $retryAttempt = $context['truncation_retry'];
                $baseLimit = max(20, $baseLimit - ($retryAttempt * 10));
                file_put_contents('debug.log', "DeepSeekTranslationService: Title retry attempt {$retryAttempt}, using token limit: {$baseLimit}\n", FILE_APPEND);
            }

            return $baseLimit;
        }

        // For content, calculate based on input size
        // NOTE: DeepSeek API max_tokens limit is 8192, so we must stay within this bound
        $baseLimit = 4096; // Default

        if ($promptLength < 2000) {
            $baseLimit = 4096; // Small content
        } elseif ($promptLength < 8000) {
            $baseLimit = 6144; // Medium content
        } elseif ($promptLength < 15000) {
            $baseLimit = 8192; // Large content (max allowed by DeepSeek)
        } else {
            $baseLimit = 8192; // Very large content (capped at DeepSeek API limit)
        }

        // Reduce token limit for retry attempts
        if (isset($context['truncation_retry'])) {
            $retryAttempt = $context['truncation_retry'];
            $reductionFactor = 1 - ($retryAttempt * 0.25); // Reduce by 25% per retry
            $baseLimit = max(2048, floor($baseLimit * $reductionFactor));

            file_put_contents('debug.log', "DeepSeekTranslationService: Retry attempt {$retryAttempt}, reduced token limit to {$baseLimit}\n", FILE_APPEND);
        }

        return $baseLimit;
    }

    /**
     * Validate if API response was completed or truncated
     */
    private function validateResponseCompletion(array $response, string $translatedText, array $context = []): array {
        $result = [
            'is_complete' => true,
            'reason' => '',
            'truncated_mid_sentence' => false,
            'finish_reason' => null
        ];

        // Check DeepSeek finish reason
        if (isset($response['choices'][0]['finish_reason'])) {
            $finishReason = $response['choices'][0]['finish_reason'];
            $result['finish_reason'] = $finishReason;

            if ($finishReason === 'length') {
                $result['is_complete'] = false;
                $result['reason'] = 'Response truncated due to max_tokens limit';
                $result['truncated_mid_sentence'] = $this->isTextTruncatedMidSentence($translatedText, $context);
            } elseif ($finishReason === 'content_filter') {
                $result['is_complete'] = false;
                $result['reason'] = 'Response blocked by content filter';
            }
        }

        // Additional heuristic checks for truncation
        if ($result['is_complete']) {
            // Check if text ends abruptly (common signs of truncation)
            if ($this->isTextTruncatedMidSentence($translatedText, $context)) {
                $result['is_complete'] = false;
                $result['reason'] = 'Text appears to end mid-sentence (heuristic detection)';
                $result['truncated_mid_sentence'] = true;
            }

            // Check for incomplete dialogue
            if ($this->hasIncompleteDialogue($translatedText)) {
                $result['is_complete'] = false;
                $result['reason'] = 'Text contains incomplete dialogue markers';
                $result['truncated_mid_sentence'] = true;
            }
        }

        return $result;
    }

    /**
     * Check if text appears to be truncated mid-sentence
     */
    private function isTextTruncatedMidSentence(string $text, array $context = []): bool {
        $text = trim($text);
        if (empty($text)) {
            return false;
        }

        // For simple tests, be very lenient with truncation detection
        if (isset($context['simple']) && $context['simple'] === true) {
            // Only flag as truncated if it's obviously incomplete
            return $this->isSimpleTestTruncated($text);
        }

        // For titles, use much more lenient truncation detection
        if (isset($context['type']) && $context['type'] === 'title') {
            return $this->isTitleTruncated($text);
        }

        $lastChar = mb_substr($text, -1);

        // Check if ends with sentence-ending punctuation
        if (in_array($lastChar, ['.', '!', '?', '。', '！', '？', '』', '」', '"', "'"])) {
            return false;
        }

        // Check if ends with common continuation patterns
        $continuationPatterns = [
            '/\s+(and|but|or|so|then|however|therefore|meanwhile|suddenly|finally)$/i',
            '/\s+(the|a|an|this|that|these|those|his|her|their|my|your)$/i',
            '/\s+(is|are|was|were|will|would|could|should|might|must)$/i',
            '/\s+(to|for|with|by|from|in|on|at|of|about)$/i',
            '/,\s*$/i', // Ends with comma
            '/\s+[a-z]+$/i' // Ends with lowercase word (likely mid-sentence)
        ];

        foreach ($continuationPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        // Check if last sentence is significantly shorter than average
        $sentences = preg_split('/[.!?。！？]+/', $text);
        if (count($sentences) > 1) {
            $lastSentence = trim(end($sentences));
            if (!empty($lastSentence)) {
                $avgLength = array_sum(array_map('mb_strlen', array_slice($sentences, 0, -1))) / (count($sentences) - 1);
                if (mb_strlen($lastSentence) < $avgLength * 0.3) {
                    return true;
                }
            }
        }

        // Check for very short text that's likely incomplete (but allow single complete words)
        if (mb_strlen($text) <= 3 && !preg_match('/^[A-Z][a-z]*[.!?]$/', $text)) {
            return true;
        }

        return false;
    }

    /**
     * Check if a simple test appears to be truncated
     */
    private function isSimpleTestTruncated(string $text): bool {
        // For simple tests, be very lenient - only flag obvious truncation

        // If we have any complete sentence, consider it not truncated
        if (preg_match('/[.!?。！？]/', $text)) {
            return false;
        }

        // Check for very obvious truncation patterns only
        $obviousTruncationPatterns = [
            '/\s+(and|or|but|so|then)$/i', // Ends with obvious connecting words
            '/,\s*$/i', // Ends with comma
            '/\s+(the|a|an|this|that)$/i', // Ends with articles
            '/\s+(is|are|was|were)$/i' // Ends with incomplete verbs
        ];

        foreach ($obviousTruncationPatterns as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        // If text is very short and doesn't end with punctuation, might be truncated
        if (mb_strlen($text) <= 3) {
            return true;
        }

        return false;
    }

    /**
     * Check if a title appears to be truncated (much more lenient than regular text)
     */
    private function isTitleTruncated(string $text): bool {
        $text = trim($text);
        if (empty($text)) {
            return true;
        }

        // For titles, only consider it truncated if it's extremely short or obviously incomplete
        if (mb_strlen($text) <= 2) {
            return true;
        }

        // Check for obvious truncation indicators
        $truncationIndicators = [
            '/\s+$/',  // Ends with whitespace
            '/\.\.\.$/', // Ends with ellipsis
            '/\s+(and|but|or|the|a|an|of|in|on|at|to|for|with)$/i', // Ends with very common connecting words
        ];

        foreach ($truncationIndicators as $pattern) {
            if (preg_match($pattern, $text)) {
                return true;
            }
        }

        // If it has reasonable length and doesn't match obvious truncation patterns, consider it complete
        return false;
    }

    /**
     * Check if text has incomplete dialogue markers
     */
    private function hasIncompleteDialogue(string $text): bool {
        // Count opening and closing dialogue markers
        $openMarkers = preg_match_all('/[「『"\'"]/', $text);
        $closeMarkers = preg_match_all('/[」』"\'"]/', $text);

        // If there's a significant imbalance, dialogue might be incomplete
        return abs($openMarkers - $closeMarkers) > 1;
    }

    /**
     * Find a complete sentence ending in truncated text
     */
    private function findCompleteSentenceEnding(string $text): string {
        // Find the last complete sentence
        $sentenceEnders = ['.', '!', '?', '。', '！', '？'];

        $lastCompletePos = -1;
        for ($i = mb_strlen($text) - 1; $i >= 0; $i--) {
            $char = mb_substr($text, $i, 1);
            if (in_array($char, $sentenceEnders)) {
                // Check if this is followed by closing dialogue markers
                $remaining = mb_substr($text, $i + 1);
                if (preg_match('/^[」』"\'"\s]*$/', $remaining)) {
                    $lastCompletePos = $i + mb_strlen($remaining);
                    break;
                } else {
                    $lastCompletePos = $i + 1;
                    break;
                }
            }
        }

        if ($lastCompletePos > 0) {
            return mb_substr($text, 0, $lastCompletePos);
        }

        return $text;
    }

    /**
     * Check if an error is retryable
     */
    private function isRetryableError(array $result): bool {
        return isset($result['retryable']) && $result['retryable'] === true;
    }

    /**
     * Check if an HTTP code indicates a retryable error
     */
    private function isHttpCodeRetryable(int $httpCode): bool {
        $retryableCodes = [
            429, // Too Many Requests
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504, // Gateway Timeout
        ];

        return in_array($httpCode, $retryableCodes);
    }

    /**
     * Parse error response to extract meaningful error information
     */
    private function parseErrorResponse(string $response, int $httpCode): array {
        $errorDetails = [
            'http_code' => $httpCode,
            'message' => 'Unknown error',
            'type' => 'unknown'
        ];

        if (!empty($response)) {
            $decoded = json_decode($response, true);
            if ($decoded && isset($decoded['error'])) {
                $error = $decoded['error'];
                $errorDetails['message'] = $error['message'] ?? 'Unknown error';
                $errorDetails['type'] = $error['type'] ?? 'unknown';
                $errorDetails['code'] = $error['code'] ?? $httpCode;

                if (isset($error['type'])) {
                    error_log("DeepSeekTranslationService: API Error Type: " . $error['type']);
                }
            }
        }

        return $errorDetails;
    }

    /**
     * Clean up title translation to ensure single, clean result
     */
    private function cleanTitleTranslation(string $translation): string {
        $original = $translation;

        file_put_contents('debug.log', "DeepSeekTranslationService: Original title response: " . substr($translation, 0, 200) . "\n", FILE_APPEND);

        // Remove common verbose introduction patterns
        $introPatterns = [
            '/^Here are.*?translations?.*?:/i',
            '/^Here are.*?options?.*?:/i',
            '/^The translation.*?:/i',
            '/^Translation.*?:/i',
        ];

        $cleaned = $translation;
        foreach ($introPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        // Remove formatting and take first substantial line
        $lines = explode("\n", $cleaned);
        $bestLine = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            if (preg_match('/^[\*\-\d\.\s]+$/', $line)) continue;
            
            $bestLine = $line;
            break;
        }

        $cleaned = $bestLine ?: $cleaned;

        // Final cleanup
        $cleanupPatterns = [
            '/\([^)]*\)/',              // Remove parenthetical explanations
            '/\[[^\]]*\]/',             // Remove bracketed explanations
            '/^["\'"]*/',               // Remove leading quotes
            '/["\'"]*$/',               // Remove trailing quotes
        ];

        foreach ($cleanupPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = trim($cleaned);

        if (empty($cleaned) || strlen($cleaned) < 3) {
            if (preg_match('/([A-Za-z][A-Za-z0-9\s\-~\[\]]+)/', $original, $matches)) {
                $cleaned = trim($matches[1]);
            } else {
                // Preserve Japanese/Chinese punctuation in fallback cleaning
                $cleaned = trim(substr(preg_replace('/[^\w\s\-~\[\]【】〖〗〔〕〈〉《》「」『』（）・※〜～]/u', '', $original), 0, 100));
            }
        }

        file_put_contents('debug.log', "DeepSeekTranslationService: Cleaned title result: " . $cleaned . "\n", FILE_APPEND);

        return $cleaned;
    }

    /**
     * Restore formatting if lost during translation
     */
    private function restoreFormattingIfLost(string $originalText, string $translatedText, array $context): string {
        // Check if formatting was preserved
        $originalLineBreaks = substr_count($originalText, "\n");
        $translatedLineBreaks = substr_count($translatedText, "\n");
        $originalParagraphs = count(preg_split('/\n\s*\n/', $originalText));
        $translatedParagraphs = count(preg_split('/\n\s*\n/', $translatedText));

        // If formatting is severely lost, attempt to restore it
        if ($originalLineBreaks > 2 && $translatedLineBreaks === 0) {
            file_put_contents('debug.log', "DeepSeekTranslationService: Severe formatting loss detected, attempting restoration\n", FILE_APPEND);
            return $this->attemptFormattingRestoration($originalText, $translatedText);
        }

        // If paragraph structure is lost, attempt to restore it
        if ($originalParagraphs > 2 && $translatedParagraphs === 1) {
            file_put_contents('debug.log', "DeepSeekTranslationService: Paragraph structure lost, attempting restoration\n", FILE_APPEND);
            return $this->attemptParagraphRestoration($originalText, $translatedText);
        }

        // Decode HTML entities that might have been introduced
        $translatedText = html_entity_decode($translatedText, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $translatedText;
    }

    /**
     * Attempt to restore formatting by analyzing original structure
     */
    private function attemptFormattingRestoration(string $originalText, string $translatedText): string {
        // Split original text into segments based on structure
        $originalLines = explode("\n", $originalText);
        $translatedWords = preg_split('/\s+/', trim($translatedText));

        if (empty($translatedWords)) {
            return $translatedText;
        }

        $restoredLines = [];
        $wordIndex = 0;
        $totalWords = count($translatedWords);

        foreach ($originalLines as $originalLine) {
            $originalLine = trim($originalLine);

            if (empty($originalLine)) {
                // Empty line in original - preserve as empty line
                $restoredLines[] = '';
                continue;
            }

            // Estimate how many words this line should have based on original
            $originalWords = preg_split('/\s+/', $originalLine);
            $estimatedWords = max(1, count($originalWords));

            // Take words from translated text
            $lineWords = [];
            for ($i = 0; $i < $estimatedWords && $wordIndex < $totalWords; $i++) {
                $lineWords[] = $translatedWords[$wordIndex++];
            }

            if (!empty($lineWords)) {
                $restoredLines[] = implode(' ', $lineWords);
            }
        }

        // Add any remaining words as a final line
        if ($wordIndex < $totalWords) {
            $remainingWords = array_slice($translatedWords, $wordIndex);
            $restoredLines[] = implode(' ', $remainingWords);
        }

        $restored = implode("\n", $restoredLines);
        file_put_contents('debug.log', "DeepSeekTranslationService: Formatting restoration attempted\n", FILE_APPEND);

        return $restored;
    }

    /**
     * Attempt to restore paragraph structure
     */
    private function attemptParagraphRestoration(string $originalText, string $translatedText): string {
        // Split original into paragraphs
        $originalParagraphs = preg_split('/\n\s*\n/', $originalText);
        $translatedSentences = preg_split('/(?<=[.!?])\s+/', trim($translatedText));

        if (empty($translatedSentences)) {
            return $translatedText;
        }

        $restoredParagraphs = [];
        $sentenceIndex = 0;
        $totalSentences = count($translatedSentences);

        foreach ($originalParagraphs as $originalParagraph) {
            $originalParagraph = trim($originalParagraph);

            if (empty($originalParagraph)) {
                continue;
            }

            // Estimate sentences per paragraph
            $originalSentenceCount = preg_match_all('/[.!?]/', $originalParagraph);
            $estimatedSentences = max(1, $originalSentenceCount);

            // Take sentences from translated text
            $paragraphSentences = [];
            for ($i = 0; $i < $estimatedSentences && $sentenceIndex < $totalSentences; $i++) {
                $paragraphSentences[] = trim($translatedSentences[$sentenceIndex++]);
            }

            if (!empty($paragraphSentences)) {
                $restoredParagraphs[] = implode(' ', $paragraphSentences);
            }
        }

        // Add any remaining sentences as a final paragraph
        if ($sentenceIndex < $totalSentences) {
            $remainingSentences = array_slice($translatedSentences, $sentenceIndex);
            $restoredParagraphs[] = implode(' ', $remainingSentences);
        }

        $restored = implode("\n\n", $restoredParagraphs);
        file_put_contents('debug.log', "DeepSeekTranslationService: Paragraph restoration attempted\n", FILE_APPEND);

        return $restored;
    }

    /**
     * Restore punctuation symbols that might have been converted by the API
     */
    private function restorePunctuationSymbols(string $originalText, string $translatedText): string {
        // Enhanced restoration with better symbol mapping and position tracking
        $restoredText = $translatedText;

        // Enhanced restoration with strict symbol preservation - no cross-conversions allowed
        // Each punctuation type should only be restored from English equivalents, never from other Japanese punctuation

        // General symbol restoration map - CRITICAL: No cross-conversions between Japanese punctuation types
        $punctuationMap = [
            '「' => ['"', "'", '"', '"'],
            '」' => ['"', "'", '"', '"'],
            '『' => ['"', "'", '"', '"'],  // Removed '「' to prevent cross-conversion
            '』' => ['"', "'", '"', '"'],  // Removed '」' to prevent cross-conversion
            '【' => ['['],
            '】' => [']'],
            '〖' => ['['],
            '〗' => [']'],
            '〔' => ['['],
            '〕' => [']'],
            '〈' => ['<'],
            '〉' => ['>'],
            '《' => ['"', '"', '"'],
            '》' => ['"', '"', '"'],
            '（' => ['('],
            '）' => [')'],
            '〜' => ['~'],
            '～' => ['~']
        ];

        // Fix reversed dialogue punctuation first (」...「 pattern)
        $translatedText = $this->fixReversedDialoguePunctuation($originalText, $translatedText);

        // Find all original punctuation symbols and their positions
        $originalSymbols = [];
        foreach ($punctuationMap as $original => $possibleConverted) {
            $offset = 0;
            while (($pos = mb_strpos($originalText, $original, $offset)) !== false) {
                $originalSymbols[] = [
                    'symbol' => $original,
                    'position' => $pos,
                    'possible_converted' => $possibleConverted
                ];
                $offset = $pos + 1;
            }
        }

        // Sort by position to maintain order
        usort($originalSymbols, function($a, $b) {
            return $a['position'] - $b['position'];
        });

        // Restore symbols by looking for converted equivalents
        foreach ($originalSymbols as $symbolInfo) {
            $original = $symbolInfo['symbol'];
            $possibleConverted = $symbolInfo['possible_converted'];

            // Check if original symbol is already present
            if (mb_substr_count($restoredText, $original) >= mb_substr_count($originalText, $original)) {
                continue; // Already preserved correctly
            }

            // Try to find and replace converted symbols
            foreach ($possibleConverted as $converted) {
                $pos = mb_strpos($restoredText, $converted);
                if ($pos !== false) {
                    $restoredText = mb_substr($restoredText, 0, $pos) . $original . mb_substr($restoredText, $pos + mb_strlen($converted));
                    file_put_contents('debug.log', "DeepSeekTranslationService: Restored $converted → $original at position $pos\n", FILE_APPEND);
                    break; // Only replace one occurrence per original symbol
                }
            }
        }

        return $restoredText;
    }

    /**
     * Fix reversed dialogue punctuation patterns (」...「 should be 「...」)
     */
    private function fixReversedDialoguePunctuation(string $originalText, string $translatedText): string {
        $fixedText = $translatedText;

        // Count original dialogue patterns to understand the expected structure
        $originalOpenCount = mb_substr_count($originalText, '「');
        $originalCloseCount = mb_substr_count($originalText, '」');

        // Fix reversed single quote patterns: 」...「 should become 「...」
        $fixedText = preg_replace_callback('/」([^」「]*?)「/u', function($matches) {
            $content = $matches[1];
            return '「' . $content . '」';
        }, $fixedText);

        // Fix reversed double quote patterns: 』...『 should become 『...』
        $fixedText = preg_replace_callback('/』([^』『]*?)『/u', function($matches) {
            $content = $matches[1];
            return '『' . $content . '』';
        }, $fixedText);

        // Fix standalone reversed punctuation at the beginning/end of dialogue
        // Pattern: 」text should become 「text」 if it's at the start of a line or after whitespace
        $fixedText = preg_replace('/(\s|^)」([^」「]+?)(\s|$)/u', '$1「$2」$3', $fixedText);

        // Pattern: text「 should become 「text」 if it's at the end of a line or before whitespace
        $fixedText = preg_replace('/(\s|^)([^」「]+?)「(\s|$)/u', '$1「$2」$3', $fixedText);

        return $fixedText;
    }
}
